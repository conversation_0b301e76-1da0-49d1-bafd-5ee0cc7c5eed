<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-16 23:49:33
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-16 23:52:49
 * @FilePath     : /需求/列表.md
 * @Description  : 财务记录表UI分析
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-16 23:49:33
-->

# 财务记录表 UI 分析

## 设计图分析

### 【重点分析】元素布局与排列方式

设计图展示了一个财务记录表格，采用了清晰的表格布局结构：

- 表格使用了标准的行列结构，包含表头和数据行
- 整体布局采用网格式排列，每个单元格对齐且等高
- 表头固定在顶部，数据行在下方垂直排列
- 第一列为时间周期，采用可展开/折叠的树形结构
- 右侧操作列固定，包含操作按钮
- 表格底部包含分页控件，居中对齐

### 【重点分析】具体尺寸、间距和比例关系

- 表格宽度：100%容器宽度
- 行高：约 50px（表头和数据行统一）
- 单元格内边距：水平约 16px，垂直约 12px
- 列宽分配：
  - 周期列：约 20%
  - 收入列：约 10%
  - 支出列（5 个子列）：每列约 10%
  - 结余列（2 个子列）：每列约 10%
  - 操作列：约 8%
- 展开/折叠图标与文本间距：约 8px
- 分页控件高度：约 40px，按钮间距约 8px

### 【重点分析】颜色值、渐变和阴影参数

- 表格边框：#E5E6EB，1px 实线
- 表头背景色：#F2F3F5
- 数据行背景色：#FFFFFF
- 展开行背景色：略微加深的白色，约#F7F8FA
- 文本颜色：
  - 表头文字：#1D2129（深灰）
  - 普通数据：#1D2129（深灰）
  - 正数金额：#00B42A（绿色）
  - 负数金额：#F53F3F（红色）
- 操作按钮：
  - 图标颜色：#4E5969（中灰）
  - 悬停颜色：#165DFF（蓝色）
- 分页按钮：
  - 激活状态：#165DFF（蓝色背景），白色文字
  - 普通状态：白色背景，#4E5969（中灰）文字
  - 边框：#E5E6EB，1px 实线

### 【重点分析】文本样式与处理方式

- 表头文字：
  - 字体：系统默认无衬线字体
  - 字号：14px
  - 字重：600（semi-bold）
  - 对齐：居中对齐
- 数据文字：
  - 字体：系统默认无衬线字体
  - 字号：14px
  - 字重：400（regular）
  - 对齐：数字右对齐，文本左对齐
- 金额数字：
  - 正数前缀：+
  - 负数前缀：-
  - 保留小数点后 2 位
- 周期文本处理：
  - 折叠状态显示"至"连接两个日期
  - 展开状态仅显示单个日期周期

## 现有代码分析

根据设计图与当前实现对比，主要存在以下差异：

1. 表格边框样式可能不一致，设计图使用了更细腻的边框
2. 金额数字的颜色处理可能不完全匹配（正负数颜色区分）
3. 展开/折叠行的背景色处理可能不一致
4. 操作列按钮的样式和间距可能需要调整
5. 分页控件的样式可能与设计图不完全一致

## 确认请求

请确认以上分析和 SCSS 方案是否符合设计图要求，如有需要调整的地方请指出，我将根据您的反馈进行修改。

## 实现说明

1. 表格 使用 element-plus 中的 table 实现
2. 使用 CSS 变量可以进一步优化代码，便于主题定制
3. 金额正负值使用不同颜色区分，增强可读性
4. 展开/折叠行使用轻微的背景色差异和图标旋转动画提升交互体验
5. 分页控件使用 flex 布局确保居中对齐和一致的间距
6. 所有过渡效果使用适当的动画时间，提升用户体验但不过分引人注目
