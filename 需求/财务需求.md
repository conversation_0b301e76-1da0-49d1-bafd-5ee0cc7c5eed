<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-14 22:41:18
 * @LastEditors  : Bruce
 * @LastEditTime : 2025-07-16 23:15:36
 * @FilePath     : /tools-apps/cursor/roles/产品/需求.md
 * @Description  : 个人资金流向管理系统需求
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-14 22:41:18
-->

# 个人资金流向管理系统需求

## 背景描述

我需要开发一个记录个人资金流向情况的系统。我的工资是每月 15 号发放，所以我会在每月 14 号记录上月的资金流向情况。例如：今天是 7-15 号，那么我就会记录 6-15 至 7-14 之间的资金流向情况。

我一般不关注具体消费在哪些类别（如购物、电影、水电费等），而是更关注资金在不同账户间的流转情况。我的资金账户主要包括：

- 银行卡（用于收取工资、支付房租房贷、充值微信等）
- 信用卡（如招行信用卡、交通银行信用卡等）
- 其他信用账户（如花呗、京东白条等）

我需要记录每月 14 号各账户的余额情况，15 号工资入账后的余额，以及还完各类信用账户后的剩余资金情况。

## 系统功能设计

### 核心功能

- **资金流转记录表（核心功能）**

  - 添加按钮, 可以添加一个周期内的收入支出情况，再里面可以添加微信，京东白条，花呗，招商信用卡， 等等这些金额
  - 按时间顺序记录所有资金流转
  - 记录每笔交易的日期、金额、来源/去向账户
  - 实时计算并显示各账户余额变化
  - 支持按日期、账户、金额等筛选查询
  - 列表的列分别有<京东白条，花呗，微信，交通信用卡，招商信用卡，房租，房贷... 等等>
  - 点击进入编辑页面，可以修改金额

- **自定义记账周期**

  - 默认设置为每月 15 号至下月 14 号
  - 支持查看按周期汇总的资金流向报告

- **多元化账户管理**

  - 现金账户（银行卡、微信、支付宝等）：记录直接收支
  - 信用账户（信用卡、花呗、京东白条等）：记录信用消费和还款
  - 支持添加/编辑/删除各类账户

- **资金流转记录**

  - 收入记录（如工资入账）
  - 支出记录（从各账户支出）
  - 转账记录（账户间资金流转）
  - 还款记录（信用账户还款）

- **财务状况追踪**

  - 14 号记录各账户余额
  - 15 号记录工资入账
  - 还款后剩余资金计算
  - 资金流向可视化

- **数据分析与报表**
  - 周期资金流向分析
  - 账户余额变化趋势
  - 信用额度使用情况
  - 资产负债状况分析
  - 周期差异化对比分析
    - 支持选择两个不同周期进行对比
    - 使用图表直观展示每笔收入和支出的差异
    - 按账户类型展示差异对比
    - 提供收支变化百分比和金额差异

## 用户场景

### 场景一：周期开始（15 号）

1. 记录工资收入进入银行卡
2. 查看各账户当前余额
3. 规划本周期内信用账户使用额度

### 场景二：日常记账

1. 记录从银行卡支付房租、房贷
2. 记录使用信用卡、花呗等进行的消费
3. 记录从银行卡向微信充值的转账

### 场景三：周期结束（14 号）

1. 记录各账户当前余额
2. 统计本周期内各账户资金变化
3. 计算总资产和负债情况
4. 规划下一周期的还款计划

## 系统架构

### 数据模型

1. **账户实体**

   - 账户 ID、账户名称
   - 账户类型（现金/信用）
   - 当前余额/可用额度
   - 账单日/还款日（信用账户）

2. **交易记录实体**

   - 交易类型（收入/支出/转账/还款）
   - 交易金额、交易日期
   - 来源账户、目标账户
   - 交易说明（可选）

3. **周期记录实体**
   - 周期起止日期
   - 期初各账户余额
   - 期末各账户余额
   - 周期总收入/总支出

### 主要功能模块

1. **资金流转记录模块（核心模块）**

   - 按时间顺序展示所有资金流转记录
   - 清晰显示每笔交易涉及的账户和金额
   - 实时更新各账户余额
   - 支持添加、编辑、删除交易记录
   - 每一行是一个周期, 比如 6-15 至 7-14
   - 提供多种筛选和搜索功能

2. **账户管理模块**

   - 添加/编辑/删除账户
   - 设置账户类型和初始余额
   - 账户状态监控

3. **报表分析模块**

   - 周期资金流向报表
   - 账户余额变化趋势图
   - 资产负债状况分析
   - 周期对比分析
     - 支持选择两个或多个周期进行对比
     - 生成收支差异对比图表
     - 显示账户余额变化对比

4. **系统设置模块**
   - 周期设置
   - 数据导入/导出
   - 提醒设置

## 界面设计要求

1. **资金流转记录页面（主页面）**

   - 类似 Excel 表格的界面，按时间顺序显示所有交易
   - 清晰展示每笔交易的日期、金额、账户信息
   - 实时显示各账户余额变化
   - 提供筛选、搜索和排序功能
   - 快速添加新交易的入口

2. **总览页面**

   - 当前周期概况
   - 各账户余额一览
   - 近期重要资金流转
   - 待还款提醒

3. **账户管理页面**

   - 账户列表及详情
   - 账户余额/额度展示
   - 账户操作入口

4. **报表分析页面**

   - 周期选择器
   - 资金流向可视化图表
   - 账户余额变化趋势图
   - 财务状况摘要
   - 周期对比分析功能
     - 多周期选择器（支持选择两个周期进行对比）
     - 对比图表展示区域（柱状图、折线图等多种图表形式）
     - 收支差异明细表格
     - 差异百分比和金额变化指标

5. **设置页面**
   - 周期设置选项
   - 账户类型管理
   - 数据备份与恢复
   - 系统偏好设置

## 非功能需求

1. **数据安全**

   - 敏感财务数据加密存储
   - 支持数据备份与恢复

2. **用户体验**

   - 简洁直观的界面设计
   - 快速录入交易的便捷操作
   - 响应式设计，支持不同设备

3. **性能要求**
   - 快速加载历史交易记录
   - 实时计算账户余额
   - 高效生成报表和图表

## 开发优先级

1. **首要功能（MVP）**

   - 资金流转记录表（核心功能）
   - 账户管理
   - 周期设置
   - 余额统计

2. **次要功能**

   - 报表分析
   - 资金流向可视化
   - 定期提醒

3. **后续迭代**
   - 数据导入/导出
   - 高级分析功能
   - 多设备同步
   - 周期差异化对比分析
