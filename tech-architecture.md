# 本地账号管理系统 - 技术架构文档

## 项目概述

基于 Tauri + Vue 3 + Arco Design Vue 构建的本地账号管理系统，提供安全、高效的密码管理解决方案。

## 技术栈选型

### 核心框架

- **Tauri 2.x** - 跨平台桌面应用框架
- **Vue 3** - 渐进式前端框架
- **TypeScript** - 类型安全的 JavaScript
- **Arco Design Vue** - 企业级 Vue UI 组件库
- **SCSS** - CSS 预处理器，提供嵌套、变量、混入等功能

### 构建工具

- **Vite** - 现代化构建工具，内置 SCSS 支持
- **Rust** - Tauri 后端语言
- **pnpm** - 包管理器

### 数据存储

- **SQLite** - 本地数据库
- **SQLCipher** - 数据库加密
- **Diesel ORM** - Rust 数据库 ORM

### 安全加密

- **AES-256-GCM** - 数据加密算法
- **Argon2** - 密码哈希算法
- **ring** - Rust 加密库

## 系统架构

### 整体架构图

```
┌─────────────────────────────────────────────────────────┐
│                    前端层 (Vue 3)                       │
├─────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │ 账号管理页面 │ │ 设置页面    │ │ 搜索页面    │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
│  ┌─────────────────────────────────────────────────────┐ │
│  │            Arco Design Vue 组件库                   │ │
│  └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│                  Tauri IPC 通信层                       │
├─────────────────────────────────────────────────────────┤
│                   后端层 (Rust)                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │ 账号服务    │ │ 加密服务    │ │ 文件服务    │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
├─────────────────────────────────────────────────────────┤
│                  数据持久层                             │
│  ┌─────────────────────────────────────────────────────┐ │
│  │              SQLite + SQLCipher                     │ │
│  └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

### 前端架构

#### 页面结构

```
src/
├── components/           # 公共组件
│   ├── AppLayout.vue    # 布局组件
│   ├── AccountCard.vue  # 账号卡片
│   └── SearchBar.vue    # 搜索栏
├── views/               # 页面组件
│   ├── Login.vue        # 登录页面
│   ├── Dashboard.vue    # 仪表板
│   ├── Accounts.vue     # 账号列表
│   ├── AccountAdd.vue   # 添加账号页面
│   ├── AccountDetail.vue # 账号详情页面（查看/编辑）
│   ├── Categories.vue   # 分类管理
│   └── Settings.vue     # 设置页面
├── composables/         # 组合式函数
│   ├── useAccounts.ts   # 账号管理
│   ├── useEncryption.ts # 加密操作
│   └── useTheme.ts      # 主题管理
├── stores/              # Pinia 状态管理
│   ├── auth.ts          # 认证状态
│   └── accounts.ts      # 账号状态
├── router/              # Vue Router 路由
│   └── index.ts         # 路由配置
├── types/               # TypeScript类型
│   ├── account.ts
│   ├── api.ts
│   └── common.ts
├── utils/               # 工具函数
│   ├── validation.ts    # 表单验证
│   ├── export.ts        # 数据导出
│   └── constants.ts     # 常量定义
└── styles/              # 样式文件
    ├── main.scss        # 主样式文件
    ├── variables.scss   # SCSS 变量定义
    ├── mixins.scss      # SCSS 混入函数
    ├── components.scss  # 组件样式
    └── arco-theme.scss  # Arco Design主题定制
```

#### 核心组件设计

**核心组件设计**

- **主布局组件 (AppLayout.vue)**: 侧边栏导航 + 主内容区域布局
- **账号卡片组件 (AccountCard.vue)**: 展示账号信息，支持复制和操作菜单
- **表单组件**: 账号创建/编辑表单，集成密码生成器
- **搜索栏组件**: 支持多条件筛选和实时搜索

#### 状态管理 (Pinia)

- **认证状态 (auth.ts)**: 主密码验证、应用锁定状态管理
- **账号状态 (accounts.ts)**: 账号数据管理、CRUD 操作和密码解密
- **设置状态**: 应用配置、主题、安全设置等

#### 路由配置

使用 Vue Router 配置单页面应用路由，包含认证守卫：

- 登录页面 (`/login`)
- 仪表板 (`/dashboard`)
- 账号管理 (`/accounts`)
- 分类管理 (`/categories`)
- 设置页面 (`/settings`)

```

#### SCSS 样式系统

**SCSS 样式系统架构**

- **设计令牌系统**: 统一的颜色、间距、字体、圆角等设计变量
- **混入函数库**: 常用的布局、动画、响应式等 SCSS 混入
```

- **通用组件样式**: 表单、表格、模态框等组件的基础样式
- **Arco Design 主题定制**: 覆盖组件库默认样式，保持设计一致性

**SCSS 使用示例**

```scss
<style lang="scss" scoped>
@import "@/styles/variables.scss";
@import "@/styles/mixins.scss";

.account-card {
  @include card-shadow;
  @include smooth-transition(transform, box-shadow);

  &:hover {
    transform: translateY(-2px);
  }

  @include mobile {
    padding: $spacing-sm;
  }
}
</style>
```

### 后端架构 (Rust)

#### 目录结构

```
src-tauri/
├── src/
│   ├── main.rs          # 程序入口
│   ├── lib.rs           # 库入口
│   ├── commands/        # Tauri命令
│   │   ├── mod.rs
│   │   ├── account.rs   # 账号相关命令
│   │   ├── encryption.rs # 加密相关命令
│   │   └── file.rs      # 文件相关命令
│   ├── models/          # 数据模型
│   │   ├── mod.rs
│   │   ├── account.rs
│   │   └── category.rs
│   ├── services/        # 业务逻辑
│   │   ├── mod.rs
│   │   ├── account_service.rs
│   │   ├── encryption_service.rs
│   │   └── database_service.rs
│   ├── utils/           # 工具函数
│   │   ├── mod.rs
│   │   ├── crypto.rs    # 加密工具
│   │   └── validation.rs # 验证工具
│   └── errors/          # 错误处理
│       ├── mod.rs
│       └── app_error.rs
├── migrations/          # 数据库迁移
├── Cargo.toml
└── tauri.conf.json
```

#### 核心服务实现

- **账号服务**: 账号的 CRUD 操作、数据加密解密
- **加密服务**: AES-256-GCM 数据加密和 Argon2 密码哈希
- **数据库服务**: SQLite 数据库连接和操作管理
- **文件服务**: 数据导入导出、备份恢复功能

### 数据库设计

#### 表结构

```sql
-- 账号表
CREATE TABLE accounts (
    id TEXT PRIMARY KEY,
    platform TEXT NOT NULL,
    username TEXT NOT NULL,
    encrypted_password BLOB NOT NULL,
    email TEXT,
    phone TEXT,
    website_url TEXT,
    category_id TEXT,
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id)
);

-- 分类表
CREATE TABLE categories (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    color TEXT,
    icon TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 设置表
CREATE TABLE settings (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 备份记录表
CREATE TABLE backup_records (
    id TEXT PRIMARY KEY,
    file_path TEXT NOT NULL,
    backup_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    file_size INTEGER
);
```

## 安全架构

### 数据加密流程

```
用户输入主密码 → Argon2哈希验证 → 生成会话密钥 → AES-256-GCM加密数据 → 存储到SQLCipher数据库
```

### 密钥管理

- **主密码**: 用户设置的主密码，使用 Argon2 进行哈希验证
- **数据库密钥**: 基于主密码派生的数据库加密密钥
- **会话密钥**: 运行时内存中的临时加密密钥
- **盐值**: 每个加密操作使用随机盐值

### 安全措施

1. **内存保护**: 敏感数据在内存中及时清零
2. **自动锁定**: 闲置时间超过设定值自动锁定
3. **数据库加密**: SQLCipher 提供透明数据库加密
4. **输入验证**: 严格的输入验证和消毒
5. **错误处理**: 不泄露敏感信息的错误处理

## 性能优化

### 前端优化

- **虚拟滚动**: 大量账号数据的高效渲染
- **懒加载**: 按需加载组件和数据
- **缓存策略**: 合理的数据缓存机制
- **防抖搜索**: 搜索输入防抖处理
- **Composition API**: 更好的逻辑复用和性能
- **SCSS 优化**:
  - 模块化样式结构，避免样式冲突
  - 使用 CSS 变量和 SCSS 变量结合，支持主题切换
  - 利用 `@import` 和 Vite 的自动导入减少重复代码
  - 响应式混入提高移动端性能
  - 使用 `scoped` 样式避免全局污染

### 后端优化

- **数据库索引**: 关键字段建立索引
- **连接池**: 数据库连接池管理
- **批量操作**: 批量数据处理优化
- **内存管理**: Rust 零成本抽象的内存安全

## 部署架构

### 构建流程

```bash
# 安装依赖（包括 SCSS 相关）
pnpm install

# 开发模式（支持 SCSS 热重载）
pnpm dev

# 前端构建（自动编译 SCSS）
pnpm build

# Tauri构建
cargo tauri build
```

**Vite 配置 (vite.config.ts)**

```typescript
import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import { resolve } from "path";

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      "@": resolve(__dirname, "src"),
    },
  },
  css: {
    preprocessorOptions: {
      scss: {
        // 自动导入全局 SCSS 变量和混入
        additionalData: `
          @import "@/styles/variables.scss";
          @import "@/styles/mixins.scss";
        `,
      },
    },
  },
  // Tauri 相关配置
  clearScreen: false,
  server: {
    port: 1420,
    strictPort: true,
  },
  envPrefix: ["VITE_", "TAURI_"],
});
```

**Package.json 依赖**

```json
{
  "devDependencies": {
    "@vitejs/plugin-vue": "^4.0.0",
    "sass": "^1.57.0",
    "vite": "^4.0.0",
    "typescript": "^4.9.0"
  }
}
```

### 发布包结构

```
dist/
├── AccountManager_1.0.0_x64.msi     # Windows安装包
├── AccountManager_1.0.0_x64.dmg     # macOS安装包
├── AccountManager_1.0.0_amd64.deb   # Linux安装包
└── AccountManager_1.0.0_amd64.AppImage # Linux便携版
```

### 自动更新机制

- **版本检查**: 启动时检查新版本
- **增量更新**: 支持增量更新减少下载量
- **回滚机制**: 更新失败时自动回滚
- **用户控制**: 用户可选择更新时机

## 开发规范

### 代码规范

- **TypeScript**: 严格模式，完整类型定义
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **SCSS**: Sass 官方风格指南，使用 BEM 命名约定
- **Rust**: Clippy 代码检查
- **Vue 3**: Composition API + `<script setup>`

**SCSS 编码规范**

- **命名约定**: 使用 kebab-case 和 BEM 命名规范
- **代码组织**: 属性按定位、盒模型、视觉效果、动画的顺序排列
- **嵌套规范**: 最多 3 层嵌套，合理使用 `&` 引用父选择器
- **变量使用**: 优先使用设计令牌变量，避免硬编码数值
- **混入函数**: 抽象通用样式模式，提高代码复用性

### Git 工作流

- **主分支**: main (生产环境)
- **开发分支**: dev (开发环境)
- **功能分支**: feature/\* (新功能开发)
- **修复分支**: hotfix/\* (紧急修复)

### 测试策略

- **单元测试**: Vitest + Vue Test Utils
- **集成测试**: Tauri 测试框架
- **E2E 测试**: Playwright
- **安全测试**: 渗透测试和代码审计

## 监控与日志

### 日志系统

- **前端日志**: 用户操作和错误日志
- **后端日志**: 系统运行和安全事件日志
- **日志级别**: Error, Warn, Info, Debug
- **日志轮转**: 自动清理过期日志

### 性能监控

- **启动时间**: 应用启动性能监控
- **内存使用**: 内存使用情况监控
- **数据库性能**: 查询性能监控
- **用户体验**: 界面响应时间监控

## 后续扩展

### 功能扩展

- **云同步**: 可选的云端数据同步
- **团队共享**: 团队账号共享功能
- **移动端**: Vue + Capacitor 移动端应用
- **浏览器扩展**: 浏览器自动填充扩展

### 技术演进

- **Tauri 3.0**: 升级到最新 Tauri 版本
- **Vue 3.5+**: 升级 Vue 版本
- **WASM**: WebAssembly 性能优化
- **AI 集成**: 智能密码分析和建议

---

此技术架构为本地账号管理系统提供了完整的技术实现方案，确保系统的安全性、性能和可维护性。
