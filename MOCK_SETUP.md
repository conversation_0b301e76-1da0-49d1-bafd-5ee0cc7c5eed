# Mock API 设置说明

## 🎯 完成的功能

我已经为您创建了完整的 Mock API 方案，包含以下5个接口：

### 📋 接口列表
1. **财务记录列表** - `GET /finance-records` (支持分页、搜索、排序)
2. **财务记录详情** - `GET /finance-records/{id}`
3. **创建财务记录** - `POST /finance-records`
4. **更新财务记录** - `PUT /finance-records/{id}`
5. **删除财务记录** - `DELETE /finance-records/{id}`

### 🔧 额外功能
- **记账周期查询** - `GET /periods`
- **根据周期查询记录** - `GET /finance-records?period={period}`

## 🚀 如何启动

### 方式1: 分别启动
```bash
# 终端1: 启动 mock 服务器
npm run mock

# 终端2: 启动前端开发服务器
npm run dev
```

### 方式2: 同时启动 (推荐)
```bash
# 需要先安装 concurrently
npm install --save-dev concurrently

# 然后同时启动
npm run dev:full
```

## 📁 文件结构

```
project/
├── mock/
│   └── db.json                 # Mock 数据
├── src/
│   ├── api/
│   │   ├── request.ts          # HTTP 请求工具
│   │   ├── finance.ts          # 财务 API 接口
│   │   └── README.md           # API 文档
│   └── composables/
│       ├── useFinanceForm.ts   # 表单逻辑 (已更新)
│       └── useFinanceList.ts   # 列表逻辑 (新增)
└── package.json                # 添加了 mock 脚本
```

## 🔄 数据流程

### 编辑页面流程
1. 进入编辑页面 → `getFinanceRecord(id)` 获取数据
2. 填充表单 → 用户修改数据
3. 提交表单 → `updateFinanceRecord(id, data)` 更新数据
4. 返回列表页

### 列表页面流程
1. 进入列表页 → `getFinanceList()` 获取列表
2. 搜索/分页 → 重新调用 `getFinanceList(params)`
3. 删除记录 → `deleteFinanceRecord(id)`
4. 新增/编辑 → 跳转到表单页面

## 📊 Mock 数据示例

已预置3条测试数据：
- 2025年1月记录
- 2024年12月记录  
- 2024年11月记录

每条记录包含完整的收入、支出、现金资产数据。

## 🛠️ 集成状态

### ✅ 已完成
- [x] Mock 服务器配置
- [x] API 接口定义
- [x] 请求工具封装
- [x] 表单逻辑更新 (useFinanceForm)
- [x] 列表逻辑创建 (useFinanceList)
- [x] 错误处理
- [x] TypeScript 类型定义

### 🔄 需要更新的页面
1. **列表页面** (`/src/views/finance/List.vue`)
   - 引入 `useFinanceList`
   - 替换模拟数据为真实API调用

2. **表单页面** (`/src/views/finance/RecordForm.vue`)
   - 已更新 `useFinanceForm`，无需修改

## 🎯 下一步

1. **启动服务**:
   ```bash
   npm run mock  # 启动 mock 服务器
   ```

2. **测试接口**:
   - 访问 `http://localhost:3001/finance-records` 查看数据
   - 在浏览器中测试表单提交和编辑功能

3. **更新列表页面**:
   - 使用 `useFinanceList` composable
   - 替换现有的数据加载逻辑

## 🔍 调试

### 查看 Mock 数据
- 直接访问: `http://localhost:3001/finance-records`
- 查看所有接口: `http://localhost:3001`

### 修改 Mock 数据
- 编辑 `mock/db.json` 文件
- json-server 会自动重新加载

### API 调用日志
- 所有 API 调用都会在控制台输出日志
- 错误信息会通过 ElMessage 显示

现在您可以启动 mock 服务器并测试所有的财务管理功能了！🎉
