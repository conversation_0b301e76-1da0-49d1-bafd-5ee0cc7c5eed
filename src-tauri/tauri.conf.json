{"$schema": "https://schema.tauri.app/config/2", "productName": "account-manager", "version": "0.1.0", "identifier": "com.account-manager.app", "build": {"beforeDevCommand": "pnpm dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "pnpm build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "account-manager", "width": 1600, "height": 1000}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}