use aes_gcm::{
    aead::{<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Key<PERSON>nit, OsRng},
    Aes256Gcm, <PERSON>, Nonce,
};
use argon2::{Argon2, PasswordHash, PasswordHasher, PasswordVerifier, password_hash::SaltString};
use base64::{Engine as _, engine::general_purpose};
use anyhow::{Result, anyhow};
use rand::RngCore;

pub struct EncryptionService;

impl EncryptionService {
    /// 使用 Argon2 哈希密码
    pub fn hash_password(password: &str) -> Result<String> {
        let salt = SaltString::generate(&mut OsRng);
        let argon2 = Argon2::default();

        let password_hash = argon2
            .hash_password(password.as_bytes(), &salt)
            .map_err(|e| anyhow!("Failed to hash password: {}", e))?
            .to_string();

        Ok(password_hash)
    }

    /// 验证密码
    pub fn verify_password(password: &str, hash: &str) -> Result<bool> {
        let parsed_hash = PasswordHash::new(hash)
            .map_err(|e| anyhow!("Failed to parse hash: {}", e))?;

        let argon2 = Argon2::default();
        Ok(argon2.verify_password(password.as_bytes(), &parsed_hash).is_ok())
    }

    /// 从密码派生加密密钥
    pub fn derive_key_from_password(password: &str, salt: &[u8]) -> Result<[u8; 32]> {
        let argon2 = Argon2::default();
        let mut key = [0u8; 32];

        argon2.hash_password_into(password.as_bytes(), salt, &mut key)
            .map_err(|e| anyhow!("Failed to derive key: {}", e))?;

        Ok(key)
    }

    /// 加密数据
    pub fn encrypt_data(data: &str, password: &str) -> Result<String> {
        // 生成随机盐
        let mut salt = [0u8; 16];
        OsRng::default().fill_bytes(&mut salt);

        // 从密码派生密钥
        let key_bytes = Self::derive_key_from_password(password, &salt)?;
        let key = Key::<Aes256Gcm>::from_slice(&key_bytes);
        let cipher = Aes256Gcm::new(key);

        // 生成随机 nonce
        let nonce = Aes256Gcm::generate_nonce(&mut OsRng);

        // 加密数据
        let ciphertext = cipher
            .encrypt(&nonce, data.as_bytes())
            .map_err(|e| anyhow!("Encryption failed: {}", e))?;

        // 组合 salt + nonce + ciphertext 并编码为 base64
        let mut result = Vec::new();
        result.extend_from_slice(&salt);
        result.extend_from_slice(&nonce);
        result.extend_from_slice(&ciphertext);

        Ok(general_purpose::STANDARD.encode(result))
    }

    /// 解密数据
    pub fn decrypt_data(encrypted_data: &str, password: &str) -> Result<String> {
        // 解码 base64
        let data = general_purpose::STANDARD
            .decode(encrypted_data)
            .map_err(|e| anyhow!("Failed to decode base64: {}", e))?;

        if data.len() < 16 + 12 {
            return Err(anyhow!("Invalid encrypted data length"));
        }

        // 提取 salt, nonce 和 ciphertext
        let salt = &data[0..16];
        let nonce_bytes = &data[16..28];
        let ciphertext = &data[28..];

        // 从密码派生密钥
        let key_bytes = Self::derive_key_from_password(password, salt)?;
        let key = Key::<Aes256Gcm>::from_slice(&key_bytes);
        let cipher = Aes256Gcm::new(key);

        // 创建 nonce
        let nonce = Nonce::from_slice(nonce_bytes);

        // 解密数据
        let plaintext = cipher
            .decrypt(nonce, ciphertext)
            .map_err(|e| anyhow!("Decryption failed: {}", e))?;

        String::from_utf8(plaintext)
            .map_err(|e| anyhow!("Failed to convert decrypted data to string: {}", e))
    }
}