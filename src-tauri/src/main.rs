/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-22 10:48:52
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-06-29 00:32:31
 * @FilePath     : /src-tauri/src/main.rs
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-22 10:48:52
 */
// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

mod models;
mod encryption;
mod database;
mod commands;

use commands::*;
mod utils;
use utils::AppState;

fn main() {
    tauri::Builder::default()
        .manage(AppState::new())
        .plugin(tauri_plugin_opener::init())
        .invoke_handler(tauri::generate_handler![
            init_database,
            verify_master_password,
            is_master_password_set,
            lock_app,
            check_auth_status,
            create_account,
            get_all_accounts,
            get_account_by_id,
            update_account,
            delete_account,
            batch_delete_accounts,
            search_accounts,
            decrypt_password,
            get_all_categories,
            create_category,
            update_category,
            delete_category,
            get_database_path,
            open_data_folder,
            open_downloads_folder,
            clear_all_data,
            export_data,
            export_data_to_file,
            import_data,
            download_template
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
