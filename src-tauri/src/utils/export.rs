/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-29 01:10:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-06-29 01:10:00
 * @FilePath     : /src-tauri/src/utils/export.rs
 * @Description  : 导出功能工具
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-29 01:10:00
 */

use crate::models::{Account, Category};
use crate::utils::error::CommandResult;
use chrono::Utc;
use serde::Serialize;
use std::path::PathBuf;

/// 标准导出数据结构
#[derive(Serialize)]
pub struct ExportData {
    pub version: String,
    pub export_time: String,
    pub accounts: Vec<Account>,
    pub categories: Vec<Category>,
    pub metadata: ExportMetadata,
}

/// 导出元数据
#[derive(Serialize)]
pub struct ExportMetadata {
    pub total_accounts: usize,
    pub total_categories: usize,
    pub export_format: String,
    pub application: String,
    pub application_version: String,
}

/// 导出相关的工具
pub struct ExportHelper;

impl ExportHelper {

    /// 创建导出数据
    pub fn create_export_data(accounts: Vec<Account>, categories: Vec<Category>) -> ExportData {
        let metadata = ExportMetadata {
            total_accounts: accounts.len(),
            total_categories: categories.len(),
            export_format: "json".to_string(),
            application: "AccountManager".to_string(),
            application_version: env!("CARGO_PKG_VERSION").to_string(),
        };

        ExportData {
            version: "1.0".to_string(),
            export_time: Utc::now().to_rfc3339(),
            accounts,
            categories,
            metadata,
        }
    }

    /// 序列化导出数据为 JSON
    pub fn serialize_to_json(export_data: &ExportData) -> CommandResult<String> {
        serde_json::to_string_pretty(export_data)
            .map_err(|e| format!("Failed to serialize export data: {}", e))
    }

    /// 序列化为紧凑 JSON（不带格式化）
    pub fn serialize_to_json_compact(export_data: &ExportData) -> CommandResult<String> {
        serde_json::to_string(export_data)
            .map_err(|e| format!("Failed to serialize export data: {}", e))
    }

    /// 生成带时间戳的文件名
    pub fn generate_filename(prefix: &str, extension: &str) -> String {
        let timestamp = Utc::now().format("%Y%m%d%H%M%S").to_string();
        format!("{}_{}.{}", prefix, timestamp, extension)
    }

    /// 生成带自定义时间格式的文件名
    pub fn generate_filename_with_format(prefix: &str, extension: &str, time_format: &str) -> String {
        let timestamp = Utc::now().format(time_format).to_string();
        format!("{}_{}.{}", prefix, timestamp, extension)
    }

    /// 获取桌面目录
    pub fn get_desktop_dir() -> CommandResult<PathBuf> {
        if let Some(desktop_path) = dirs::desktop_dir() {
            Ok(desktop_path)
        } else if let Some(home_dir) = dirs::home_dir() {
            Ok(home_dir.join("Desktop"))
        } else {
            Err("无法确定保存位置".to_string())
        }
    }

    /// 获取下载目录
    pub fn get_downloads_dir() -> CommandResult<PathBuf> {
        if let Some(downloads_path) = dirs::download_dir() {
            Ok(downloads_path)
        } else if let Some(home_dir) = dirs::home_dir() {
            Ok(home_dir.join("Downloads"))
        } else {
            Err("无法确定下载目录".to_string())
        }
    }

    /// 获取文档目录
    pub fn get_documents_dir() -> CommandResult<PathBuf> {
        if let Some(documents_path) = dirs::document_dir() {
            Ok(documents_path)
        } else if let Some(home_dir) = dirs::home_dir() {
            Ok(home_dir.join("Documents"))
        } else {
            Err("无法确定文档目录".to_string())
        }
    }

    /// 确保目录存在
    pub fn ensure_dir_exists(path: &PathBuf) -> CommandResult<()> {
        if !path.exists() {
            std::fs::create_dir_all(path)
                .map_err(|e| format!("Failed to create directory '{}': {}", path.display(), e))?;
        }
        Ok(())
    }

    /// 写入文件
    pub fn write_to_file(content: &str, file_path: &PathBuf) -> CommandResult<()> {
        // 确保父目录存在
        if let Some(parent) = file_path.parent() {
            Self::ensure_dir_exists(&parent.to_path_buf())?;
        }

        std::fs::write(file_path, content)
            .map_err(|e| format!("Failed to write file '{}': {}", file_path.display(), e))?;

        Ok(())
    }

    /// 获取文件大小
    pub fn get_file_size(file_path: &PathBuf) -> CommandResult<u64> {
        let metadata = std::fs::metadata(file_path)
            .map_err(|e| format!("Failed to get file metadata '{}': {}", file_path.display(), e))?;
        Ok(metadata.len())
    }

    /// 验证导出数据的完整性
    pub fn validate_export_data(export_data: &ExportData) -> CommandResult<()> {
        // 检查版本
        if export_data.version.is_empty() {
            return Err("Export data version is empty".to_string());
        }

        // 检查导出时间
        chrono::DateTime::parse_from_rfc3339(&export_data.export_time)
            .map_err(|e| format!("Invalid export time format: {}", e))?;

        // 检查元数据一致性
        if export_data.metadata.total_accounts != export_data.accounts.len() {
            return Err("Account count mismatch in metadata".to_string());
        }

        if export_data.metadata.total_categories != export_data.categories.len() {
            return Err("Category count mismatch in metadata".to_string());
        }

        Ok(())
    }

    /// 创建Excel导出模板的标题行
    pub fn get_excel_headers() -> Vec<&'static str> {
        vec![
            "平台", "用户名", "显示名称", "密码", "邮箱",
            "手机号", "网址", "分类", "备注", "创建时间", "更新时间"
        ]
    }

    /// 获取CSV分隔符
    pub fn get_csv_separator() -> char {
        ','
    }

    /// 转义CSV字段
    pub fn escape_csv_field(field: &str) -> String {
        if field.contains(',') || field.contains('"') || field.contains('\n') {
            format!("\"{}\"", field.replace('"', "\"\""))
        } else {
            field.to_string()
        }
    }
}

/// 导入验证工具
pub struct ImportValidator;

impl ImportValidator {
    /// 验证导入的JSON数据
    pub fn validate_import_json(json_str: &str) -> CommandResult<serde_json::Value> {
        let data: serde_json::Value = serde_json::from_str(json_str)
            .map_err(|e| format!("Invalid JSON format: {}", e))?;

        if !data.is_object() {
            return Err("Invalid backup file format: expected object".to_string());
        }

        // 检查必需字段
        if !data.get("accounts").is_some() {
            return Err("Missing accounts data in backup file".to_string());
        }

        if !data.get("categories").is_some() {
            return Err("Missing categories data in backup file".to_string());
        }

        Ok(data)
    }

    /// 验证导入的账号数据
    pub fn validate_account_data(account_data: &serde_json::Value) -> CommandResult<()> {
        if let Some(account_obj) = account_data.as_object() {
            // 检查必需字段
            if !account_obj.contains_key("platform") ||
               !account_obj.contains_key("username") {
                return Err("Account missing required fields (platform, username)".to_string());
            }

            // 验证字段类型
            if !account_obj.get("platform").unwrap().is_string() {
                return Err("Account platform must be string".to_string());
            }

            if !account_obj.get("username").unwrap().is_string() {
                return Err("Account username must be string".to_string());
            }

            Ok(())
        } else {
            Err("Invalid account data format".to_string())
        }
    }

    /// 验证导入数据的大小限制
    pub fn validate_import_size(data_size: usize, max_size: usize) -> CommandResult<()> {
        if data_size > max_size {
            return Err(format!(
                "Import data too large: {} bytes (max: {} bytes)",
                data_size, max_size
            ));
        }
        Ok(())
    }
}