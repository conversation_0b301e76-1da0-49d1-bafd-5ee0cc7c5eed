/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-29 01:10:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-06-29 00:10:21
 * @FilePath     : /src-tauri/src/utils/logging.rs
 * @Description  : 日志记录工具
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-29 01:10:00
 */

use chrono::Utc;

/// 日志记录工具
pub struct LogHelper;

impl LogHelper {
    /// 记录命令开始
    pub fn log_command_start(command_name: &str, details: Option<&str>) {
        let timestamp = Utc::now().format("%H:%M:%S%.3f").to_string();
        match details {
            Some(details) => println!("🚀 [{}] [{}] Starting: {}", timestamp, command_name, details),
            None => println!("🚀 [{}] [{}] Starting", timestamp, command_name),
        }
    }

    /// 记录命令成功
    pub fn log_command_success(command_name: &str, details: Option<&str>) {
        let timestamp = Utc::now().format("%H:%M:%S%.3f").to_string();
        match details {
            Some(details) => println!("✅ [{}] [{}] Success: {}", timestamp, command_name, details),
            None => println!("✅ [{}] [{}] Success", timestamp, command_name),
        }
    }

    /// 记录命令失败
    pub fn log_command_error(command_name: &str, error: &str) {
        let timestamp = Utc::now().format("%H:%M:%S%.3f").to_string();
        println!("❌ [{}] [{}] Error: {}", timestamp, command_name, error);
    }

    /// 记录数据统计
    pub fn log_data_stats(operation: &str, count: usize, item_type: &str) {
        let timestamp = Utc::now().format("%H:%M:%S%.3f").to_string();
        println!("📊 [{}] [{}] Processed {} {}", timestamp, operation, count, item_type);
    }

    /// 记录性能信息
    pub fn log_performance(operation: &str, duration_ms: u128) {
        let timestamp = Utc::now().format("%H:%M:%S%.3f").to_string();
        println!("⏱️  [{}] [{}] Completed in {}ms", timestamp, operation, duration_ms);
    }

    /// 记录警告
    pub fn log_warning(context: &str, message: &str) {
        let timestamp = Utc::now().format("%H:%M:%S%.3f").to_string();
        println!("⚠️  [{}] [{}] Warning: {}", timestamp, context, message);
    }

    /// 记录信息
    pub fn log_info(context: &str, message: &str) {
        let timestamp = Utc::now().format("%H:%M:%S%.3f").to_string();
        println!("ℹ️  [{}] [{}] {}", timestamp, context, message);
    }

    /// 记录调试信息（仅在调试模式下）
    pub fn log_debug(context: &str, message: &str) {
        #[cfg(debug_assertions)]
        {
            let timestamp = Utc::now().format("%H:%M:%S%.3f").to_string();
            println!("🔍 [{}] [{}] Debug: {}", timestamp, context, message);
        }
        #[cfg(not(debug_assertions))]
        {
            let _ = (context, message); // 避免未使用变量警告
        }
    }

    /// 记录数据库操作
    pub fn log_db_operation(operation: &str, table: &str, affected_rows: Option<u64>) {
        let timestamp = Utc::now().format("%H:%M:%S%.3f").to_string();
        match affected_rows {
            Some(rows) => println!("🗄️  [{}] DB {} on {}: {} rows affected", timestamp, operation, table, rows),
            None => println!("🗄️  [{}] DB {} on {}", timestamp, operation, table),
        }
    }

    /// 记录文件操作
    pub fn log_file_operation(operation: &str, file_path: &str, size: Option<u64>) {
        let timestamp = Utc::now().format("%H:%M:%S%.3f").to_string();
        match size {
            Some(size) => println!("📁 [{}] File {} '{}': {} bytes", timestamp, operation, file_path, size),
            None => println!("📁 [{}] File {} '{}'", timestamp, operation, file_path),
        }
    }

    /// 记录网络操作
    pub fn log_network_operation(operation: &str, endpoint: &str, status: Option<u16>) {
        let timestamp = Utc::now().format("%H:%M:%S%.3f").to_string();
        match status {
            Some(status) => println!("🌐 [{}] Network {} to '{}': status {}", timestamp, operation, endpoint, status),
            None => println!("🌐 [{}] Network {} to '{}'", timestamp, operation, endpoint),
        }
    }
}

/// 性能测量工具
pub struct PerformanceLogger {
    operation: String,
    start_time: std::time::Instant,
}

impl PerformanceLogger {
    /// 开始性能测量
    pub fn start(operation: &str) -> Self {
        LogHelper::log_command_start(operation, None);
        Self {
            operation: operation.to_string(),
            start_time: std::time::Instant::now(),
        }
    }

    /// 结束性能测量并记录
    pub fn finish(self) {
        let duration = self.start_time.elapsed();
        LogHelper::log_performance(&self.operation, duration.as_millis());
        LogHelper::log_command_success(&self.operation, Some(&format!("Completed in {}ms", duration.as_millis())));
    }

    /// 结束性能测量并记录（带自定义消息）
    pub fn finish_with_message(self, message: &str) {
        let duration = self.start_time.elapsed();
        LogHelper::log_performance(&self.operation, duration.as_millis());
        LogHelper::log_command_success(&self.operation, Some(&format!("{} ({}ms)", message, duration.as_millis())));
    }

    /// 记录中间检查点
    pub fn checkpoint(&self, checkpoint_name: &str) {
        let duration = self.start_time.elapsed();
        LogHelper::log_info(&self.operation, &format!("Checkpoint '{}' at {}ms", checkpoint_name, duration.as_millis()));
    }
}

/// 宏：简化性能日志记录
#[macro_export]
macro_rules! log_performance {
    ($operation:expr, $code:block) => {{
        let _perf_logger = crate::utils::logging::PerformanceLogger::start($operation);
        let result = $code;
        _perf_logger.finish();
        result
    }};
}

/// 宏：带错误处理的性能日志记录
#[macro_export]
macro_rules! log_performance_result {
    ($operation:expr, $code:block) => {{
        let perf_logger = crate::utils::logging::PerformanceLogger::start($operation);
        let result = $code;
        match &result {
            Ok(_) => perf_logger.finish_with_message("Success"),
            Err(e) => {
                crate::utils::logging::LogHelper::log_command_error($operation, &e.to_string());
                perf_logger.finish_with_message("Failed");
            }
        }
        result
    }};
}