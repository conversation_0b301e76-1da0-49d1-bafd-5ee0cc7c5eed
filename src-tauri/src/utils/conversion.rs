/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-29 01:10:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-06-29 00:37:11
 * @FilePath     : /src-tauri/src/utils/conversion.rs
 * @Description  : 数据转换工具
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-29 01:10:00
 */

use crate::utils::error::CommandResult;
use chrono::{DateTime, Utc};
use serde_json::Value;
use std::collections::HashMap;

/// 数据转换工具
pub struct ConversionHelper;

impl ConversionHelper {
    /// 安全地转换可选字符串
    pub fn safe_optional_string(value: Option<String>) -> Option<String> {
        value.and_then(|s| {
            let trimmed = s.trim();
            if trimmed.is_empty() {
                None
            } else {
                Some(trimmed.to_string())
            }
        })
    }

    /// 安全地转换字符串到可选字符串
    pub fn string_to_optional(value: &str) -> Option<String> {
        let trimmed = value.trim();
        if trimmed.is_empty() {
            None
        } else {
            Some(trimmed.to_string())
        }
    }

    /// HashMap to JSON 字符串
    pub fn hashmap_to_json(map: &HashMap<String, String>) -> CommandResult<String> {
        serde_json::to_string(map).map_err(|e| format!("Failed to serialize HashMap: {}", e))
    }

    /// JSON 字符串 to HashMap
    pub fn json_to_hashmap(json: &str) -> CommandResult<HashMap<String, String>> {
        if json.trim().is_empty() {
            return Ok(HashMap::new());
        }

        serde_json::from_str(json).map_err(|e| format!("Failed to deserialize JSON: {}", e))
    }

    /// 安全地从JSON值中获取字符串
    pub fn json_value_to_string(value: &Value) -> Option<String> {
        match value {
            Value::String(s) => Self::string_to_optional(s),
            Value::Number(n) => Some(n.to_string()),
            Value::Bool(b) => Some(b.to_string()),
            _ => None,
        }
    }

    /// 安全地从JSON值中获取可选字符串
    pub fn json_value_to_optional_string(value: Option<&Value>) -> Option<String> {
        value.and_then(Self::json_value_to_string)
    }

    /// 字符串转换为布尔值
    pub fn string_to_bool(value: &str) -> bool {
        match value.to_lowercase().as_str() {
            "true" | "1" | "yes" | "on" | "enabled" => true,
            _ => false,
        }
    }

    /// 布尔值转换为字符串
    pub fn bool_to_string(value: bool) -> String {
        if value { "true" } else { "false" }.to_string()
    }

    /// 安全地解析整数
    pub fn safe_parse_i64(value: &str) -> Option<i64> {
        value.trim().parse().ok()
    }

    /// 安全地解析浮点数
    pub fn safe_parse_f64(value: &str) -> Option<f64> {
        value.trim().parse().ok()
    }

    /// 时间戳转换为DateTime
    pub fn timestamp_to_datetime(timestamp: i64) -> CommandResult<DateTime<Utc>> {
        DateTime::from_timestamp(timestamp, 0)
            .ok_or("Invalid timestamp".to_string())
    }

    /// DateTime转换为时间戳
    pub fn datetime_to_timestamp(datetime: &DateTime<Utc>) -> i64 {
        datetime.timestamp()
    }

    /// 格式化文件大小
    pub fn format_file_size(bytes: u64) -> String {
        const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
        let mut size = bytes as f64;
        let mut unit_index = 0;

        while size >= 1024.0 && unit_index < UNITS.len() - 1 {
            size /= 1024.0;
            unit_index += 1;
        }

        if unit_index == 0 {
            format!("{} {}", bytes, UNITS[unit_index])
        } else {
            format!("{:.2} {}", size, UNITS[unit_index])
        }
    }

    /// 解析文件大小字符串
    pub fn parse_file_size(size_str: &str) -> CommandResult<u64> {
        let size_str = size_str.trim().to_uppercase();

        let (number_part, unit_part) = if let Some(pos) = size_str.find(char::is_alphabetic) {
            (&size_str[..pos], &size_str[pos..])
        } else {
            (size_str.as_str(), "B")
        };

        let number: f64 = number_part.trim().parse()
            .map_err(|_| format!("Invalid number format: {}", number_part))?;

        let multiplier = match unit_part.trim() {
            "B" | "" => 1,
            "KB" | "K" => 1024,
            "MB" | "M" => 1024 * 1024,
            "GB" | "G" => 1024 * 1024 * 1024,
            "TB" | "T" => 1024_u64.pow(4),
            _ => return Err(format!("Unknown unit: {}", unit_part)),
        };

        Ok((number * multiplier as f64) as u64)
    }

    /// 字符串列表转换为逗号分隔的字符串
    pub fn vec_to_comma_separated(vec: &[String]) -> String {
        vec.join(", ")
    }

    /// 逗号分隔的字符串转换为字符串列表
    pub fn comma_separated_to_vec(csv: &str) -> Vec<String> {
        csv.split(',')
            .map(|s| s.trim().to_string())
            .filter(|s| !s.is_empty())
            .collect()
    }

    /// 转换URL为域名
    pub fn url_to_domain(url: &str) -> Option<String> {
        if url.trim().is_empty() {
            return None;
        }

        // 如果没有协议，添加默认协议
        let url_with_protocol = if !url.contains("://") {
            format!("https://{}", url)
        } else {
            url.to_string()
        };

        // 解析URL
        if let Ok(parsed_url) = url::Url::parse(&url_with_protocol) {
            parsed_url.host_str().map(|host| {
                // 移除 www. 前缀
                if host.starts_with("www.") {
                    host[4..].to_string()
                } else {
                    host.to_string()
                }
            })
        } else {
            None
        }
    }

    /// 标准化URL格式
    pub fn normalize_url(url: &str) -> Option<String> {
        if url.trim().is_empty() {
            return None;
        }

        let trimmed = url.trim();

        // 如果没有协议，添加https://
        let url_with_protocol = if !trimmed.contains("://") {
            format!("https://{}", trimmed)
        } else {
            trimmed.to_string()
        };

        // 验证并标准化URL
        if let Ok(parsed_url) = url::Url::parse(&url_with_protocol) {
            Some(parsed_url.to_string())
        } else {
            None
        }
    }

    /// 转换颜色格式（十六进制）
    pub fn normalize_color(color: &str) -> Option<String> {
        let color = color.trim();

        if color.is_empty() {
            return None;
        }

        // 确保以#开头
        let color_with_hash = if color.starts_with('#') {
            color.to_string()
        } else {
            format!("#{}", color)
        };

        // 验证颜色格式
        if color_with_hash.len() == 7 && color_with_hash.chars().skip(1).all(|c| c.is_ascii_hexdigit()) {
            Some(color_with_hash.to_uppercase())
        } else if color_with_hash.len() == 4 && color_with_hash.chars().skip(1).all(|c| c.is_ascii_hexdigit()) {
            // 将3位颜色扩展为6位
            let short_color = &color_with_hash[1..];
            let expanded = format!("#{}{}{}{}{}{}",
                short_color.chars().nth(0).unwrap(),
                short_color.chars().nth(0).unwrap(),
                short_color.chars().nth(1).unwrap(),
                short_color.chars().nth(1).unwrap(),
                short_color.chars().nth(2).unwrap(),
                short_color.chars().nth(2).unwrap()
            );
            Some(expanded.to_uppercase())
        } else {
            None
        }
    }

    /// 清理和标准化文本（移除多余空格和特殊字符）
    pub fn clean_text(text: &str) -> String {
        text.lines()
            .map(|line| line.trim())
            .filter(|line| !line.is_empty())
            .collect::<Vec<_>>()
            .join(" ")
            .chars()
            .map(|c| if c.is_control() && c != '\t' { ' ' } else { c })
            .collect::<String>()
            .split_whitespace()
            .collect::<Vec<_>>()
            .join(" ")
    }

    /// 截断文本到指定长度
    pub fn truncate_text(text: &str, max_length: usize) -> String {
        if text.len() <= max_length {
            text.to_string()
        } else {
            let mut truncated = text.chars().take(max_length.saturating_sub(3)).collect::<String>();
            truncated.push_str("...");
            truncated
        }
    }

    /// 生成安全的文件名（移除或替换不安全字符）
    pub fn sanitize_filename(filename: &str) -> String {
        filename
            .chars()
            .map(|c| match c {
                '/' | '\\' | ':' | '*' | '?' | '"' | '<' | '>' | '|' => '_',
                c if c.is_control() => '_',
                c => c,
            })
            .collect::<String>()
            .trim_matches('.')
            .trim()
            .to_string()
    }
}