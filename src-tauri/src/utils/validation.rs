/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-29 01:10:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-06-29 01:10:00
 * @FilePath     : /src-tauri/src/utils/validation.rs
 * @Description  : 验证工具
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-29 01:10:00
 */

use crate::utils::error::{CommandResult, validation_failed};
use regex::Regex;
use std::collections::HashMap;

/// 验证相关的工具
pub struct ValidationHelper;

impl ValidationHelper {
    /// 验证分类名称
    pub fn validate_category_name(name: &str) -> CommandResult<String> {
        let clean_name = name.trim();

        if clean_name.is_empty() {
            return Err(validation_failed("分类名称", "不能为空"));
        }

        if clean_name.len() > 20 {
            return Err(validation_failed("分类名称", "长度不能超过20个字符"));
        }

        // 检查特殊字符
        if clean_name.contains(['<', '>', '"', '\'', '&']) {
            return Err(validation_failed("分类名称", "不能包含特殊字符"));
        }

        Ok(clean_name.to_string())
    }

    /// 验证并清理分类ID
    pub fn clean_category_id(category_id: Option<String>) -> Option<String> {
        category_id.and_then(|id| {
            let trimmed = id.trim();
            if trimmed.is_empty() {
                None
            } else {
                Some(trimmed.to_string())
            }
        })
    }

    /// 验证账号必填字段
    pub fn validate_account_required_fields(
        platform: &str,
        username: &str,
    ) -> CommandResult<()> {
        if platform.trim().is_empty() {
            return Err(validation_failed("平台名称", "不能为空"));
        }

        if username.trim().is_empty() {
            return Err(validation_failed("用户名", "不能为空"));
        }

        Ok(())
    }

    /// 验证邮箱格式
    pub fn validate_email(email: &str) -> CommandResult<()> {
        if email.trim().is_empty() {
            return Ok(()); // 空邮箱是允许的
        }

        let email_regex = Regex::new(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$")
            .map_err(|e| format!("邮箱验证正则表达式错误: {}", e))?;

        if !email_regex.is_match(email.trim()) {
            return Err(validation_failed("邮箱", "格式不正确"));
        }

        Ok(())
    }

    /// 验证手机号格式（支持多种格式）
    pub fn validate_phone(phone: &str) -> CommandResult<()> {
        if phone.trim().is_empty() {
            return Ok(()); // 空手机号是允许的
        }

        // 去除所有非数字字符
        let clean_phone: String = phone.chars().filter(|c| c.is_ascii_digit()).collect();

        // 验证长度（支持国内外手机号）
        if clean_phone.len() < 10 || clean_phone.len() > 15 {
            return Err(validation_failed("手机号", "长度应在10-15位之间"));
        }

        Ok(())
    }

    /// 验证URL格式
    pub fn validate_url(url: &str) -> CommandResult<()> {
        if url.trim().is_empty() {
            return Ok(()); // 空URL是允许的
        }

        let url_str = url.trim();

        // 检查是否包含协议
        if !url_str.starts_with("http://") && !url_str.starts_with("https://") {
            return Err(validation_failed("网址", "必须以http://或https://开头"));
        }

        // 尝试解析URL
        url::Url::parse(url_str)
            .map_err(|_| validation_failed("网址", "格式不正确"))?;

        Ok(())
    }

    /// 验证密码强度
    pub fn validate_password_strength(password: &str) -> CommandResult<PasswordStrength> {
        if password.is_empty() {
            return Err(validation_failed("密码", "不能为空"));
        }

        let mut score = 0;
        let mut feedback = Vec::new();

        // 长度检查
        if password.len() >= 8 {
            score += 2;
        } else {
            feedback.push("密码长度至少8位".to_string());
        }

        // 包含小写字母
        if password.chars().any(|c| c.is_ascii_lowercase()) {
            score += 1;
        } else {
            feedback.push("应包含小写字母".to_string());
        }

        // 包含大写字母
        if password.chars().any(|c| c.is_ascii_uppercase()) {
            score += 1;
        } else {
            feedback.push("应包含大写字母".to_string());
        }

        // 包含数字
        if password.chars().any(|c| c.is_ascii_digit()) {
            score += 1;
        } else {
            feedback.push("应包含数字".to_string());
        }

        // 包含特殊字符
        if password.chars().any(|c| "!@#$%^&*()_+-=[]{}|;':\",./<>?".contains(c)) {
            score += 2;
        } else {
            feedback.push("应包含特殊字符".to_string());
        }

        // 额外加分
        if password.len() >= 12 {
            score += 1;
        }

        let level = match score {
            0..=3 => PasswordStrengthLevel::Weak,
            4..=6 => PasswordStrengthLevel::Medium,
            _ => PasswordStrengthLevel::Strong,
        };

        Ok(PasswordStrength {
            level,
            score,
            feedback,
        })
    }

    /// 验证自定义字段
    pub fn validate_custom_fields(custom_fields: &HashMap<String, String>) -> CommandResult<()> {
        // 检查字段数量限制
        if custom_fields.len() > 20 {
            return Err(validation_failed("自定义字段", "数量不能超过20个"));
        }

        // 检查每个字段
        for (key, value) in custom_fields {
            // 验证字段名
            if key.trim().is_empty() {
                return Err(validation_failed("自定义字段名", "不能为空"));
            }

            if key.len() > 50 {
                return Err(validation_failed("自定义字段名", "长度不能超过50个字符"));
            }

            // 验证字段值
            if value.len() > 500 {
                return Err(validation_failed(
                    &format!("自定义字段'{}'的值", key),
                    "长度不能超过500个字符"
                ));
            }
        }

        Ok(())
    }

    /// 清理和标准化字符串
    pub fn clean_string(input: &str) -> String {
        input.trim().to_string()
    }

    /// 清理可选字符串
    pub fn clean_optional_string(input: Option<String>) -> Option<String> {
        input.and_then(|s| {
            let cleaned = s.trim();
            if cleaned.is_empty() {
                None
            } else {
                Some(cleaned.to_string())
            }
        })
    }
}

/// 密码强度等级
#[derive(Debug, Clone, PartialEq)]
pub enum PasswordStrengthLevel {
    Weak,
    Medium,
    Strong,
}

/// 密码强度评估结果
#[derive(Debug, Clone)]
pub struct PasswordStrength {
    pub level: PasswordStrengthLevel,
    pub score: u8,
    pub feedback: Vec<String>,
}

impl PasswordStrength {
    pub fn description(&self) -> &'static str {
        match self.level {
            PasswordStrengthLevel::Weak => "密码强度较弱",
            PasswordStrengthLevel::Medium => "密码强度中等",
            PasswordStrengthLevel::Strong => "密码强度很好",
        }
    }

    pub fn is_acceptable(&self) -> bool {
        matches!(self.level, PasswordStrengthLevel::Medium | PasswordStrengthLevel::Strong)
    }
}