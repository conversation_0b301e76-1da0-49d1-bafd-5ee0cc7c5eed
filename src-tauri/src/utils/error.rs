/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-29 01:10:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-06-29 01:10:00
 * @FilePath     : /src-tauri/src/utils/error.rs
 * @Description  : 错误处理工具
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-29 01:10:00
 */

/// 统一的命令结果类型
pub type CommandResult<T> = Result<T, String>;

/// 错误转换扩展trait
pub trait ErrorExt<T> {
    fn to_command_error(self) -> CommandResult<T>;
    fn to_command_error_with_context(self, context: &str) -> CommandResult<T>;
}

impl<T, E> ErrorExt<T> for Result<T, E>
where
    E: std::fmt::Display,
{
    fn to_command_error(self) -> CommandResult<T> {
        self.map_err(|e| e.to_string())
    }

    fn to_command_error_with_context(self, context: &str) -> CommandResult<T> {
        self.map_err(|e| format!("{}: {}", context, e))
    }
}

/// 标准错误消息
pub struct ErrorMessages;

impl ErrorMessages {
    pub const DB_NOT_INITIALIZED: &'static str = "Database not initialized";
    pub const AUTH_REQUIRED: &'static str = "Please unlock the app first";
    pub const INVALID_REQUEST: &'static str = "Invalid request parameters";
    pub const OPERATION_FAILED: &'static str = "Operation failed";
    pub const NETWORK_ERROR: &'static str = "Network error occurred";
    pub const PERMISSION_DENIED: &'static str = "Permission denied";
    pub const RESOURCE_NOT_FOUND: &'static str = "Resource not found";
    pub const VALIDATION_FAILED: &'static str = "Validation failed";
}

/// 创建标准错误的便捷函数
pub fn db_not_initialized() -> String {
    ErrorMessages::DB_NOT_INITIALIZED.to_string()
}

pub fn auth_required() -> String {
    ErrorMessages::AUTH_REQUIRED.to_string()
}

pub fn invalid_request(details: &str) -> String {
    format!("{}: {}", ErrorMessages::INVALID_REQUEST, details)
}

pub fn operation_failed(operation: &str, details: &str) -> String {
    format!("{} ({}): {}", ErrorMessages::OPERATION_FAILED, operation, details)
}

pub fn validation_failed(field: &str, reason: &str) -> String {
    format!("{} for {}: {}", ErrorMessages::VALIDATION_FAILED, field, reason)
}

/// 宏：简化错误处理
#[macro_export]
macro_rules! to_cmd_error {
    ($expr:expr) => {
        $expr.map_err(|e| e.to_string())
    };
    ($expr:expr, $context:expr) => {
        $expr.map_err(|e| format!("{}: {}", $context, e))
    };
}

/// 宏：快速创建错误结果
#[macro_export]
macro_rules! cmd_error {
    ($msg:expr) => {
        Err($msg.to_string())
    };
    ($fmt:expr, $($arg:tt)*) => {
        Err(format!($fmt, $($arg)*))
    };
}