/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-29 01:10:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-06-29 00:08:01
 * @FilePath     : /src-tauri/src/utils/types.rs
 * @Description  : 公共类型定义
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-29 01:10:00
 */

use crate::database::DatabaseService;
use tokio::sync::Mutex;

/// 应用状态结构
#[derive(Debug)]
pub struct AppState {
    pub db: Mutex<Option<DatabaseService>>,
    pub master_password: Mutex<Option<String>>,
}

impl AppState {
    /// 创建新的应用状态
    pub fn new() -> Self {
        Self {
            db: Mutex::new(None),
            master_password: Mutex::new(None),
        }
    }
}

impl Default for AppState {
    fn default() -> Self {
        Self::new()
    }
}