/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-29 01:10:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-06-29 00:14:55
 * @FilePath     : /src-tauri/src/utils/database.rs
 * @Description  : 数据库操作工具
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-29 01:10:00
 */

use crate::database::DatabaseService;
use crate::models::{Account, Category};
use crate::utils::error::{CommandResult, ErrorExt, auth_required, db_not_initialized};
use crate::utils::types::AppState;
use anyhow::Result;
use chrono::{DateTime, Utc};
use sqlx::Row;

use tauri::State;

/// 数据库访问助手
pub struct DbHelper;

impl DbHelper {
    /// 获取数据库连接（克隆）
    pub async fn get_db(state: &State<'_, AppState>) -> CommandResult<DatabaseService> {
        let db_guard = state.db.lock().await;
        let db = db_guard
            .as_ref()
            .ok_or_else(db_not_initialized)?
            .clone();
        Ok(db)
    }

    /// 获取数据库连接的引用
    pub async fn get_db_ref<'a>(
        guard: &'a tokio::sync::MutexGuard<'a, Option<DatabaseService>>,
    ) -> CommandResult<&'a DatabaseService> {
        guard.as_ref().ok_or_else(db_not_initialized)
    }

    /// 获取认证状态和密码
    pub async fn get_auth(
        state: &State<'_, AppState>,
    ) -> CommandResult<(DatabaseService, String)> {
        let db_guard = state.db.lock().await;
        let db = db_guard
            .as_ref()
            .ok_or_else(db_not_initialized)?
            .clone();

        let password_guard = state.master_password.lock().await;
        let master_password = password_guard
            .as_ref()
            .ok_or_else(auth_required)?
            .clone();

        Ok((db, master_password))
    }

    /// 仅获取数据库连接（需要认证但不需要密码）
    pub async fn get_db_readonly(state: &State<'_, AppState>) -> CommandResult<DatabaseService> {
        let db_guard = state.db.lock().await;
        let db = db_guard
            .as_ref()
            .ok_or_else(db_not_initialized)?
            .clone();

        // 验证是否已认证
        let _password_guard = state.master_password.lock().await;
        let _master_password = _password_guard
            .as_ref()
            .ok_or_else(auth_required)?;

        Ok(db)
    }

    /// 检查是否已初始化
    pub async fn is_initialized(state: &State<'_, AppState>) -> bool {
        let db_guard = state.db.lock().await;
        db_guard.is_some()
    }

    /// 检查是否已认证
    pub async fn is_authenticated(state: &State<'_, AppState>) -> bool {
        let password_guard = state.master_password.lock().await;
        password_guard.is_some()
    }

    /// 安全地设置数据库连接
    pub async fn set_db(state: &State<'_, AppState>, db: DatabaseService) {
        let mut db_guard = state.db.lock().await;
        *db_guard = Some(db);
    }

    /// 安全地设置主密码
    pub async fn set_master_password(state: &State<'_, AppState>, password: String) {
        let mut password_guard = state.master_password.lock().await;
        *password_guard = Some(password);
    }

    /// 清除主密码（锁定应用）
    pub async fn clear_master_password(state: &State<'_, AppState>) {
        let mut password_guard = state.master_password.lock().await;
        *password_guard = None;
    }
}

/// Row 转 Model 的工具
pub struct ModelMapper;

impl ModelMapper {
    /// 将数据库行转换为 Account 模型
    pub fn row_to_account(row: &sqlx::sqlite::SqliteRow) -> Result<Account> {
        let custom_fields_str: Option<String> = row.try_get("custom_fields").ok();
        let custom_fields = custom_fields_str
            .and_then(|s| serde_json::from_str(&s).ok())
            .unwrap_or_default();

        Ok(Account {
            id: row.get("id"),
            platform: row.get("platform"),
            username: row.get("username"),
            display_name: row.try_get("display_name").ok(),
            encrypted_password: row.get("encrypted_password"),
            email: row.try_get("email").ok(),
            phone: row.try_get("phone").ok(),
            website_url: row.try_get("website_url").ok(),
            category_id: row.try_get("category_id").ok(),
            notes: row.try_get("notes").ok(),
            custom_fields,
            created_at: Self::parse_datetime(&row.get::<String, _>("created_at"))?,
            updated_at: Self::parse_datetime(&row.get::<String, _>("updated_at"))?,
        })
    }

    /// 将数据库行转换为 Category 模型
    pub fn row_to_category(row: &sqlx::sqlite::SqliteRow) -> Result<Category> {
        Ok(Category {
            id: row.get("id"),
            name: row.get("name"),
            color: row.try_get("color").ok(),
            icon: row.try_get("icon").ok(),
            created_at: Self::parse_datetime(&row.get::<String, _>("created_at"))?,
        })
    }

    /// 解析日期时间字符串
    fn parse_datetime(datetime_str: &str) -> Result<DateTime<Utc>> {
        chrono::DateTime::parse_from_rfc3339(datetime_str)
            .map(|dt| dt.with_timezone(&Utc))
            .map_err(|e| anyhow::anyhow!("Failed to parse datetime: {}", e))
    }

    /// 批量转换 Account 行
    pub fn rows_to_accounts(rows: Vec<sqlx::sqlite::SqliteRow>) -> CommandResult<Vec<Account>> {
        let mut accounts = Vec::new();
        for row in rows {
            let account = Self::row_to_account(&row).to_command_error()?;
            accounts.push(account);
        }
        Ok(accounts)
    }

    /// 批量转换 Category 行
    pub fn rows_to_categories(rows: Vec<sqlx::sqlite::SqliteRow>) -> CommandResult<Vec<Category>> {
        let mut categories = Vec::new();
        for row in rows {
            let category = Self::row_to_category(&row).to_command_error()?;
            categories.push(category);
        }
        Ok(categories)
    }
}

/// 宏：简化数据库操作
#[macro_export]
macro_rules! with_db {
    ($state:expr, $operation:expr) => {{
        let db_guard = $state.db.lock().await;
        let db = db_guard
            .as_ref()
            .ok_or_else(|| crate::utils::error::db_not_initialized())?;
        $operation(db).await.to_command_error()
    }};
}

/// 宏：简化认证检查
#[macro_export]
macro_rules! with_auth {
    ($state:expr, $operation:expr) => {{
        let (db, master_password) = crate::utils::database::DbHelper::get_auth($state).await?;
        $operation(&db, &master_password).await.to_command_error()
    }};
}