use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize, Serializer, Deserializer};
use uuid::Uuid;
use std::collections::HashMap;

// 自定义序列化函数
fn serialize_datetime<S>(datetime: &DateTime<Utc>, serializer: S) -> Result<S::Ok, S::Error>
where
    S: Serializer,
{
    serializer.serialize_str(&datetime.to_rfc3339())
}

fn deserialize_datetime<'de, D>(deserializer: D) -> Result<DateTime<Utc>, D::Error>
where
    D: Deserializer<'de>,
{
    let s = String::deserialize(deserializer)?;
    DateTime::parse_from_rfc3339(&s)
        .map(|dt| dt.with_timezone(&Utc))
        .map_err(serde::de::Error::custom)
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Account {
    pub id: String,
    pub platform: String,
    pub username: String,
    pub display_name: Option<String>,
    pub encrypted_password: String,
    pub email: Option<String>,
    pub phone: Option<String>,
    pub website_url: Option<String>,
    pub category_id: Option<String>,
    pub notes: Option<String>,
    #[serde(default)]
    pub custom_fields: HashMap<String, String>,
    #[serde(serialize_with = "serialize_datetime", deserialize_with = "deserialize_datetime")]
    pub created_at: DateTime<Utc>,
    #[serde(serialize_with = "serialize_datetime", deserialize_with = "deserialize_datetime")]
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct Category {
    pub id: String,
    pub name: String,
    pub color: Option<String>,
    pub icon: Option<String>,
    #[serde(serialize_with = "serialize_datetime", deserialize_with = "deserialize_datetime")]
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateAccountRequest {
    pub platform: String,
    pub username: String,
    pub display_name: Option<String>,
    pub password: String,
    pub email: Option<String>,
    pub phone: Option<String>,
    pub website_url: Option<String>,
    pub category_id: Option<String>,
    pub notes: Option<String>,
    #[serde(default)]
    pub custom_fields: HashMap<String, String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateAccountRequest {
    pub id: String,
    pub platform: Option<String>,
    pub username: Option<String>,
    pub display_name: Option<String>,
    pub password: Option<String>,
    pub email: Option<String>,
    pub phone: Option<String>,
    pub website_url: Option<String>,
    pub category_id: Option<String>,
    pub notes: Option<String>,
    pub custom_fields: Option<HashMap<String, String>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateCategoryRequest {
    pub name: String,
    pub color: Option<String>,
    pub icon: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateCategoryRequest {
    pub id: String,
    pub name: Option<String>,
    pub color: Option<String>,
    pub icon: Option<String>,
}

impl Account {
    pub fn new(request: CreateAccountRequest, encrypted_password: String) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4().to_string(),
            platform: request.platform,
            username: request.username,
            display_name: request.display_name,
            encrypted_password,
            email: request.email,
            phone: request.phone,
            website_url: request.website_url,
            category_id: request.category_id,
            notes: request.notes,
            custom_fields: request.custom_fields,
            created_at: now,
            updated_at: now,
        }
    }
}

impl Category {
    pub fn new(request: CreateCategoryRequest) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            name: request.name,
            color: request.color,
            icon: request.icon,
            created_at: Utc::now(),
        }
    }
}