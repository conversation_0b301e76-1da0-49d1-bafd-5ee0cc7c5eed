/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-22 10:53:26
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-06-29 00:13:34
 * @FilePath     : /src-tauri/src/database.rs
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-22 10:53:26
 */
use sqlx::{SqlitePool, Row};
use anyhow::{Result, anyhow};
use std::path::PathBuf;
use dirs;
use crate::models::{Account, Category, CreateAccountRequest, UpdateAccountRequest, CreateCategoryRequest, UpdateCategoryRequest};
use crate::encryption::EncryptionService;

#[derive(Debug, Clone)]
pub struct DatabaseService {
    pool: SqlitePool,
}

impl DatabaseService {
            pub async fn new() -> Result<Self> {
        // 使用持久化数据库文件
        let db_path = Self::get_database_path()?;

        // 确保数据库目录存在
        if let Some(parent) = db_path.parent() {
            std::fs::create_dir_all(parent)
                .map_err(|e| anyhow!("Failed to create database directory: {}", e))?;
        }

        let database_url = format!("sqlite:{}?mode=rwc", db_path.display());
        println!("Using persistent database: {}", database_url);

        let pool = SqlitePool::connect(&database_url).await
            .map_err(|e| anyhow!("Failed to connect to database: {}", e))?;

        let service = Self { pool };
        service.init_database().await
            .map_err(|e| anyhow!("Failed to initialize database schema: {}", e))?;

        println!("Database initialized successfully");
        Ok(service)
    }

    pub fn get_database_path() -> Result<PathBuf> {
        // 优先使用用户数据目录，如果不存在则使用当前目录
        let db_path = if let Some(data_dir) = dirs::data_dir() {
            data_dir.join("AccountManager").join("accounts.db")
        } else {
            // 如果无法获取用户数据目录，则使用当前工作目录
            let current_dir = std::env::current_dir()
                .map_err(|e| anyhow!("Failed to get current directory: {}", e))?;
            current_dir.join("data").join("accounts.db")
        };

        println!("Database path: {}", db_path.display());
        Ok(db_path)
    }

    async fn init_database(&self) -> Result<()> {
        // 创建分类表
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS categories (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL UNIQUE,
                color TEXT,
                icon TEXT,
                created_at TEXT NOT NULL
            )
            "#,
        )
        .execute(&self.pool)
        .await?;

        // 创建账号表
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS accounts (
                id TEXT PRIMARY KEY,
                platform TEXT NOT NULL,
                username TEXT NOT NULL,
                display_name TEXT,
                encrypted_password TEXT NOT NULL,
                email TEXT,
                phone TEXT,
                website_url TEXT,
                category_id TEXT,
                notes TEXT,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL,
                FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
            )
            "#,
        )
        .execute(&self.pool)
        .await?;

        // 迁移：添加display_name字段（如果不存在）
        let _ = sqlx::query("ALTER TABLE accounts ADD COLUMN display_name TEXT")
            .execute(&self.pool)
            .await;
        // 忽略错误，因为如果字段已存在会报错

        // 迁移：添加custom_fields字段（如果不存在）
        let _ = sqlx::query("ALTER TABLE accounts ADD COLUMN custom_fields TEXT")
            .execute(&self.pool)
            .await;
        // 忽略错误，因为如果字段已存在会报错

        // 创建设置表
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS settings (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL,
                updated_at TEXT NOT NULL
            )
            "#,
        )
        .execute(&self.pool)
        .await?;

        // 创建默认分类
        self.create_default_categories().await?;

        Ok(())
    }

    async fn create_default_categories(&self) -> Result<()> {
        let default_categories = vec![
            ("社交平台", "#1890ff", "user"),
            ("工作相关", "#52c41a", "desktop"),
            ("购物平台", "#faad14", "shopping-cart"),
            ("娱乐平台", "#722ed1", "play-circle"),
            ("金融相关", "#f5222d", "bank"),
            ("其他", "#8c8c8c", "more"),
        ];

        for (name, color, icon) in default_categories {
            let exists = sqlx::query("SELECT COUNT(*) as count FROM categories WHERE name = ?")
                .bind(name)
                .fetch_one(&self.pool)
                .await?
                .get::<i64, _>("count") > 0;

            if !exists {
                let category = Category::new(CreateCategoryRequest {
                    name: name.to_string(),
                    color: Some(color.to_string()),
                    icon: Some(icon.to_string()),
                });

                sqlx::query(
                    "INSERT INTO categories (id, name, color, icon, created_at) VALUES (?, ?, ?, ?, ?)"
                )
                .bind(&category.id)
                .bind(&category.name)
                .bind(&category.color)
                .bind(&category.icon)
                .bind(category.created_at.to_rfc3339())
                .execute(&self.pool)
                .await?;
            }
        }

        Ok(())
    }

    // 账号相关操作
    pub async fn create_account(&self, request: CreateAccountRequest, master_password: &str) -> Result<Account> {
        // 验证分类ID是否存在（如果提供了的话）
        if let Some(category_id) = &request.category_id {
            if !category_id.is_empty() {
                let category_exists = sqlx::query("SELECT COUNT(*) as count FROM categories WHERE id = ?")
                    .bind(category_id)
                    .fetch_one(&self.pool)
                    .await?
                    .get::<i64, _>("count") > 0;

                if !category_exists {
                    return Err(anyhow!("指定的分类不存在"));
                }
            }
        }

        let encrypted_password = EncryptionService::encrypt_data(&request.password, master_password)?;
        let account = Account::new(request, encrypted_password);
        let custom_fields_json = serde_json::to_string(&account.custom_fields)?;

        sqlx::query(
            r#"
            INSERT INTO accounts (id, platform, username, display_name, encrypted_password, email, phone, website_url, category_id, notes, custom_fields, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            "#
        )
        .bind(&account.id)
        .bind(&account.platform)
        .bind(&account.username)
        .bind(&account.display_name)
        .bind(&account.encrypted_password)
        .bind(&account.email)
        .bind(&account.phone)
        .bind(&account.website_url)
        .bind(&account.category_id)
        .bind(&account.notes)
        .bind(&custom_fields_json)
        .bind(account.created_at.to_rfc3339())
        .bind(account.updated_at.to_rfc3339())
        .execute(&self.pool)
        .await?;

        Ok(account)
    }

    pub async fn get_all_accounts(&self) -> Result<Vec<Account>> {
        let rows = sqlx::query("SELECT * FROM accounts ORDER BY updated_at DESC")
            .fetch_all(&self.pool)
            .await?;

        let mut accounts = Vec::new();
        for row in rows {
            let custom_fields_str: Option<String> = row.try_get("custom_fields").ok();
            let custom_fields = custom_fields_str
                .and_then(|s| serde_json::from_str(&s).ok())
                .unwrap_or_default();

            let account = Account {
                id: row.get("id"),
                platform: row.get("platform"),
                username: row.get("username"),
                display_name: row.try_get("display_name").ok(),
                encrypted_password: row.get("encrypted_password"),
                email: row.try_get("email").ok(),
                phone: row.try_get("phone").ok(),
                website_url: row.try_get("website_url").ok(),
                category_id: row.try_get("category_id").ok(),
                notes: row.try_get("notes").ok(),
                custom_fields,
                created_at: chrono::DateTime::parse_from_rfc3339(&row.get::<String, _>("created_at"))?.with_timezone(&chrono::Utc),
                updated_at: chrono::DateTime::parse_from_rfc3339(&row.get::<String, _>("updated_at"))?.with_timezone(&chrono::Utc),
            };
            accounts.push(account);
        }

        Ok(accounts)
    }

    pub async fn get_account_by_id(&self, id: &str) -> Result<Option<Account>> {
        let row = sqlx::query("SELECT * FROM accounts WHERE id = ?")
            .bind(id)
            .fetch_optional(&self.pool)
            .await?;

        if let Some(row) = row {
            let custom_fields_str: Option<String> = row.try_get("custom_fields").ok();
            let custom_fields = custom_fields_str
                .and_then(|s| serde_json::from_str(&s).ok())
                .unwrap_or_default();

            let account = Account {
                id: row.get("id"),
                platform: row.get("platform"),
                username: row.get("username"),
                display_name: row.try_get("display_name").ok(),
                encrypted_password: row.get("encrypted_password"),
                email: row.try_get("email").ok(),
                phone: row.try_get("phone").ok(),
                website_url: row.try_get("website_url").ok(),
                category_id: row.try_get("category_id").ok(),
                notes: row.try_get("notes").ok(),
                custom_fields,
                created_at: chrono::DateTime::parse_from_rfc3339(&row.get::<String, _>("created_at"))?.with_timezone(&chrono::Utc),
                updated_at: chrono::DateTime::parse_from_rfc3339(&row.get::<String, _>("updated_at"))?.with_timezone(&chrono::Utc),
            };
            Ok(Some(account))
        } else {
            Ok(None)
        }
    }

            pub async fn update_account(&self, request: UpdateAccountRequest, master_password: &str) -> Result<Account> {
        let mut account = self.get_account_by_id(&request.id).await?
            .ok_or_else(|| anyhow!("Account not found"))?;

        // 清理可能存在的空字符串分类ID
        if let Some(ref category_id) = account.category_id {
            if category_id.trim().is_empty() {
                account.category_id = None;
            }
        }

        // 更新字段
        if let Some(platform) = request.platform {
            account.platform = platform;
        }
        if let Some(username) = request.username {
            account.username = username;
        }
        if let Some(display_name) = request.display_name {
            account.display_name = Some(display_name);
        }
        if let Some(password) = request.password {
            account.encrypted_password = EncryptionService::encrypt_data(&password, master_password)?;
        }
        if let Some(email) = request.email {
            account.email = Some(email);
        }
        if let Some(phone) = request.phone {
            account.phone = Some(phone);
        }
        if let Some(website_url) = request.website_url {
            account.website_url = Some(website_url);
        }
                                                        // 处理分类ID，确保空值被正确处理为None
        match request.category_id {
            Some(category_id) if !category_id.trim().is_empty() => {
                // 验证分类ID是否存在
                let category_exists = sqlx::query("SELECT COUNT(*) as count FROM categories WHERE id = ?")
                    .bind(&category_id)
                    .fetch_one(&self.pool)
                    .await
                    .map(|row| row.get::<i64, _>("count") > 0)
                    .unwrap_or(false);

                if category_exists {
                    account.category_id = Some(category_id);
                } else {
                    account.category_id = None;
                }
            }
            Some(_) => {
                account.category_id = None;
            }
            None => {
                // 保持原值不变
            }
        }
        if let Some(notes) = request.notes {
            account.notes = Some(notes);
        }
                if let Some(custom_fields) = request.custom_fields {
            account.custom_fields = custom_fields;
        }

        account.updated_at = chrono::Utc::now();
        let custom_fields_json = serde_json::to_string(&account.custom_fields)?;

                // 确保custom_fields字段存在
        let _ = sqlx::query("ALTER TABLE accounts ADD COLUMN custom_fields TEXT")
            .execute(&self.pool)
            .await;

        let result = sqlx::query(
            r#"
            UPDATE accounts
            SET platform = ?, username = ?, display_name = ?, encrypted_password = ?, email = ?, phone = ?,
                website_url = ?, category_id = ?, notes = ?, custom_fields = ?, updated_at = ?
            WHERE id = ?
            "#
        )
        .bind(&account.platform)
        .bind(&account.username)
        .bind(&account.display_name)
        .bind(&account.encrypted_password)
        .bind(&account.email)
        .bind(&account.phone)
        .bind(&account.website_url)
        .bind(&account.category_id)
        .bind(&account.notes)
        .bind(&custom_fields_json)
        .bind(account.updated_at.to_rfc3339())
        .bind(&account.id)
        .execute(&self.pool)
        .await;

        result?;
        Ok(account)
    }

    pub async fn delete_account(&self, id: &str) -> Result<()> {
        sqlx::query("DELETE FROM accounts WHERE id = ?")
            .bind(id)
            .execute(&self.pool)
            .await?;

        Ok(())
    }

    pub async fn search_accounts(&self, keyword: &str) -> Result<Vec<Account>> {
        let keyword = format!("%{}%", keyword);
        let rows = sqlx::query(
            "SELECT * FROM accounts WHERE platform LIKE ? OR username LIKE ? OR email LIKE ? ORDER BY updated_at DESC"
        )
        .bind(&keyword)
        .bind(&keyword)
        .bind(&keyword)
        .fetch_all(&self.pool)
        .await?;

        let mut accounts = Vec::new();
        for row in rows {
            let custom_fields_str: Option<String> = row.try_get("custom_fields").ok();
            let custom_fields = custom_fields_str
                .and_then(|s| serde_json::from_str(&s).ok())
                .unwrap_or_default();

            let account = Account {
                id: row.get("id"),
                platform: row.get("platform"),
                username: row.get("username"),
                display_name: row.try_get("display_name").ok(),
                encrypted_password: row.get("encrypted_password"),
                email: row.try_get("email").ok(),
                phone: row.try_get("phone").ok(),
                website_url: row.try_get("website_url").ok(),
                category_id: row.try_get("category_id").ok(),
                notes: row.try_get("notes").ok(),
                custom_fields,
                created_at: chrono::DateTime::parse_from_rfc3339(&row.get::<String, _>("created_at"))?.with_timezone(&chrono::Utc),
                updated_at: chrono::DateTime::parse_from_rfc3339(&row.get::<String, _>("updated_at"))?.with_timezone(&chrono::Utc),
            };
            accounts.push(account);
        }

        Ok(accounts)
    }

    // 分类相关操作
    pub async fn get_all_categories(&self) -> Result<Vec<Category>> {
        let rows = sqlx::query("SELECT * FROM categories ORDER BY created_at ASC")
            .fetch_all(&self.pool)
            .await?;

        let mut categories = Vec::new();
        for row in rows {
            let category = Category {
                id: row.get("id"),
                name: row.get("name"),
                color: row.try_get("color").ok(),
                icon: row.try_get("icon").ok(),
                created_at: chrono::DateTime::parse_from_rfc3339(&row.get::<String, _>("created_at"))?.with_timezone(&chrono::Utc),
            };
            categories.push(category);
        }

        Ok(categories)
    }

    pub async fn create_category(&self, request: CreateCategoryRequest) -> Result<Category> {
        // 验证分类名称
        if request.name.trim().is_empty() {
            return Err(anyhow!("分类名称不能为空"));
        }

        if request.name.trim().len() > 20 {
            return Err(anyhow!("分类名称长度不能超过20个字符"));
        }

        // 检查分类名称是否已存在
        let existing = sqlx::query("SELECT COUNT(*) as count FROM categories WHERE name = ?")
            .bind(request.name.trim())
            .fetch_one(&self.pool)
            .await?
            .get::<i64, _>("count");

        if existing > 0 {
            return Err(anyhow!("分类名称已存在"));
        }

        // 创建分类，使用清理后的名称
        let mut clean_request = request;
        clean_request.name = clean_request.name.trim().to_string();
        let category = Category::new(clean_request);

        sqlx::query("INSERT INTO categories (id, name, color, icon, created_at) VALUES (?, ?, ?, ?, ?)")
            .bind(&category.id)
            .bind(&category.name)
            .bind(&category.color)
            .bind(&category.icon)
            .bind(category.created_at.to_rfc3339())
            .execute(&self.pool)
            .await?;

        Ok(category)
    }

    pub async fn update_category(&self, request: UpdateCategoryRequest) -> Result<Category> {
        // 首先获取现有分类
        let row = sqlx::query("SELECT * FROM categories WHERE id = ?")
            .bind(&request.id)
            .fetch_optional(&self.pool)
            .await?;

        let mut category = if let Some(row) = row {
            Category {
                id: row.get("id"),
                name: row.get("name"),
                color: row.try_get("color").ok(),
                icon: row.try_get("icon").ok(),
                created_at: chrono::DateTime::parse_from_rfc3339(&row.get::<String, _>("created_at"))?.with_timezone(&chrono::Utc),
            }
        } else {
            return Err(anyhow!("分类不存在"));
        };

        // 更新字段并验证
        if let Some(name) = request.name {
            let clean_name = name.trim();

            // 验证分类名称
            if clean_name.is_empty() {
                return Err(anyhow!("分类名称不能为空"));
            }

            if clean_name.len() > 20 {
                return Err(anyhow!("分类名称长度不能超过20个字符"));
            }

            // 检查分类名称是否已存在（排除当前分类）
            let existing = sqlx::query("SELECT COUNT(*) as count FROM categories WHERE name = ? AND id != ?")
                .bind(clean_name)
                .bind(&request.id)
                .fetch_one(&self.pool)
                .await?
                .get::<i64, _>("count");

            if existing > 0 {
                return Err(anyhow!("分类名称已存在"));
            }

            category.name = clean_name.to_string();
        }
        if let Some(color) = request.color {
            category.color = Some(color);
        }
        if let Some(icon) = request.icon {
            category.icon = Some(icon);
        }

        // 更新数据库
        sqlx::query("UPDATE categories SET name = ?, color = ?, icon = ? WHERE id = ?")
            .bind(&category.name)
            .bind(&category.color)
            .bind(&category.icon)
            .bind(&category.id)
            .execute(&self.pool)
            .await?;

        Ok(category)
    }

    pub async fn delete_category(&self, id: &str) -> Result<()> {
        // 检查是否有账号使用此分类
        let count = sqlx::query("SELECT COUNT(*) as count FROM accounts WHERE category_id = ?")
            .bind(id)
            .fetch_one(&self.pool)
            .await?
            .get::<i64, _>("count");

        if count > 0 {
            return Err(anyhow!("Cannot delete category: {} accounts are still using this category", count));
        }

        // 删除分类
        sqlx::query("DELETE FROM categories WHERE id = ?")
            .bind(id)
            .execute(&self.pool)
            .await?;

        Ok(())
    }

    // 设置相关操作
    pub async fn get_setting(&self, key: &str) -> Result<Option<String>> {
        let row = sqlx::query("SELECT value FROM settings WHERE key = ?")
            .bind(key)
            .fetch_optional(&self.pool)
            .await?;

        Ok(row.map(|r| r.get("value")))
    }

    pub async fn set_setting(&self, key: &str, value: &str) -> Result<()> {
        let now = chrono::Utc::now().to_rfc3339();

        sqlx::query(
            "INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES (?, ?, ?)"
        )
        .bind(key)
        .bind(value)
        .bind(now)
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    // 清空数据相关操作
    pub async fn clear_all_accounts(&self) -> Result<()> {
        sqlx::query("DELETE FROM accounts")
            .execute(&self.pool)
            .await?;

        println!("All accounts cleared from database");
        Ok(())
    }

    pub async fn clear_all_categories(&self) -> Result<()> {
        // 只清除非默认分类，保留默认分类
        let default_categories = vec![
            "社交平台",
            "工作相关",
            "购物平台",
            "娱乐平台",
            "金融相关",
            "其他",
        ];

        // 删除所有非默认分类
        sqlx::query("DELETE FROM categories WHERE name NOT IN (?, ?, ?, ?, ?, ?)")
            .bind(&default_categories[0])
            .bind(&default_categories[1])
            .bind(&default_categories[2])
            .bind(&default_categories[3])
            .bind(&default_categories[4])
            .bind(&default_categories[5])
            .execute(&self.pool)
            .await?;

        println!("All custom categories cleared from database");
        Ok(())
    }
}