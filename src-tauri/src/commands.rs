use tauri::State;
use serde::Serialize;
use crate::models::{Account, Category, CreateAccountRequest, UpdateAccountRequest, CreateCategoryRequest, UpdateCategoryRequest};
use crate::database::DatabaseService;
use crate::encryption::EncryptionService;
use crate::utils::{App<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, CommandResult, ErrorExt};

#[tauri::command]
pub async fn init_database(state: State<'_, AppState>) -> Result<(), String> {
    let db = DatabaseService::new().await.map_err(|e| e.to_string())?;
    let mut db_guard = state.db.lock().await;
    *db_guard = Some(db);
    Ok(())
}

#[tauri::command]
pub async fn verify_master_password(
    password: String,
    state: State<'_, AppState>
) -> Result<bool, String> {
    println!("Verifying password in backend...");
    let db_guard = state.db.lock().await;
    let db = db_guard.as_ref().ok_or("Database not initialized")?;

    // 检查是否已设置主密码
    let stored_hash = db.get_setting("master_password_hash")
        .await
        .map_err(|e| e.to_string())?;

    match stored_hash {
        Some(hash) => {
            println!("Found existing password hash, verifying...");
            let is_valid = EncryptionService::verify_password(&password, &hash)
                .map_err(|e| e.to_string())?;

            if is_valid {
                let mut password_guard = state.master_password.lock().await;
                *password_guard = Some(password);
                println!("Password verified and stored in memory");
            } else {
                println!("Password verification failed");
            }

            Ok(is_valid)
        }
        None => {
            // 首次使用，设置主密码
            println!("No existing password found, setting new password...");
            let hash = EncryptionService::hash_password(&password)
                .map_err(|e| e.to_string())?;

            db.set_setting("master_password_hash", &hash)
                .await
                .map_err(|e| e.to_string())?;

            let mut password_guard = state.master_password.lock().await;
            *password_guard = Some(password);
            println!("New password set and stored");
            Ok(true)
        }
    }
}

#[tauri::command]
pub async fn is_master_password_set(state: State<'_, AppState>) -> Result<bool, String> {
    let db_guard = state.db.lock().await;
    let db = db_guard.as_ref().ok_or("Database not initialized")?;

    let stored_hash = db.get_setting("master_password_hash")
        .await
        .map_err(|e| e.to_string())?;

    Ok(stored_hash.is_some())
}

#[tauri::command]
pub async fn lock_app(state: State<'_, AppState>) -> Result<(), String> {
    let mut password_guard = state.master_password.lock().await;
    *password_guard = None;
    println!("App locked - master password cleared from memory");
    Ok(())
}

#[tauri::command]
pub async fn check_auth_status(state: State<'_, AppState>) -> Result<bool, String> {
    let password_guard = state.master_password.lock().await;
    let is_authenticated = password_guard.is_some();
    println!("Auth status check: authenticated = {}", is_authenticated);
    Ok(is_authenticated)
}

#[tauri::command]
pub async fn create_account(
    request: CreateAccountRequest,
    state: State<'_, AppState>
) -> Result<Account, String> {
    println!("Creating account for platform: {}", request.platform);

    let db_guard = state.db.lock().await;
    let db = db_guard.as_ref().ok_or("Database not initialized")?;
    println!("Database connection obtained");

    let password_guard = state.master_password.lock().await;
    let master_password = password_guard.as_ref().ok_or_else(|| {
        println!("Master password not found in memory - app needs to be unlocked first");
        println!("Current password state: {:?}", password_guard.is_some());
        "Please unlock the app first".to_string()
    })?;
    println!("Master password verified");

    let account = db.create_account(request, master_password)
        .await
        .map_err(|e| {
            println!("Failed to create account in database: {}", e);
            e.to_string()
        })?;

    println!("Account created successfully with ID: {}", account.id);
    Ok(account)
}

#[tauri::command]
pub async fn get_all_accounts(state: State<'_, AppState>) -> CommandResult<Vec<Account>> {
    LogHelper::log_command_start("get_all_accounts", None);

    let db = DbHelper::get_db_readonly(&state).await?;
    let accounts = db.get_all_accounts().await.to_command_error()?;

    LogHelper::log_data_stats("get_all_accounts", accounts.len(), "accounts");
    LogHelper::log_command_success("get_all_accounts", Some(&format!("Retrieved {} accounts", accounts.len())));

    Ok(accounts)
}

#[tauri::command]
pub async fn get_account_by_id(
    id: String,
    state: State<'_, AppState>
) -> Result<Option<Account>, String> {
    let db_guard = state.db.lock().await;
    let db = db_guard.as_ref().ok_or("Database not initialized")?;

    let _password_guard = state.master_password.lock().await;
    let _master_password = _password_guard.as_ref().ok_or("Please unlock the app first")?;

    let account = db.get_account_by_id(&id)
        .await
        .map_err(|e| e.to_string())?;

    Ok(account)
}

#[tauri::command]
pub async fn update_account(
    request: UpdateAccountRequest,
    state: State<'_, AppState>
) -> Result<Account, String> {
    let db_guard = state.db.lock().await;
    let db = db_guard.as_ref().ok_or("Database not initialized")?;

    let password_guard = state.master_password.lock().await;
    let master_password = password_guard.as_ref().ok_or("Please unlock the app first")?;

    let account = db.update_account(request, master_password)
        .await
        .map_err(|e| e.to_string())?;

    Ok(account)
}

#[tauri::command]
pub async fn delete_account(
    id: String,
    state: State<'_, AppState>
) -> Result<(), String> {
    let db_guard = state.db.lock().await;
    let db = db_guard.as_ref().ok_or("Database not initialized")?;

    let _password_guard = state.master_password.lock().await;
    let _master_password = _password_guard.as_ref().ok_or("Please unlock the app first")?;

    db.delete_account(&id)
        .await
        .map_err(|e| e.to_string())?;

    Ok(())
}

#[tauri::command]
pub async fn batch_delete_accounts(
    ids: Vec<String>,
    state: State<'_, AppState>
) -> CommandResult<i32> {
    use crate::utils::{LogHelper, DbHelper};

    LogHelper::log_command_start("batch_delete_accounts", Some(&format!("删除 {} 个账号", ids.len())));

    if ids.is_empty() {
        return Err("没有要删除的账号".to_string());
    }

    let (db, _master_password) = DbHelper::get_auth(&state).await?;

    let mut deleted_count = 0i32;
    let mut failed_count = 0i32;

    for (index, id) in ids.iter().enumerate() {
        LogHelper::log_debug("batch_delete_accounts", &format!("删除账号 {}/{}: {}", index + 1, ids.len(), id));

        match db.delete_account(id).await {
            Ok(_) => {
                deleted_count += 1;
                LogHelper::log_debug("batch_delete_accounts", &format!("账号 {} 删除成功", id));
            }
            Err(e) => {
                failed_count += 1;
                LogHelper::log_warning("batch_delete_accounts", &format!("账号 {} 删除失败: {}", id, e));
            }
        }
    }

    LogHelper::log_command_success("batch_delete_accounts", Some(&format!("成功删除 {} 个账号，失败 {} 个", deleted_count, failed_count)));
    Ok(deleted_count)
}

#[tauri::command]
pub async fn search_accounts(
    keyword: String,
    state: State<'_, AppState>
) -> Result<Vec<Account>, String> {
    let db_guard = state.db.lock().await;
    let db = db_guard.as_ref().ok_or("Database not initialized")?;

    let _password_guard = state.master_password.lock().await;
    let _master_password = _password_guard.as_ref().ok_or("Please unlock the app first")?;

    let accounts = db.search_accounts(&keyword)
        .await
        .map_err(|e| e.to_string())?;

    Ok(accounts)
}

#[tauri::command]
pub async fn decrypt_password(
    encrypted_password: String,
    state: State<'_, AppState>
) -> Result<String, String> {
    let password_guard = state.master_password.lock().await;
    let master_password = password_guard.as_ref().ok_or("Please unlock the app first")?;

    let decrypted = EncryptionService::decrypt_data(&encrypted_password, master_password)
        .map_err(|e| e.to_string())?;

    Ok(decrypted)
}

#[tauri::command]
pub async fn get_all_categories(state: State<'_, AppState>) -> Result<Vec<Category>, String> {
    let db_guard = state.db.lock().await;
    let db = db_guard.as_ref().ok_or("Database not initialized")?;

    let categories = db.get_all_categories()
        .await
        .map_err(|e| e.to_string())?;

    Ok(categories)
}

#[tauri::command]
pub async fn create_category(
    request: CreateCategoryRequest,
    state: State<'_, AppState>
) -> Result<Category, String> {
    let db_guard = state.db.lock().await;
    let db = db_guard.as_ref().ok_or("Database not initialized")?;

    let category = db.create_category(request)
        .await
        .map_err(|e| e.to_string())?;

    Ok(category)
}

#[tauri::command]
pub async fn update_category(
    request: UpdateCategoryRequest,
    state: State<'_, AppState>
) -> Result<Category, String> {
    let db_guard = state.db.lock().await;
    let db = db_guard.as_ref().ok_or("Database not initialized")?;

    let category = db.update_category(request)
        .await
        .map_err(|e| e.to_string())?;

    Ok(category)
}

#[tauri::command]
pub async fn delete_category(
    id: String,
    state: State<'_, AppState>
) -> Result<(), String> {
    let db_guard = state.db.lock().await;
    let db = db_guard.as_ref().ok_or("Database not initialized")?;

    db.delete_category(&id)
        .await
        .map_err(|e| e.to_string())?;

    Ok(())
}

#[tauri::command]
pub async fn get_database_path() -> Result<String, String> {
    use crate::database::DatabaseService;
    let db_path = DatabaseService::get_database_path()
        .map_err(|e| e.to_string())?;
    Ok(db_path.to_string_lossy().to_string())
}

#[tauri::command]
pub async fn open_data_folder() -> Result<(), String> {
    use crate::database::DatabaseService;
    let db_path = DatabaseService::get_database_path()
        .map_err(|e| e.to_string())?;

    // 获取数据库文件的目录
    let data_dir = db_path.parent()
        .ok_or("Failed to get parent directory")?;

    // 根据操作系统打开文件夹
    #[cfg(target_os = "macos")]
    {
        std::process::Command::new("open")
            .arg(data_dir)
            .spawn()
            .map_err(|e| format!("Failed to open folder: {}", e))?;
    }

    #[cfg(target_os = "windows")]
    {
        std::process::Command::new("explorer")
            .arg(data_dir)
            .spawn()
            .map_err(|e| format!("Failed to open folder: {}", e))?;
    }

    #[cfg(target_os = "linux")]
    {
        std::process::Command::new("xdg-open")
            .arg(data_dir)
            .spawn()
            .map_err(|e| format!("Failed to open folder: {}", e))?;
    }

    Ok(())
}

#[tauri::command]
pub async fn open_downloads_folder() -> Result<(), String> {
    // 获取下载目录路径
    let downloads_dir = if let Some(home_dir) = dirs::home_dir() {
        home_dir.join("Downloads")
    } else {
        return Err("Failed to get home directory".to_string());
    };

    // 根据操作系统打开文件夹
    #[cfg(target_os = "macos")]
    {
        std::process::Command::new("open")
            .arg(&downloads_dir)
            .spawn()
            .map_err(|e| format!("Failed to open downloads folder: {}", e))?;
    }

    #[cfg(target_os = "windows")]
    {
        std::process::Command::new("explorer")
            .arg(&downloads_dir)
            .spawn()
            .map_err(|e| format!("Failed to open downloads folder: {}", e))?;
    }

    #[cfg(target_os = "linux")]
    {
        std::process::Command::new("xdg-open")
            .arg(&downloads_dir)
            .spawn()
            .map_err(|e| format!("Failed to open downloads folder: {}", e))?;
    }

    Ok(())
}

#[tauri::command]
pub async fn clear_all_data(state: State<'_, AppState>) -> Result<(), String> {
    println!("Starting to clear all data...");

    let db_guard = state.db.lock().await;
    let db = db_guard.as_ref().ok_or("Database not initialized")?;

    let _password_guard = state.master_password.lock().await;
    let _master_password = _password_guard.as_ref().ok_or("Please unlock the app first")?;

    // 清空所有账号数据
    db.clear_all_accounts()
        .await
        .map_err(|e| {
            println!("Failed to clear accounts: {}", e);
            e.to_string()
        })?;

    // 清空所有分类数据
    db.clear_all_categories()
        .await
        .map_err(|e| {
            println!("Failed to clear categories: {}", e);
            e.to_string()
        })?;

    println!("All data cleared successfully");
    Ok(())
}

#[tauri::command]
pub async fn export_data_to_file(
    _app_handle: tauri::AppHandle,
    state: State<'_, AppState>
) -> Result<String, String> {
    println!("Starting data export to file...");

    let db_guard = state.db.lock().await;
    let db = db_guard.as_ref().ok_or("Database not initialized")?;

    let _password_guard = state.master_password.lock().await;
    let _master_password = _password_guard.as_ref().ok_or("Please unlock the app first")?;

    // 获取所有账号和分类
    let accounts = db.get_all_accounts()
        .await
        .map_err(|e| {
            println!("Failed to get accounts: {}", e);
            e.to_string()
        })?;

    println!("Retrieved {} accounts", accounts.len());

    let categories = db.get_all_categories()
        .await
        .map_err(|e| {
            println!("Failed to get categories: {}", e);
            e.to_string()
        })?;

    println!("Retrieved {} categories", categories.len());

    // 创建导出数据结构
    let export_time = chrono::Utc::now().to_rfc3339();

    // 创建一个临时的导出结构体
    #[derive(Serialize)]
    struct ExportData {
        version: String,
        export_time: String,
        accounts: Vec<Account>,
        categories: Vec<Category>,
    }

    let export_data = ExportData {
        version: "1.0".to_string(),
        export_time,
        accounts,
        categories,
    };

    let json_string = serde_json::to_string_pretty(&export_data)
        .map_err(|e| {
            println!("Failed to serialize export data: {}", e);
            format!("Failed to serialize export data: {}", e)
        })?;

    // 生成默认文件名
    let now = chrono::Utc::now();
    let timestamp = now.format("%Y%m%d%H%M%S").to_string();
    let default_filename = format!("AccountManager_backup_{}.json", timestamp);

    // 获取桌面目录作为保存位置
    let desktop_dir = if let Some(desktop_path) = dirs::desktop_dir() {
        desktop_path
    } else if let Some(home_dir) = dirs::home_dir() {
        home_dir.join("Desktop")
    } else {
        return Err("无法确定保存位置".to_string());
    };

    let file_path = desktop_dir.join(&default_filename);

    // 写入文件
    std::fs::write(&file_path, json_string)
        .map_err(|e| format!("Failed to write file: {}", e))?;

    println!("Data exported successfully to: {}", file_path.display());

    // 打开文件所在目录
    #[cfg(target_os = "macos")]
    {
        std::process::Command::new("open")
            .arg("-R")  // -R 参数会在 Finder 中选中文件
            .arg(&file_path)
            .spawn()
            .map_err(|e| format!("Failed to open finder: {}", e))?;
    }

    #[cfg(target_os = "windows")]
    {
        std::process::Command::new("explorer")
            .arg("/select,")
            .arg(&file_path)
            .spawn()
            .map_err(|e| format!("Failed to open explorer: {}", e))?;
    }

    #[cfg(target_os = "linux")]
    {
        // 在 Linux 上只能打开目录，无法选中文件
        std::process::Command::new("xdg-open")
            .arg(&desktop_dir)
            .spawn()
            .map_err(|e| format!("Failed to open file manager: {}", e))?;
    }

    Ok(format!("数据导出成功！文件已保存到桌面：{}", default_filename))
}

#[tauri::command]
pub async fn export_data(state: State<'_, AppState>) -> Result<String, String> {
    println!("Starting data export...");

    let db_guard = state.db.lock().await;
    let db = db_guard.as_ref().ok_or("Database not initialized")?;

    let _password_guard = state.master_password.lock().await;
    let _master_password = _password_guard.as_ref().ok_or("Please unlock the app first")?;

    // 获取所有账号和分类
    let accounts = db.get_all_accounts()
        .await
        .map_err(|e| {
            println!("Failed to get accounts: {}", e);
            e.to_string()
        })?;

    println!("Retrieved {} accounts", accounts.len());

    let categories = db.get_all_categories()
        .await
        .map_err(|e| {
            println!("Failed to get categories: {}", e);
            e.to_string()
        })?;

    println!("Retrieved {} categories", categories.len());

    // 创建导出数据结构
    let export_time = chrono::Utc::now().to_rfc3339();

    // 创建一个临时的导出结构体
    #[derive(Serialize)]
    struct ExportData {
        version: String,
        export_time: String,
        accounts: Vec<Account>,
        categories: Vec<Category>,
    }

    let export_data = ExportData {
        version: "1.0".to_string(),
        export_time,
        accounts,
        categories,
    };

    let json_string = serde_json::to_string_pretty(&export_data)
        .map_err(|e| {
            println!("Failed to serialize export data: {}", e);
            format!("Failed to serialize export data: {}", e)
        })?;

    println!("Data export completed successfully, JSON length: {}", json_string.len());
    Ok(json_string)
}

#[derive(Serialize)]
pub struct ImportResult {
    pub success: i32,
    pub failed: i32,
}

#[tauri::command]
pub async fn import_data(
    import_json: String,
    state: State<'_, AppState>
) -> CommandResult<ImportResult> {
    LogHelper::log_command_start("import_data", Some(&format!("JSON size: {} bytes", import_json.len())));
    LogHelper::log_debug("import_data", &format!("JSON preview: {}", &import_json[..import_json.len().min(200)]));

    // 验证导入数据大小
    if import_json.len() > 50 * 1024 * 1024 { // 50MB 限制
        LogHelper::log_command_error("import_data", "导入文件过大");
        return Err("导入文件过大，请选择小于50MB的文件".to_string());
    }

    let (db, master_password) = DbHelper::get_auth(&state).await?;

    // 解析和验证导入的JSON数据
    let import_data: serde_json::Value = serde_json::from_str(&import_json)
        .to_command_error_with_context("解析JSON格式失败")?;

    // 验证数据格式
    if !import_data.is_object() {
        return Err("无效的备份文件格式，期望JSON对象".to_string());
    }

    let accounts_data = import_data.get("accounts")
        .ok_or("备份文件中缺少账号数据")?;
    let categories_data = import_data.get("categories")
        .ok_or("备份文件中缺少分类数据")?;

    let mut imported_accounts = 0i32;
    let mut failed_accounts = 0i32;
    let mut imported_categories = 0i32;

    // 导入分类（非默认分类）
    if let Some(categories_array) = categories_data.as_array() {
        LogHelper::log_info("import_data", &format!("开始处理 {} 个分类", categories_array.len()));

        let default_category_names = vec![
            "社交平台", "工作相关", "购物平台", "娱乐平台", "金融相关", "其他"
        ];

        for (index, category_json) in categories_array.iter().enumerate() {
            if let Some(category_name) = category_json.get("name").and_then(|v| v.as_str()) {
                // 跳过默认分类
                if default_category_names.contains(&category_name) {
                    LogHelper::log_debug("import_data", &format!("跳过默认分类: {}", category_name));
                    continue;
                }

                // 尝试导入自定义分类
                if let Ok(category) = serde_json::from_value::<crate::models::Category>(category_json.clone()) {
                    let create_request = crate::models::CreateCategoryRequest {
                        name: category.name.clone(),
                        color: category.color.clone(),
                        icon: category.icon.clone(),
                    };

                    match db.create_category(create_request).await {
                        Ok(_) => {
                            LogHelper::log_info("import_data", &format!("成功导入分类: {}", category.name));
                            imported_categories += 1;
                        }
                        Err(e) => {
                            LogHelper::log_warning("import_data", &format!("分类导入失败: {} - {}", category.name, e));
                        }
                    }
                } else {
                    LogHelper::log_warning("import_data", &format!("分类 {} 解析失败", index));
                }
            }
        }
    }

    // 导入账号
    if let Some(accounts_array) = accounts_data.as_array() {
        LogHelper::log_info("import_data", &format!("开始处理 {} 个账号", accounts_array.len()));

        for (index, account_json) in accounts_array.iter().enumerate() {
            LogHelper::log_debug("import_data", &format!("处理账号 {}", index));

            // 尝试解析为简单的账号数据结构
            if let Some(account_obj) = account_json.as_object() {
                let platform = account_obj.get("platform")
                    .and_then(|v| v.as_str())
                    .unwrap_or("")
                    .trim()
                    .to_string();

                let username = account_obj.get("username")
                    .and_then(|v| v.as_str())
                    .unwrap_or("")
                    .trim()
                    .to_string();

                let password = account_obj.get("password")
                    .and_then(|v| v.as_str())
                    .unwrap_or("")
                    .trim()
                    .to_string();

                // 使用新的数据转换工具
                use crate::utils::ConversionHelper;
                let email = ConversionHelper::json_value_to_optional_string(account_obj.get("email"));
                let phone = ConversionHelper::json_value_to_optional_string(account_obj.get("phone"));
                let website_url = ConversionHelper::json_value_to_optional_string(account_obj.get("website_url"));
                let notes = ConversionHelper::json_value_to_optional_string(account_obj.get("notes"));

                // 验证必填字段
                if platform.is_empty() || username.is_empty() {
                    LogHelper::log_warning("import_data", &format!("账号 {} 跳过：缺少平台名称或用户名", index));
                    failed_accounts += 1;
                    continue;
                }

                LogHelper::log_debug("import_data", &format!("创建账号: {}@{}", username, platform));

                let create_request = crate::models::CreateAccountRequest {
                    platform,
                    username,
                    display_name: None,
                    password,
                    email,
                    phone,
                    website_url,
                    category_id: None,
                    notes,
                    custom_fields: std::collections::HashMap::new(),
                };

                match db.create_account(create_request, &master_password).await {
                    Ok(_) => {
                        LogHelper::log_debug("import_data", &format!("账号 {} 导入成功", index));
                        imported_accounts += 1;
                    }
                    Err(e) => {
                        LogHelper::log_warning("import_data", &format!("账号 {} 导入失败: {}", index, e));
                        failed_accounts += 1;
                    }
                }
            } else {
                LogHelper::log_warning("import_data", &format!("账号 {} 跳过：格式无效", index));
                failed_accounts += 1;
            }
        }
    }

    LogHelper::log_data_stats("import_data", imported_accounts as usize, "accounts imported");
    LogHelper::log_data_stats("import_data", failed_accounts as usize, "accounts failed");
    LogHelper::log_data_stats("import_data", imported_categories as usize, "categories imported");

    let success_message = format!("成功导入 {} 个账号，失败 {} 个，分类 {} 个",
                                 imported_accounts, failed_accounts, imported_categories);
    LogHelper::log_command_success("import_data", Some(&success_message));

    Ok(ImportResult {
        success: imported_accounts,
        failed: failed_accounts,
    })
}

#[tauri::command]
pub async fn download_template() -> Result<String, String> {
    println!("Starting template download...");

    use rust_xlsxwriter::*;

    // 生成默认文件名
    let now = chrono::Utc::now();
    let timestamp = now.format("%Y%m%d%H%M%S").to_string();
    let default_filename = format!("账号管理模板_{}.xlsx", timestamp);

    // 获取桌面目录作为保存位置
    let desktop_dir = if let Some(desktop_path) = dirs::desktop_dir() {
        desktop_path
    } else if let Some(home_dir) = dirs::home_dir() {
        home_dir.join("Desktop")
    } else {
        return Err("无法确定保存位置".to_string());
    };

    let file_path = desktop_dir.join(&default_filename);

    // 创建新的工作簿
    let mut workbook = Workbook::new();
    let worksheet = workbook.add_worksheet();

    // 设置工作表名称
    worksheet.set_name("账号数据").map_err(|e| format!("Failed to set worksheet name: {}", e))?;

    // 创建标题样式
    let header_format = Format::new()
        .set_bold()
        .set_background_color(Color::RGB(0x4472C4))
        .set_font_color(Color::White)
        .set_align(FormatAlign::Center)
        .set_border(FormatBorder::Thin)
        .set_border_color(Color::RGB(0x333333))
        .set_font_size(12);

    // 创建数据样式
    let data_format = Format::new()
        .set_align(FormatAlign::Left)
        .set_border(FormatBorder::Thin)
        .set_border_color(Color::RGB(0xCCCCCC))
        .set_font_size(11);

    // 写入标题行
    let headers = ["平台", "用户名", "密码", "邮箱", "手机号", "网址", "备注"];
    for (col, header) in headers.iter().enumerate() {
        worksheet.write_string_with_format(0, col as u16, *header, &header_format)
            .map_err(|e| format!("Failed to write header: {}", e))?;
    }

    // 写入示例数据
    let sample_data = [
        ["微信", "your_username", "your_password", "<EMAIL>", "13800138000", "https://weixin.qq.com", "这是备注信息"],
        ["知乎", "zhihu_user", "zhihu_pass", "<EMAIL>", "13800138001", "https://zhihu.com", "知识分享平台"],
        ["GitHub", "github_dev", "github_secret", "<EMAIL>", "", "https://github.com", "代码托管平台"],
    ];

    for (row, data) in sample_data.iter().enumerate() {
        for (col, value) in data.iter().enumerate() {
            worksheet.write_string_with_format((row + 1) as u32, col as u16, *value, &data_format)
                .map_err(|e| format!("Failed to write data: {}", e))?;
        }
    }

    // 设置列宽（增加宽度）
    let column_widths = [18.0, 28.0, 20.0, 35.0, 20.0, 35.0, 25.0];
    for (col, width) in column_widths.iter().enumerate() {
        worksheet.set_column_width(col as u16, *width)
            .map_err(|e| format!("Failed to set column width: {}", e))?;
    }

        // 设置行高
    // 标题行高度（增加到35像素）
    worksheet.set_row_height(0, 35.0)
        .map_err(|e| format!("Failed to set header row height: {}", e))?;

    // 数据行高度（增加到28像素）
    for row in 1..=sample_data.len() {
        worksheet.set_row_height(row as u32, 28.0)
            .map_err(|e| format!("Failed to set row height: {}", e))?;
    }

    // 为后续可能添加的空行也设置行高（设置更多行的默认高度）
    for row in (sample_data.len() + 1)..=20 {
        worksheet.set_row_height(row as u32, 28.0)
            .map_err(|e| format!("Failed to set row height: {}", e))?;
    }

    // 冻结标题行
    worksheet.set_freeze_panes(1, 0)
        .map_err(|e| format!("Failed to freeze panes: {}", e))?;

    // 保存文件
    workbook.save(&file_path)
        .map_err(|e| format!("Failed to save Excel file: {}", e))?;

    println!("Template downloaded successfully to: {}", file_path.display());

    // 打开文件所在目录
    #[cfg(target_os = "macos")]
    {
        std::process::Command::new("open")
            .arg("-R")  // -R 参数会在 Finder 中选中文件
            .arg(&file_path)
            .spawn()
            .map_err(|e| format!("Failed to open finder: {}", e))?;
    }

    #[cfg(target_os = "windows")]
    {
        std::process::Command::new("explorer")
            .arg("/select,")
            .arg(&file_path)
            .spawn()
            .map_err(|e| format!("Failed to open explorer: {}", e))?;
    }

    #[cfg(target_os = "linux")]
    {
        // 在 Linux 上只能打开目录，无法选中文件
        std::process::Command::new("xdg-open")
            .arg(&desktop_dir)
            .spawn()
            .map_err(|e| format!("Failed to open file manager: {}", e))?;
    }

    Ok(format!("Excel模板文件已保存到桌面：{}", default_filename))
}