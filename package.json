{"name": "account-manager", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "tauri": "tauri", "mock": "concurrently \"json-server --watch mock/db.json --port 3001\" \"json-server --watch mock/accounts/db.json --port 3002\"", "dev:full": "concurrently \"npm run mock\" \"npm run dev\""}, "dependencies": {"@arco-design/web-vue": "^2.57.0", "@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "@vueuse/core": "^13.4.0", "echarts": "^5.6.0", "element-plus": "^2.10.4", "pinia": "^3.0.3", "uuid": "^11.1.0", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-router": "^4.5.1", "xlsx": "^0.18.5"}, "devDependencies": {"@tauri-apps/cli": "^2", "@vitejs/plugin-vue": "^5.2.1", "concurrently": "^8.2.2", "json-server": "^1.0.0-beta.3", "sass": "^1.89.2", "typescript": "~5.6.2", "vite": "^6.0.3", "vue-tsc": "^2.1.10"}}