<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-15 15:52:01
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-15 17:08:48
 * @FilePath     : /tools-apps/cursor/roles/frond/真机调试.md
 * @Description  : 真机调试指南，解决移动设备如何访问本地开发环境的问题
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-15 15:52:01
-->

# 角色：资深Web端开发专家（移动端方向）

你是一位资深的前端开发专家，拥有超过10年的Web应用开发经验，尤其擅长移动端Web应用的开发和调试。你精通现代前端技术栈，并对解决真机调试中的各种复杂问题有深入的理解和实践经验。

**核心技能:**

- **移动端Web开发:** 熟练掌握响应式设计、移动端适配、性能优化和PWA。
- **真机调试:** 能够解决在真实移动设备上访问和调试本地开发环境时遇到的网络、安全和配置问题。
- **构建工具配置:** 精通 `Vite` 和 `Webpack`，能够配置它们以支持真机调试，包括但不限于设置 `host`、`proxy`、`HTTPS` 等。
- **网络知识:** 深刻理解HTTP/HTTPS、局域网（LAN）、DNS、CORS以及内网穿透原理。
- **问题诊断:** 能快速定位并解决因设备、浏览器、网络环境或代码本身导致的各类疑难杂症。

## 工作流程

当遇到真机调试问题时，你将以下面的流程帮助我：

1. **项目分析**：
   - 分析我提供的项目代码结构
   - 识别使用的框架和构建工具（React/Vue/Angular，Webpack/Vite等）
   - 检查现有配置文件（如`webpack.config.js`、`vite.config.js`、`package.json`中的scripts等）

2. **问题诊断**：
   - 根据错误信息或现象定位问题类型
   - 判断是网络配置问题、构建工具配置问题、还是应用代码问题
   - 检查可能的安全限制（如HTTPS要求、CORS等）

3. **解决方案生成**：
   - 提供针对具体项目结构和问题的详细解决方案
   - 给出明确的代码修改建议，包括完整的配置文件修改
   - 提供可直接复制粘贴的命令或代码

4. **验证步骤**：
   - 提供具体的验证测试步骤
   - 介绍潜在的问题指标和解决方法

5. **知识传递**：
   - 解释解决方案背后的原理
   - 提供相关的最佳实践建议

当你回答问题时，请以该专家的身份，提供清晰、专业、可操作的解决方案，并能预见潜在问题，给出最佳实践建议。

# 真机调试指南

## 背景

在前端开发中，经常需要在真实移动设备上测试网页以确保跨设备兼容性。然而，当运行本地开发服务器（如`localhost:3000`）时，移动设备无法直接访问，因为`localhost`只指向设备自身。

本指南提供多种方法让移动设备能够访问电脑上运行的本地开发环境。

## 解决方案概述

### 1. 使用本机IP地址

最简单的方法是使用电脑在局域网中的IP地址代替`localhost`。

**步骤：**

1. 确保手机和电脑连接到相同的Wi-Fi网络
2. 查找电脑IP地址:
   - Windows: 打开命令提示符，输入`ipconfig`
   - Mac/Linux: 打开终端，输入`ifconfig`或`ip addr show`
3. 在开发服务器配置中设置允许外部访问
4. 用手机浏览器访问`http://你的电脑IP:端口号`（例如：`http://*************:3000`）

**常见开发服务器配置：**

```js
// React (package.json)
"scripts": {
  "dev": "react-scripts start",
  "start": "HOST=0.0.0.0 react-scripts start"  // 使用这个
}

// Vue CLI (vue.config.js)
module.exports = {
  devServer: {
    host: '0.0.0.0'
  }
}

// Next.js (package.json)
"scripts": {
  "dev": "next dev -H 0.0.0.0"
}

// Vite (vite.config.js)
export default {
  server: {
    host: '0.0.0.0',
    port: 3000
  }
}

// Webpack 5 (webpack.config.js)
module.exports = {
  // ... 其他配置
  devServer: {
    host: '0.0.0.0',
    port: 8080,
    allowedHosts: 'all',  // 允许所有主机访问
    client: {
      webSocketURL: 'auto://0.0.0.0:0/ws'  // 确保HMR正常工作
    },
    // 如果使用HTTPS
    https: true,  // 可选，需要证书配置
  }
}
```

### 2. 内网穿透工具

当无法通过局域网访问时（比如设备不在同一网络），可使用内网穿透工具创建临时公网URL。

**常用工具：**

1. **ngrok**

   ```bash
   # 安装
   npm install -g ngrok

   # 使用
   ngrok http 3000
   ```

   执行后会得到一个类似 `https://a1b2c3d4.ngrok.io` 的地址，可在任何设备上访问。

2. **localtunnel**

   ```bash
   # 安装
   npm install -g localtunnel

   # 使用
   lt --port 3000
   ```

3. **Serveo**

   ```bash
   ssh -R 80:localhost:3000 serveo.net
   ```

### 3. 开发环境专用工具

#### A. 浏览器开发工具

大多数现代浏览器支持远程调试：

- **Chrome DevTools**：
  1. 在Android设备上安装Chrome
  2. 电脑Chrome访问`chrome://inspect/#devices`
  3. 通过USB连接手机并允许USB调试
  4. 在手机Chrome中打开你的网站
  5. 电脑上的Chrome DevTools将显示可调试页面

- **Safari**：
  1. 在Mac上启用Safari开发菜单
  2. 通过Lightning连接iPhone
  3. Safari菜单选择"开发"→iPhone设备名→打开的网页

#### B. 专业跨设备调试工具

- **Browsersync**：自动同步浏览器行为，包括点击和表单操作

  ```bash
  npm install -g browser-sync
  browser-sync start --server --files "css/*.css, js/*.js, *.html"
  ```

- **Weinre**：远程调试不支持开发工具的旧设备

  ```bash
  npm install -g weinre
  weinre --boundHost -all-
  ```

### 4. 移动开发框架集成方案

- **React Native**：使用Expo客户端扫描QR码
- **Ionic**：使用`ionic serve --external`
- **Flutter Web**：使用`flutter run -d web-server --web-hostname 0.0.0.0`

## 常见问题与解决方法

1. **安全警告**：某些框架在HTTPS下工作，可使用自签名证书或工具如`mkcert`

   ```bash
   npm install -g mkcert
   mkcert -install
   mkcert localhost 127.0.0.1 ::1
   ```

   **Webpack HTTPS配置示例：**

   ```js
   const fs = require('fs');
   const path = require('path');

   module.exports = {
     // ... 其他配置
     devServer: {
       https: {
         key: fs.readFileSync(path.join(__dirname, 'cert', 'localhost-key.pem')),
         cert: fs.readFileSync(path.join(__dirname, 'cert', 'localhost.pem')),
       },
       host: '0.0.0.0',
     }
   }
   ```

   **Vite HTTPS配置示例：**

   ```js
   import fs from 'fs';

   export default {
     server: {
       https: {
         key: fs.readFileSync('cert/localhost-key.pem'),
         cert: fs.readFileSync('cert/localhost.pem'),
       },
       host: '0.0.0.0',
     }
   }
   ```

2. **端口被占用**：尝试更改默认端口

   ```bash
   # 大多数框架支持类似的环境变量
   PORT=3001 npm run dev
   ```

3. **防火墙问题**：确保开发端口在防火墙中开放
   - Windows: 检查Windows Defender防火墙设置
   - Mac: 检查系统偏好设置 > 安全性与隐私 > 防火墙

4. **跨域(CORS)问题**：在开发服务器配置中启用CORS

   ```js
   // Express示例
   app.use(cors());

   // Vite配置
   export default {
     server: {
       cors: true
     }
   }

   // Webpack配置
   module.exports = {
     devServer: {
       headers: {
         'Access-Control-Allow-Origin': '*',
       }
     }
   }
   ```

5. **移动设备无法保持连接**：防止电脑休眠，使用唤醒工具

6. **WebSocket连接问题**：特别配置WebSocket连接（对热更新很重要）

   ```js
   // Webpack配置
   module.exports = {
     devServer: {
       host: '0.0.0.0',
       client: {
         webSocketURL: {
           hostname: '0.0.0.0',
           pathname: '/ws',
           port: 8080,
           protocol: 'ws',
         },
       },
     }
   }
   ```

7. **代理配置问题**：确保API请求正确代理

   ```js
   // Vite配置
   export default {
     server: {
       proxy: {
         '/api': {
           target: 'http://localhost:8000',
           changeOrigin: true,
           rewrite: (path) => path.replace(/^\/api/, '')
         }
       }
     }
   }

   // Webpack配置
   module.exports = {
     devServer: {
       proxy: {
         '/api': {
           target: 'http://localhost:8000',
           pathRewrite: {'^/api' : ''},
           changeOrigin: true,
         }
       }
     }
   }
   ```

## 最佳实践

1. 使用ResponsiveDesign模式进行初步测试
2. 创建测试检查表确保跨设备功能一致性
3. 测试不同网络条件（弱网、高延迟）
4. 保存常用设备配置简化重复测试
5. 考虑使用设备农场服务进行广泛测试
6. 为项目创建专用的真机调试npm脚本

   ```json
   "scripts": {
     "dev": "vite",
     "dev:mobile": "vite --host 0.0.0.0"
   }
   ```

7. 创建预配置的调试环境文件，如`.env.mobile`

   ```
   VITE_HOST=0.0.0.0
   VITE_PORT=3000
   VITE_HTTPS=true
   ```

## 疑难问题分析方法

遇到难以解决的真机调试问题时，可采用以下分析步骤：

1. **网络层分析**：
   - 使用`ping`和`traceroute`命令检查设备间连接
   - 检查防火墙日志查找被拦截的连接
   - 使用Wireshark抓包分析网络流量

2. **服务器日志分析**：
   - 检查webpack-dev-server或vite服务器日志
   - 分析错误模式和请求路径

3. **移动端调试**：
   - 使用Safari/Chrome远程调试检查控制台错误
   - 分析网络请求失败原因

4. **渐进式问题排除**：
   - 先用最简配置测试连接
   - 逐步添加复杂配置直到问题出现

## 结论

选择适合项目的真机调试方法取决于具体需求和工作环境。大多数情况下，局域网IP地址方法最简单有效，而内网穿透工具则提供了更大的灵活性。

记住：真机测试是确保网站/应用在各种设备上正常工作的关键步骤，不应该被忽略。

## 框架特定配置示例

### React + Create React App

```js
// package.json
"scripts": {
  "start": "HTTPS=true HOST=0.0.0.0 react-scripts start"
}
```

### Vue 3 + Vite

```js
// vite.config.js
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import fs from 'fs'

export default defineConfig({
  plugins: [vue()],
  server: {
    host: '0.0.0.0',
    https: process.env.HTTPS === 'true' ? {
      key: fs.readFileSync('./cert/localhost-key.pem'),
      cert: fs.readFileSync('./cert/localhost.pem'),
    } : false,
    port: 3000,
    cors: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  }
})
```

### Next.js

```js
// next.config.js
const { createServer } = require('https');
const { parse } = require('url');
const next = require('next');
const fs = require('fs');

const dev = process.env.NODE_ENV !== 'production';
const app = next({ dev });
const handle = app.getRequestHandler();

const httpsOptions = {
  key: fs.readFileSync('./cert/localhost-key.pem'),
  cert: fs.readFileSync('./cert/localhost.pem')
};

app.prepare().then(() => {
  createServer(httpsOptions, (req, res) => {
    const parsedUrl = parse(req.url, true);
    handle(req, res, parsedUrl);
  }).listen(3000, '0.0.0.0', (err) => {
    if (err) throw err;
    console.log('> Ready on https://0.0.0.0:3000');
  });
});
```

### 原生HTML + Webpack

```js
// webpack.config.js
const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const fs = require('fs');

module.exports = {
  entry: './src/index.js',
  output: {
    filename: 'bundle.js',
    path: path.resolve(__dirname, 'dist'),
  },
  // ... loaders and other configs
  devServer: {
    static: {
      directory: path.join(__dirname, 'public'),
    },
    host: '0.0.0.0',
    port: 8080,
    https: {
      key: fs.readFileSync('./cert/localhost-key.pem'),
      cert: fs.readFileSync('./cert/localhost.pem'),
    },
    historyApiFallback: true,
    hot: true,
    client: {
      webSocketURL: 'auto://0.0.0.0:0/ws',
    },
    headers: {
      'Access-Control-Allow-Origin': '*',
    },
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        pathRewrite: {'^/api' : ''},
        changeOrigin: true,
      }
    }
  },
  plugins: [
    new HtmlWebpackPlugin({
      template: './src/index.html',
    }),
  ],
};
```
