<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-10 15:30:10
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-10 15:56:52
 * @FilePath     : /Afeng/翻译/印地语(hi-IN).md
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-10 15:30:10
-->

# hi-IN 印地语(印度)本地化翻译规则 (Hindi Localization Rules)

## 1. 基础规则

### 1.1 翻译文件路径规则

- 根据文件路径自动判断目标语言：
  - `hi-IN/` 目录 → 印地语翻译
  - `en-US/` 目录 → 英文内容
- 标准路径结构：

例如

  ```
  src/language/locales/
  ├── en-US.json    # 英文文案
  ├── hi-IN.json    # 印地语文案
  ```

### 1.2 文件格式规范

- 保持 JSON 格式有效性
- 确保键值对完整性
- 与英文文件保持相同的：
  - 字段名称
  - 字段顺序
  - 字段结构
- 翻译前必须对比同名的英文和印地语文件
- 按照文件名称的字母排序与英文文件逐个进行对比

### 1.3 变量处理规则

- 保留所有代码变量，如：`{conversationCount}`
- 保持变量大小写不变
- 变量空格处理：
  - 独立变量：前后加空格，如：`कुल {count} संदेश`
  - 人名变量：根据语境处理，如：`{userName} जी`

## 2. 印地语语言规范

### 2.1 语体系统

- 对用户的提示：使用敬语（जी, आप）
  - 英文：`"Please check your email"`
  - 印地语：`"कृपया अपना ईमेल चेक करें"`
- 系统状态提示：使用简洁表达
  - 英文：`"Loading..."`
  - 印地语：`"लोड हो रहा है..."`
- 错误信息：使用完整的句式
  - 英文：`"Invalid email format"`
  - 印地语：`"ईमेल का प्रारूप गलत है"`

### 2.2 动词变位规则

- 现在时：रहा/रही/रहे + है/हैं
- 过去时：था/थी/थे
- 未来时：गा/गी/गे
- 命令式：ए/ो/िए

示例：

```json
{
  "MESSAGE": {
    "SENT": "संदेश भेजा गया है",
    "RECEIVED": "नया संदेश प्राप्त हुआ",
    "LOCATION": "मुंबई से संदेश भेजा गया",
    "TOOL": "सिस्टम द्वारा प्रोसेस किया गया"
  }
}
```

### 2.3 疑问和确认表达

- क्या：一般疑问
- जी हाँ：礼貌确认
- ठीक है：同意
- कृपया：请

示例：

```json
{
  "CONFIRM": {
    "SAVE": "क्या सेव करना है?",
    "INFO": "सेटिंग्स अपडेट हो गई हैं",
    "CHECK": "देखते हैं"
  }
}
```

## 3. 格式规范

### 3.1 标点符号规则

- 印地语标点符号：
  - 句号：।
  - 逗号：,
  - 括号：( )
  - 引号：" "
  - 问号：?
  - 感叹号：!
- 半角符号使用场景：
  - 英数字
  - 变量
  - URL/邮箱地址
- 混排规则：
  - 印地语和英文之间加空格
  - 印地语和数字之间加空格
  - 数字和单位之间加空格

### 3.2 数字表达规则

- 数量单位：
  - 一般数量：संख्या/गिनती
  - 人数：लोग/व्यक्ति
  - 时间：मिनट, घंटा, दिन
- 数字使用规则：
  - 1-10：可使用印地语数字（एक, दो, तीन等）
  - 10以上：使用阿拉伯数字
- 示例：

  ```json
  {
    "COUNT": {
      "MESSAGES": "{count} संदेश",
      "USERS": "{count} उपयोगकर्ता",
      "TIME": "{minutes} मिनट"
    }
  }
  ```

## 4. 专业术语规范

### 4.1 系统操作术语

```json
{
  "SYSTEM": {
    "LOGIN": "लॉगिन",
    "LOGOUT": "लॉगआउट",
    "SETTINGS": "सेटिंग्स",
    "DASHBOARD": "डैशबोर्ड",
    "PROFILE": "प्रोफाइल",
    "NOTIFICATION": "सूचना"
  }
}
```

### 4.2 状态术语

```json
{
  "STATUS": {
    "ONLINE": "ऑनलाइन",
    "OFFLINE": "ऑफलाइन",
    "BUSY": "व्यस्त",
    "AWAY": "दूर",
    "AVAILABLE": "उपलब्ध"
  }
}
```

### 4.3 UI要素术语

```json
{
  "UI": {
    "SAVE": "सेव करें",
    "CANCEL": "रद्द करें",
    "DELETE": "हटाएं",
    "EDIT": "संपादित करें",
    "UPDATE": "अपडेट करें",
    "LOADING": "लोड हो रहा है",
    "PROCESSING": "प्रोसेसिंग",
    "PLEASE_WAIT": "कृपया प्रतीक्षा करें",
    "COMPLETED": "पूरा हुआ"
  }
}
```

## 5. 特殊处理规则

### 5.1 品牌名称处理

- "BetFugu" 保持不变（保持英文）
- "Jackpot" 可翻译为 "जैकपॉट" 或保持英文
- 技术术语保持不变：API、URL、Email等
- 其他品牌名称保持原文：Facebook、Google、Apple等

### 5.2 语序结构转换

1. 时间地点在前：
   - 英文：`"Message sent at {time} in {location}"`
   - 印地语：`"{time} बजे {location} में संदेश भेजा गया"`

2. 动作主体和接受者：
   - 英文：`"Assigned to {assignee} by {user}"`
   - 印地语：`"{user} द्वारा {assignee} को असाइन किया गया"`

3. 状态变化描述：
   - 英文：`"Status changed from {old} to {new}"`
   - 印地语：`"स्थिति {old} से {new} में बदल गई"`

## 6. 常见错误示例

### 6.1 错误的翻译示例

```json
{
  "WRONG": {
    "SPACING": "सिस्टमऑनलाइनहै",           // 错误：缺少空格
    "CORRECT": "सिस्टम ऑनलाइन है",        // 正确：适当的空格

    "GRAMMAR": "सेटिंग बदलेगा",            // 错误：语法错误
    "CORRECT": "सेटिंग बदल जाएगी",        // 正确：正确的语法

    "POLITENESS": "पासवर्ड बदलो",          // 错误：缺少礼貌用语
    "CORRECT": "कृपया पासवर्ड बदलें"      // 正确：使用礼貌用语
  }
}
```

### 6.2 常见错误修正

1. 变量顺序错误：
   - 错误：`"{assignee} को {team} में {user} असाइन किया"`
   - 正确：`"{user} द्वारा {assignee} को {team} में असाइन किया गया"`

2. 礼貌用语使用错误：
   - 错误：`"पासवर्ड बदला गया"`
   - 正确：`"पासवर्ड अपडेट हो गया है"`

3. 语序结构错误：
   - 错误：`"एरर हुआ: डेटाबेस कनेक्शन"`
   - 正确：`"डेटाबेस कनेक्शन में समस्या हुई"`

## 7. 印地语特殊语法规则

### 7.1 格变化（Case System）

- 主格 (Nominative): ने, को, से, में, पर, का/की/के
- 宾格 (Accusative): को
- 工具格 (Instrumental): से
- 处所格 (Locative): में, पर

### 7.2 敬语系统

- आप (formal you)
- तुम (informal you)
- जी (respectful particle)
- साहब/साहिब (sir/madam)

### 7.3 常用连接词

- और - 和
- या - 或者
- लेकिन - 但是
- इसलिए - 所以
- क्योंकि - 因为

## 8. UI长度控制规则

### 8.1 文案长度要求

- **核心原则**：翻译文案长度应与英文原文保持基本一致
- **目标**：避免因文案长度差异导致的界面布局错乱
- **控制范围**：所有UI可见文案，特别是按钮、标签、标题等

### 8.2 长度控制策略

1. **按钮文案**：
   - 优先使用简洁表达
   - 英文：`"Save"` → 印地语：`"सेव"` (使用常见缩写)
   - 英文：`"Cancel"` → 印地语：`"रद्द"`
   - 英文：`"Delete"` → 印地语：`"हटाएं"`

2. **技术术语混合使用**：
   - 系统术语：Login, Settings, Profile (保持英文或使用简短印地语)
   - 状态术语：Online, Offline, Loading (可译为：ऑनलाइन, ऑफलाइन, लोडिंग)
   - 操作术语：Update, Refresh, Connect, Sync

3. **标题和标签**：
   - 使用缩写或简化表达
   - 避免冗长的解释性文字
   - 保持核心信息完整

### 8.3 长度检查标准

- **字符数控制**：印地语翻译不应超过英文原文字符数的130%（考虑到印地语的复合词特性）
- **视觉宽度**：考虑देवनागरी文字的视觉宽度
- **容器适配**：确保翻译文案能在原UI容器中正常显示

### 8.4 特殊情况处理

1. **必要的长翻译**：
   - 使用省略号 "..." 处理超长文案
   - 分行显示（如果UI支持）
   - 使用工具提示显示完整信息

2. **缩写策略**：
   - 使用常见的印地语缩写
   - 保持关键信息不丢失
   - 确保用户理解无障碍

3. **混合策略**：
   - 关键词使用英文，描述使用印地语
   - 品牌名称保持英文
   - 技术术语保持英文或使用简短音译

### 8.5 质量检查清单

- [ ] 翻译文案长度在合理范围内
- [ ] 在不同屏幕尺寸下UI显示正常
- [ ] 按钮文字未被截断
- [ ] 标题在容器内完整显示
- [ ] 关键信息未丢失
- [ ] 用户体验未受影响
- [ ] 德韦納格里文字显示正确
- [ ] 敬语使用恰当

## 9. 印地语特有考虑事项

### 9.1 德韦納格里文字特点

- 从左到右书写
- 字符上方有横线连接
- 复合字符较多
- 元音记号可能影响布局

### 9.2 文化适应性

- 尊重印度文化背景
- 考虑宗教敏感词汇
- 使用适当的敬语
- 避免文化冲突

### 9.3 本地化特殊要求

- 货币：₹ (印度卢比)
- 日期格式：DD/MM/YYYY
- 数字系统：使用阿拉伯数字
- 时间格式：24小时制或12小时制（根据用户偏好）
