# 印尼语本地化翻译规则 (Indonesian Localization Rules)

## 1. 基础规则

### 1.1 翻译文件路径规则

- 根据文件路径自动判断目标语言：
  - `id-ID/` 目录 → 印尼语翻译
  - `en-US/` 目录 → 英文内容
- 标准路径结构：
 例如

  ```
  src/language/locales/
  ├── en-US.json    # 英文文案
  ├── id-ID.json    # 印尼语文案
  ```

### 1.2 文件格式规范

- 保持 JSON 格式有效性
- 确保键值对完整性
- 与英文文件保持相同的：
  - 字段名称
  - 字段顺序
  - 字段结构
- 翻译前必须对比同名的英文和印尼语文件
- 按照文件名称的字母排序与英文文件逐个进行对比

### 1.3 变量处理规则

- 保留所有代码变量，如：`{conversationCount}`
- 保持变量大小写不变
- 变量空格处理：
  - 独立变量：前后加空格，如：`Total {count} pesan`
  - 人名变量：根据语境处理，如：`{userName} telah`

## 2. 印尼语语言规范

### 2.1 语体系统

- 对用户的提示：使用礼貌用语（silakan, mohon）
  - 英文：`"Please check your email"`
  - 印尼语：`"Silakan periksa email Anda"`
- 系统状态提示：使用简洁表达
  - 英文：`"Loading..."`
  - 印尼语：`"Memuat..."`
- 错误信息：使用完整的句式
  - 英文：`"Invalid email format"`
  - 印尼语：`"Format email tidak valid"`

### 2.2 动词时态规则

- 现在时：使用动词原形或加前缀me-
- 过去时：使用sudah/telah + 动词
- 将来时：使用akan + 动词
- 被动语态：使用di- + 动词

示例：

```json
{
  "MESSAGE": {
    "SENT": "Pesan telah dikirim",
    "RECEIVED": "Pesan baru diterima",
    "LOCATION": "Pesan dikirim dari Jakarta",
    "TOOL": "Diproses oleh sistem"
  }
}
```

### 2.3 疑问和确认表达

- Apakah：一般疑问词
- Ya/Tidak：是/否
- Baik：好的，同意
- Mohon：请

示例：

```json
{
  "CONFIRM": {
    "SAVE": "Apakah ingin menyimpan?",
    "INFO": "Pengaturan telah diperbarui",
    "CHECK": "Mari periksa"
  }
}
```

## 3. 格式规范

### 3.1 标点符号规则

- 印尼语标点符号：
  - 句号：.
  - 逗号：,
  - 括号：( )
  - 引号：" "
  - 问号：?
  - 感叹号：!
- 半角符号使用场景：
  - 英数字
  - 变量
  - URL/邮箱地址
- 混排规则：
  - 印尼语和英文之间加空格
  - 印尼语和数字之间加空格
  - 数字和单位之间加空格

### 3.2 数字表达规则

- 数量单位：
  - 一般数量：jumlah/angka
  - 人数：orang/pengguna
  - 时间：menit, jam, hari
- 数字使用规则：
  - 1-10：可使用印尼语数字（satu, dua, tiga等）
  - 10以上：使用阿拉伯数字
- 示例：

  ```json
  {
    "COUNT": {
      "MESSAGES": "{count} pesan",
      "USERS": "{count} pengguna",
      "TIME": "{minutes} menit"
    }
  }
  ```

## 4. 专业术语规范

### 4.1 系统操作术语

```json
{
  "SYSTEM": {
    "LOGIN": "Masuk",
    "LOGOUT": "Keluar",
    "SETTINGS": "Pengaturan",
    "DASHBOARD": "Dasbor",
    "PROFILE": "Profil",
    "NOTIFICATION": "Notifikasi"
  }
}
```

### 4.2 状态术语

```json
{
  "STATUS": {
    "ONLINE": "Online",
    "OFFLINE": "Offline",
    "BUSY": "Sibuk",
    "AWAY": "Tidak ada",
    "AVAILABLE": "Tersedia"
  }
}
```

### 4.3 UI要素术语

```json
{
  "UI": {
    "SAVE": "Simpan",
    "CANCEL": "Batal",
    "DELETE": "Hapus",
    "EDIT": "Edit",
    "UPDATE": "Perbarui",
    "LOADING": "Memuat",
    "PROCESSING": "Memproses",
    "PLEASE_WAIT": "Harap tunggu",
    "COMPLETED": "Selesai"
  }
}
```

## 5. 特殊处理规则

### 5.1 品牌名称处理

- "BetFugu" 保持不变（保持英文）
- "Jackpot" 可翻译为 "Jackpot" 或保持英文
- 技术术语保持不变：API、URL、Email等
- 其他品牌名称保持原文：Facebook、Google、Apple等

### 5.2 语序结构转换

印尼语基本语序：

- 主谓宾结构（SVO）：与英语相似
- 修饰语通常在被修饰词之后
- 形容词一般放在名词后面

语序转换示例：

```json
{
  "EXAMPLE": {
    "ENGLISH": "New message",
    "INDONESIAN": "Pesan baru"
  }
}
```

## 6. 常见错误示例

### 6.1 错误翻译

❌ 错误示例：

```json
{
  "LOGIN": "Masuk ke dalam",  // 过于冗长
  "SAVE": "Menyimpan",       // 使用动名词而非动词
  "ERROR": "Kesalahan terjadi"  // 语序不自然
}
```

✅ 正确翻译：

```json
{
  "LOGIN": "Masuk",
  "SAVE": "Simpan",
  "ERROR": "Terjadi kesalahan"
}
```

### 6.2 格式错误修正

❌ 错误格式：

- 变量处理：`Total{count}pesan` → `Total {count} pesan`
- 标点符号：`Apakah anda yakin。` → `Apakah Anda yakin?`
- 大小写：`anda` → `Anda`（敬语）

## 7. 印尼语特殊语法规则

### 7.1 敬语使用

- 使用"Anda"而不是"kamu"表示尊重
- 使用"Bapak/Ibu"称呼表示更高敬意
- 商务环境中优先使用正式用语

### 7.2 前缀后缀系统

- me-：主动语态前缀
- di-：被动语态前缀
- -kan：使役后缀
- -an：名词化后缀

示例：

```json
{
  "VERBS": {
    "SEND": "kirim → mengirim → dikirim → kirimkan",
    "SAVE": "simpan → menyimpan → disimpan → simpankan"
  }
}
```

### 7.3 重叠词使用

- 表示复数：orang-orang（人们）
- 表示强调：baik-baik（好好地）
- 在UI翻译中谨慎使用，避免冗长

## 8. UI长度控制规则

### 8.1 文案长度要求

- **核心原则**：翻译文案长度应与英文原文保持基本一致
- **目标**：避免因文案长度差异导致的界面布局错乱
- **控制范围**：所有UI可见文案，特别是按钮、标签、标题等

### 8.2 长度控制策略

1. **按钮文案**：优先使用简洁表达
   - `"SAVE"` → `"Simpan"` 而非 `"Menyimpan"`
   - `"LOGIN"` → `"Masuk"` 而非 `"Masuk ke dalam"`

2. **技术术语保持英文**：
   - 系统术语：Login, Dashboard, Profile
   - 状态术语：Online, Offline
   - 操作术语：根据常用性决定

3. **标题和标签**：使用缩写或简化表达
   - 避免冗长的完整句式
   - 优先使用名词而非动词短语

### 8.3 长度检查标准

- **字符数控制**：印尼语翻译不应超过英文原文字符数的125%
- **视觉宽度**：考虑印尼语单词平均长度较英文略长
- **容器适配**：确保在不同屏幕尺寸下正常显示

### 8.4 特殊情况处理

1. **必要的长翻译**：
   - 使用换行符合理分割
   - 采用缩写策略
   - 考虑上下文提示

2. **缩写策略**：
   - 保留关键信息词
   - 省略冗余修饰词
   - 使用常见缩写形式

3. **混合策略**：
   - 技术词汇保持英文
   - 动作词汇使用印尼语
   - 根据用户习惯调整

### 8.5 质量检查清单

翻译完成后进行以下检查：

- [ ] 字符数是否在控制范围内（≤125%）
- [ ] 在不同设备上显示是否正常
- [ ] 是否保持了原文的核心信息
- [ ] 用户理解是否无障碍
- [ ] 是否符合印尼语表达习惯
- [ ] 按钮和标签是否在容器内正常显示
- [ ] 响应式布局是否受到影响

## 9. 印尼语特有考虑事项

### 9.1 地域差异

- 印尼语在不同地区可能有细微差异
- 优先使用标准印尼语（Bahasa Indonesia Baku）
- 避免使用过于地方化的表达

### 9.2 文化适应性

- 考虑印尼的宗教文化背景
- 使用适当的礼貌用语
- 避免可能引起误解的表达

### 9.3 技术术语本地化

- 平衡本地化和国际化需求
- 游戏术语优先保持英文
- 系统功能词汇适度本地化

### 9.4 用户体验优化

- 符合印尼用户的使用习惯
- 保持界面的直观性
- 确保翻译的一致性和准确性
