# 阿拉伯语本地化翻译规则 (Arabic Localization Rules)

## 1. 基础规则

### 1.1 翻译文件路径规则

- 根据文件路径自动判断目标语言：
  - `ar/` 目录 → 阿拉伯语翻译
  - `en/` 目录 → 英文内容
- 标准路径结构：

  ```
  app/javascript/dashboard/i18n/locale/
  ├── en/    # 英文文案
  ├── ar/    # 阿拉伯语文案
  ```

### 1.2 文件格式规范

- 保持 JSON 格式有效性
- 确保键值对完整性
- 与英文文件保持相同的：
  - 字段名称
  - 字段顺序
  - 字段结构
- 翻译前必须对比同名的英文和阿拉伯语文件

### 1.3 变量处理规则

- 保留所有代码变量，如：`{conversationCount}`
- 保持变量大小写不变
- 变量空格处理：
  - 独立变量：前后加空格，如：`مجموع {count} عنصر`
  - 词组变量：无需空格，如：`{userName}المحترم`

## 2. 阿拉伯语语言规范

### 2.1 文本方向

- 阿拉伯语是从右到左(RTL)的语言
- 确保所有文本正确显示方向
- 数字仍然从左到右显示

### 2.2 语法结构

- 名词-形容词顺序：阿拉伯语中形容词通常跟在名词后面
  - 英文：`"New Message"`
  - 阿拉伯语：`"رسالة جديدة"` (字面意思：消息 新的)

- 动词-主语-宾语顺序：阿拉伯语通常使用动词-主语-宾语(VSO)结构
  - 英文：`"User sent a message"`
  - 阿拉伯语：`"أرسل المستخدم رسالة"` (字面意思：发送了 用户 消息)

### 2.3 性别和数量

- 阿拉伯语有阳性和阴性形式
- 数量形式：单数、双数和复数
- 翻译时需考虑目标对象的性别和数量

示例：

```json
{
  "MESSAGE": {
    "SENT_MALE": "أرسل رسالة",
    "SENT_FEMALE": "أرسلت رسالة",
    "RECEIVED_SINGULAR": "استلمت رسالة واحدة",
    "RECEIVED_DUAL": "استلمت رسالتين",
    "RECEIVED_PLURAL": "استلمت {count} رسائل"
  }
}
```

## 3. 格式规范

### 3.1 标点符号规则

- 阿拉伯语使用特定的标点符号：
  - 逗号：، (从右到左逗号)
  - 分号：؛
  - 问号：؟
  - 感叹号：!
- 数字和拉丁字符保持从左到右的顺序
- 混合文本中的方向处理：
  - 使用Unicode方向标记确保正确显示
  - 阿拉伯语和英文/数字混合时注意方向性

### 3.2 数字表达规则

- 阿拉伯语地区通常使用印度-阿拉伯数字系统（0, 1, 2, 3...）而非阿拉伯数字（٠, ١, ٢, ٣...）
- 数量表达：
  - 1-10：可使用阿拉伯语数词（واحد، اثنان等）
  - 10以上：通常使用数字

示例：

```json
{
  "COUNT": {
    "MESSAGES": "{count} رسالة",
    "USERS": "{count} مستخدم",
    "TIME": "{minutes} دقيقة"
  }
}
```

## 4. 专业术语规范

### 4.1 系统操作术语

```json
{
  "SYSTEM": {
    "LOGIN": "تسجيل الدخول",
    "LOGOUT": "تسجيل الخروج",
    "SETTINGS": "الإعدادات",
    "DASHBOARD": "لوحة التحكم",
    "PROFILE": "الملف الشخصي",
    "NOTIFICATION": "الإشعارات"
  }
}
```

### 4.2 状态术语

```json
{
  "STATUS": {
    "ONLINE": "متصل",
    "OFFLINE": "غير متصل",
    "BUSY": "مشغول",
    "AWAY": "غير متواجد",
    "AVAILABLE": "متاح"
  }
}
```

### 4.3 UI要素术语

```json
{
  "UI": {
    "SAVE": "حفظ",
    "CANCEL": "إلغاء",
    "DELETE": "حذف",
    "EDIT": "تعديل",
    "UPDATE": "تحديث",
    "LOADING": "جاري التحميل",
    "PROCESSING": "جاري المعالجة",
    "PLEASE_WAIT": "يرجى الانتظار",
    "COMPLETED": "تم الإكمال"
  }
}
```

## 5. 特殊处理规则

### 5.1 品牌名称处理

- "Chatwoot" 改为 "Talka"（保持英文）
- "chatwoot" 改为 "talka"（保持英文）
- Webhook,webhook 不用翻译， 保持不变
- 其他品牌名称保持原文：Facebook、GitHub、Twitter等

### 5.2 语序结构转换

1. 时间地点表达：
   - 英文：`"Message sent at {time} in {location}"`
   - 阿拉伯语：`"تم إرسال الرسالة في {location} في {time}"`

2. 动作主体和接受者：
   - 英文：`"Assigned to {assignee} by {user}"`
   - 阿拉伯语：`"تم تعيينه إلى {assignee} بواسطة {user}"`

3. 状态变化描述：
   - 英文：`"Status changed from {old} to {new}"`
   - 阿拉伯语：`"تم تغيير الحالة من {old} إلى {new}"`

## 6. 常见错误示例

### 6.1 错误的翻译示例

```json
{
  "WRONG": {
    "DIRECTION": "النظام هو على الانترنت",      // 错误：未考虑RTL方向
    "CORRECT": "النظام متصل بالإنترنت",         // 正确：考虑RTL布局

    "GENDER": "المستخدم أرسلت رسالة",         // 错误：性别不匹配（阳性名词用了阴性动词）
    "CORRECT": "المستخدم أرسل رسالة",         // 正确：性别匹配

    "NUMBER": "لديك 5 رسالة جديدة",          // 错误：数量不匹配（5与单数形式）
    "CORRECT": "لديك 5 رسائل جديدة"          // 正确：数量匹配（5与复数形式）
  }
}
```

### 6.2 常见错误修正

1. 方向性错误：
   - 错误：`"Click هنا to continue"`
   - 正确：`"انقر هنا للمتابعة"`

2. 性别匹配错误：
   - 错误：`"الصفحة غير موجود"` (阴性名词用了阳性形容词)
   - 正确：`"الصفحة غير موجودة"` (阴性名词用阴性形容词)

3. 数量匹配错误：
   - 错误：`"مستخدمان جديد"` (双数名词用了单数形容词)
   - 正确：`"مستخدمان جديدان"` (双数名词用双数形容词)

### 6.3 文化适应性

- 日期格式：阿拉伯国家通常使用日/月/年格式
- 时间格式：可使用12小时或24小时制
- 周起始日：阿拉伯国家通常以周六为一周的第一天
- 避免使用宗教或政治敏感词汇
- 图像和图标需考虑文化适应性
