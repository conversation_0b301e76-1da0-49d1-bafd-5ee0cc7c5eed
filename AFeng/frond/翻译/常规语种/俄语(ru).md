<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-15 10:33:46
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-15 10:33:49
 * @FilePath     : /tools-apps/cursor/roles/frond/翻译/俄语(ru).md
 * @Description  : Правила интернационализации для русского языка
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-15 10:33:46
-->

# 俄语(ru)国际化翻译规则

请按照以下规则翻译这些文件中的文本内容：

## 基本规则

1. 自动检测俄语类型：
   - 根据文件路径确定目标俄语变体
   - ru_RU/ 或 ru-RU/ 目录 → 俄语(俄罗斯)
   - ru_BY/ 或 ru-BY/ 目录 → 俄语(白俄罗斯)
   - ru_KZ/ 或 ru-KZ/ 目录 → 俄语(哈萨克斯坦)
   - ru_UA/ 或 ru-UA/ 目录 → 俄语(乌克兰)

2. 默认行为：
   - 如果用户没有明确指定俄语变体
   - 默认使用俄罗斯俄语(ru_RU)
   - 根据地区特点调整翻译

3. 文件结构示例：

   ```
   app/javascript/dashboard/i18n/locale/
   ├── en/      # 英文内容
   ├── ru_RU/   # 俄语(俄罗斯)内容
   └── ru_BY/   # 俄语(白俄罗斯)内容
   ```

## 基础翻译规则

1. 内容转换
   - 将英文文本翻译成俄语
   - 保留代码变量，如 `{conversationCount}`
   - 保持现有俄语文本不变
   - 保持数字不变

2. JSON格式标准
   - 保持JSON格式有效性
   - 确保键值对完整性
   - 保持字段名称、顺序和结构与源文件相同

3. 专有名词保留
   - 保持品牌名称不变：Facebook、Github、Twitter、WhatsApp、Google、Telegram等

4. 标点符号规则
   - 俄语文本中使用俄语标点
   - 俄语引号：«文本»（不同于英文引号）
   - 混合语言间距规则：
     - 在俄语和英语之间添加空格
     - 在数字和俄语文本之间添加空格
     - 在数字和单位之间添加空格

   示例：
   错误：Это страница настройкиChat Widget,с5опциями.
   正确：Это страница настройки «Chat Widget», с 5 опциями.

5. 变量处理规则
   - 保持变量大小写敏感
   - 变量间距规则：
     - 独立变量周围添加空格
     - 短语中的变量不需要额外空格

   示例：
   - 独立变量：`У вас {count} новых сообщений`
   - 短语变量：`Настройки {userName}`

## 风格要求

1. 一致性
   - 与现有俄语翻译保持一致的语言风格
   - 使用统一的技术术语翻译
   - 保持语气和语调一致

2. 格式标准
   - 确保引号正确配对
   - 保持一致的缩进
   - 避免双引号内嵌套双引号

## 特殊处理

1. 文化差异注意事项
   - 避免文化敏感内容(宗教、政治等)
   - 处理数字格式差异(俄语使用逗号作为小数点)
   - 日期时间格式本地化(俄语使用DD.MM.YYYY格式)
   - 货币单位转换显示(俄罗斯使用₽符号)
   - 度量衡单位转换(使用公制)

2. 语法表达规范
   - 保持简洁直接的表达
   - 优先使用主动语态
   - 避免过长的复合句
   - 技术术语保持一致性
   - 符合目标语言地区的习惯用法

3. 文件比较要求
   - 翻译前比较英文和俄语文件
   - 添加缺失的俄语翻译字段
   - 确保字段顺序匹配
   - 保持字段名称相同

4. 语言逻辑差异

   ### 语法转换规则

   #### 1. 动作主体和接收者

   - 英语的"A assigned to B"结构在俄语中应转换为"B назначен A"或"A назначил B"
   - 保持变量语义角色：
     - 正确：`"{assignee} назначен пользователем {user}"`
     - 错误：`"{user} назначен пользователем {assignee}"`
   - 对于多个参与者，按照俄语逻辑组织词序
   - 确保翻译在保持原始语义的同时保持自然的俄语表达

   示例：
   英语：`"Assigned to {assignee} via {team} by {user}"`
   正确翻译：`"{assignee} назначен через {team} пользователем {user}"`
   错误翻译：`"Пользователем {user} назначен {assignee} через {team}"`

   #### 2. 时间表达转换

   - 将"month/day/year"转换为"day.month.year"
   - 将"AM/PM"转换为24小时制
   - 翻译星期几和月份名称

   示例：
   英语：`"Monday, December 25, 2023 at 3:00 PM"`
   俄语：`"Понедельник, 25 декабря 2023 г., 15:00"`

   #### 3. 地点描述顺序

   - 俄语中地点描述通常从大到小排列
   - 保持地址元素完整性

   示例：
   英语：`"Zhangjiang Hi-Tech Park, Pudong, Shanghai, China"`
   俄语：`"Китай, Шанхай, Пудун, Технопарк Чжанцзян"`

   #### 4. 格变化系统

   - 俄语有六种格(主格、属格、与格、宾格、工具格、前置格)
   - 根据上下文选择正确的格
   - 名词、形容词、代词需要根据性别、数量和格进行变化

   示例：
   英语：`"Send message to user"`
   俄语：`"Отправить сообщение пользователю"` (与格)

   #### 5. 动词体系转换

   - 俄语有完成体和未完成体
   - 根据动作是一次性还是重复性选择正确的体

   示例：
   英语：`"Update settings"` (命令)
   俄语完成体：`"Обновить настройки"` (一次性动作)
   俄语未完成体：`"Обновлять настройки"` (重复动作)

   #### 6. 数量表达转换

   - 将数量表达适应俄语习惯
   - 正确处理复数形式(俄语有三种复数形式)
   - 转换近似数值表达

   示例：
   英语：`"5 messages"`
   俄语：`"5 сообщений"` (2-4个用一种形式，5+用另一种)

   #### 7. 比较结构转换

   - 将英语比较结构转换为俄语等效结构
   - 调整最高级形式
   - 转换同级比较

   示例：
   英语：`"busier than yesterday"`
   俄语：`"загруженнее, чем вчера"`

   #### 8. 否定形式转换

   - 调整否定位置(俄语可以有多重否定)
   - 处理双重否定
   - 处理否定转移情况

   示例：
   英语：`"I don't see anyone"`
   俄语：`"Я никого не вижу"` (双重否定)

   #### 9. 习惯用语转换

   - 为英语习惯用语找到俄语等效表达
   - 保持语义而非字面翻译
   - 调整文化特定表达

   示例：
   英语：`"break the ice"`
   俄语：`"растопить лёд"`

## 专业术语词汇表

1. 常用技术术语翻译
   - Dashboard -> Панель управления
   - Widget -> Виджет
   - Conversation -> Диалог/Беседа
   - Agent -> Агент/Оператор
   - Inbox -> Входящие
   - Settings -> Настройки
   - User -> Пользователь
   - Message -> Сообщение
   - Notification -> Уведомление
   - Upload -> Загрузить
   - Download -> Скачать
   - Login -> Вход/Авторизация
   - Logout -> Выход

## 翻译示例

### 示例1：基本翻译

原文：

```json
{
  "CONVERSATION": {
    "you_have_new_message": "You have {count} new messages",
    "app_name": "Chatwoot"
  }
}
```

译文:

```json
{
  "CONVERSATION": {
    "you_have_new_message": "У вас {count} новых сообщений",
    "app_name": "Chatwoot"
  }
}
```

### 示例2：处理缺失字段

原英文文件：

```json
{
  "INBOX": {
    "SETTINGS": {
      "TITLE": "Inbox Settings",
      "UPDATE": {
        "SUCCESS": "Inbox settings updated successfully",
        "ERROR": "Couldn't update inbox settings",
        "RETRY": "Please try again"
      },
      "AUTO_ASSIGNMENT": {
        "ENABLED": "Enabled",
        "DISABLED": "Disabled"
      }
    }
  }
}
```

当前俄语文件：

```json
{
  "INBOX": {
    "SETTINGS": {
      "TITLE": "Настройки входящих",
      "UPDATE": {
        "SUCCESS": "Настройки входящих успешно обновлены",
        "ERROR": "Не удалось обновить настройки входящих"
      }
    }
  }
}
```

正确的俄语译文（补充缺失字段）：

```json
{
  "INBOX": {
    "SETTINGS": {
      "TITLE": "Настройки входящих",
      "UPDATE": {
        "SUCCESS": "Настройки входящих успешно обновлены",
        "ERROR": "Не удалось обновить настройки входящих",
        "RETRY": "Пожалуйста, попробуйте еще раз"
      },
      "AUTO_ASSIGNMENT": {
        "ENABLED": "Включено",
        "DISABLED": "Отключено"
      }
    }
  }
}
```

### 示例3：专业术语统一

原文：

```json
{
  "DASHBOARD": {
    "AGENT_INBOX": "Agent Inbox",
    "CONVERSATION_WIDGET": "Chat Widget Settings",
    "ACTIVE_CONVERSATIONS": "Active Conversations"
  }
}
```

译文:

```json
{
  "DASHBOARD": {
    "AGENT_INBOX": "Входящие агента",
    "CONVERSATION_WIDGET": "Настройки виджета чата",
    "ACTIVE_CONVERSATIONS": "Активные диалоги"
  }
}
```
