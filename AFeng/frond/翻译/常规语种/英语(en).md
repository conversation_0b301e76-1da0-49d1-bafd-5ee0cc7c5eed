国际化英文翻译

请翻译这些文件中的中文文案，翻译成英文， 规则如下:

# 翻译规则 (Translation Rules)

如果没有明确要求翻译为美式英文还是英式英文，则默认翻译为美式英文!

1. 自动判断翻译语言类型：
  根据文件路径自动判断源语言
  zh_CN/ 目录 → 中文简体内容
  zh_TW/ 目录 → 中文繁体内容
  en/ 目录 → 翻译为英文

2. 默认行为：
  如果用户没有明确指定要翻译成美式英文还是英式英文
  默认翻译为美式英文

3. 路径示例：
  app/javascript/dashboard/i18n/locale/
  ├── en/      # 英文文案
  ├── zh_CN/   # 中文简体文案
  └── zh_TW/   # 中文繁体文案

## 基本规则

1. 文案转换
   - 将中文文案翻译为英文
   - 保留代码变量，如：`{conversationCount}`
   - 已有英文文案保持不变
   - 数字保持不变

2. JSON 格式规范
   - 保持 JSON 格式有效性
   - 确保键值对完整
   - 保持字段名称,字段顺序,字段结构与中文文件中的字段完全一致

3. 专有名称不需翻译
  Facebook, Github, Twitter, WhatsApp, Google, Telegram 等等

4. 标点符号规则
  1. 英文文案中使用英文标点符号
  2. 中文单词周围保留中文标点
  3. 中英文混排时的空格处理规则
     - 中英文之间需要加空格
     - 英文与数字之间需要加空格
     - 数字与单位之间需要加空格
示例：
错误：This is a Chat Widget设置页面,with 5 options.
正确：This is a Chat Widget settings page, with 5 options.

5. 变量处理规则
  1. 保持变量大小写不变
  2. 变量前后的空格处理
     - 独立变量需要前后加空格
     - 作为词组一部分的变量不需要额外空格

示例：

- 独立变量：`There are {count} messages`
- 词组变量：`{userName}'s settings`

## 风格要求

1. 一致性
   - 与现有英文翻译保持一致的语言风格
   - 专业术语使用统一的英文译法
   - 保持语气和语调的一致性

2. 格式规范
   - 确保引号配对完整
   - 保持缩进一致性
   - 双引号内不允许嵌套双引号

## 特殊处理

1. 品牌名称
   - "Chatwoot" 统一替换为 "Talka"
  - "chatwoot" 统一替换为 "talka"
   - "Talka" 保持英文，不做翻译

2. 文件对比要求
   - 翻译前先对比同名的 中文，英文文件
   - 补充缺失的英文翻译字段
   - 确保英文文件中的字段顺序与中文文件中的字段顺序一致
   - 保持字段名完全相同

3. 注意区分中文和英文语言的逻辑

 ### 动作主体和接受者的翻译规则

 在处理包含动作执行者和接受者的系统消息时：

 1. 中文的 "由 B 分配给 A" 结构在英文中应转换为 "A assigned to B" 的结构
 2. 保持变量的语义角色：
    - 正确：`"Assigned to {assignee} by {user}"`
    - 错误：`"Assigned to {user} by {assignee}"`
 3. 多个参与者的情况下，按照动作发生的顺序组织英文语序
 4. 确保翻译后的文本符合英文的表达习惯，同时保持原文的语义准确性

 示例：
 中文：`"由 {user} 通过 {team} 分配给 {assignee}"`
 正确翻译：`"Assigned to {assignee} via {team} by {user}"`
 错误翻译：`"Assigned by {user} to {assignee} via {team}"`

 示例:
 assigned: "由 %{user_name} 分配给 %{assignee_name}"

 正确翻译:
  assigned: "Assigned to %{assignee_name} by %{user_name}"

 ### 中英文环境的语言逻辑转换规则

 1. 语序结构差异
    - 中文：强调时间、地点等状语前置，修饰语往往前置
    - 英文：主谓宾结构为主，修饰语往往后置
    示例：
    - 中文：`"在 %{inbox_name} 中创建了一个对话 (#%{display_id})"`
    - 英文：`"A conversation (#%{display_id}) has been created in %{inbox_name}"`

 2. 表达视角差异
    - 中文：倾向于以动作执行者为主语的主动表达
    - 英文：倾向于以动作接受者为主语的被动表达
    示例：
    - 中文：`"对话被 %{user_name} 标记为已解决"`
    - 英文：`"Conversation was marked resolved by %{user_name}"`

 3. 信息组织逻辑
    - 中文：先说过程，再说结果
    - 英文：先说结果，再说过程
    示例：
    - 中文：`"密码重置请求已成功。请检查您的邮件获取说明。"`
    - 英文：`"Request for password reset is successful. Check your mail for instructions."`

 4. 状态描述方式
    - 中文：偏好使用动词性短语
    - 英文：偏好使用形容词性短语
    示例：
    - 中文：`"在线中"`
    - 英文：`"is Online"`

 5. 语气表达差异
    - 中文：更委婉，常用建议式
    - 英文：更直接，常用命令式
    示例：
    - 中文：`"请留下联系方式以便团队联系您"`
    - 英文：`"Give the team a way to reach you"`

 6. 称谓逻辑
    - 中文：偏好使用名词或省略
    - 英文：偏好使用代词
    示例：
    - 中文：`"有 {count} 条新消息"`
    - 英文：`"You have {count} new messages"`

 7. 时态表达
    - 中文：通过上下文或时间词表达
    - 英文：通过动词变化表达时态
    示例：
    - 中文：`"已分配"`
    - 英文：`"has been assigned"`

 8. 数量表达
    - 中文：使用量词
    - 英文：区分单复数
    示例：
    - 中文：`"%{count} 条消息"`
    - 英文：`"%{count} messages"`

 9. 逻辑关系表达
    - 中文：常通过语序暗示
    - 英文：通过连接词明确表达
    示例：
    - 中文：`"对话 %{duration} 天未活动，已自动标记为已解决"`
    - 英文：`"The conversation is resolved because it was inactive for %{duration} days"`

 10. 系统提示语的人称处理
     - 中文：倾向使用敬语或省略人称
     - 英文：经常使用第二人称 (you)
     示例：
     - 中文：`"输入的邮箱地址无效"`
     - 英文：`"You have entered an invalid email"`

## 补充规则

	 1. 专业术语词汇表

- 控制台 -> Dashboard
- 小部件 -> Widget
- 对话 -> Conversation
- 客服 -> Agent
- 收件箱 -> Inbox

## 示例

### 示例1：基本翻译

原文：

{
 "CONVERSATION": {
  "you_have_new_message": "您有 {count} 条新消息",
  "app_name": "Talka"
 }
}

译文:

{
 "CONVERSATION": {
  "you_have_new_message": "You have {count} new messages",
  "app_name": "Talka"
 }
}

### 示例2：处理缺失字段

原中文文件：

{
 "INBOX": {
  "SETTINGS": {
   "TITLE": "收件箱设置",
   "UPDATE": {
    "SUCCESS": "收件箱设置更新成功",
    "ERROR": "无法更新收件箱设置"
    // 缺少 RETRY 字段
   }
  // 缺少 AUTO_ASSIGNMENT 部分
  }
 }
}

当前英文文件：
{
 "INBOX": {
  "SETTINGS": {
   "TITLE": "Inbox Settings",
   "UPDATE": {
    "SUCCESS": "Inbox settings updated successfully",
    "ERROR": "Couldn't update inbox settings",
    "RETRY": "Please try again"
   },
   "AUTO_ASSIGNMENT": {
    "ENABLED": "Enabled",
    "DISABLED": "Disabled"
   }
  }
 }
}

正确的英文译文（参考中文文件）：

{
 "INBOX": {
  "SETTINGS": {
   "TITLE": "Inbox Settings",
   "UPDATE": {
    "SUCCESS": "Inbox settings updated successfully",
    "ERROR": "Couldn't update inbox settings",
    "RETRY": "Please try again"
   },
   "AUTO_ASSIGNMENT": {
    "ENABLED": "Enabled",
    "DISABLED": "Disabled"
   }
  }
 }
}

### 示例3：专业术语统一

原文：

{
 "DASHBOARD": {
  "AGENT_INBOX": "客服收件箱",
  "CONVERSATION_WIDGET": "聊天小部件设置",
  "ACTIVE_CONVERSATIONS": "进行中的对话"
 }
}

译文:
{
 "DASHBOARD": {
  "AGENT_INBOX": "Agent Inbox",
  "CONVERSATION_WIDGET": "Chat Widget Settings",
  "ACTIVE_CONVERSATIONS": "Active Conversations"
 }
}
