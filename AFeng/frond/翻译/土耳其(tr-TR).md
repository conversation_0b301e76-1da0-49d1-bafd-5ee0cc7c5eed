# 土耳其语本地化翻译规则 (Turkish Localization Rules)

## 1. 基础规则

### 1.1 翻译文件路径规则

- 根据文件路径自动判断目标语言：
  - `tr-TR/` 目录 → 土耳其语翻译
  - `en-US/` 目录 → 英文内容
- 标准路径结构：
例如

  ```
  src/language/locales/
  ├── en-US.json    # 英文文案
  ├── tr-TR.json    # 土耳其语文案
  ```

### 1.2 文件格式规范

- 保持 JSON 格式有效性
- 确保键值对完整性
- 与英文文件保持相同的：
  - 字段名称
  - 字段顺序
  - 字段结构
- 翻译前必须对比同名的英文和土耳其语文件
- 按照文件名称的字母排序与英文文件逐个进行对比

### 1.3 变量处理规则

- 保留所有代码变量，如：`{conversationCount}`
- 保持变量大小写不变
- 变量空格处理：
  - 独立变量：前后加空格，如：`Toplam {count} mesaj`
  - 人名变量：根据语境处理，如：`{userName} gönderdi`

## 2. 土耳其语语言规范

### 2.1 语体系统

- 对用户的提示：使用礼貌用语（lütfen, rica ederim）
  - 英文：`"Please check your email"`
  - 土耳其语：`"Lütfen e-postanızı kontrol edin"`
- 系统状态提示：使用简洁表达
  - 英文：`"Loading..."`
  - 土耳其语：`"Yükleniyor..."`
- 错误信息：使用完整的句式
  - 英文：`"Invalid email format"`
  - 土耳其语：`"Geçersiz e-posta formatı"`

### 2.2 动词变位和时态规则

- 现在时：-yor 进行时后缀
- 过去时：-di/-dı/-du/-dü 过去时后缀
- 将来时：-ecek/-acak 将来时后缀
- 命令式：动词词根（用于按钮和操作指令）

示例：

```json
{
  "VERB_TENSES": {
    "SENDING": "Gönderiliyor",
    "SENT": "Gönderildi",
    "WILL_SEND": "Gönderilecek",
    "SEND": "Gönder"
  }
}
```

### 2.3 疑问和确认表达

- 疑问词：Ne, Nasıl, Ne zaman, Nerede
- 确认：Evet/Hayır, Onayla, Tamam
- 礼貌表达：Lütfen, Teşekkürler

示例：

```json
{
  "CONFIRM": {
    "SAVE": "Kaydetmek istiyor musunuz?",
    "INFO": "Ayarlar güncellendi",
    "CHECK": "Kontrol edelim"
  }
}
```

## 3. 格式规范

### 3.1 标点符号规则

- 土耳其语标点符号：
  - 句号：.
  - 逗号：,
  - 括号：( )
  - 引号：" " 或 « »
  - 问号：?
  - 感叹号：!
  - 破折号：—
- 半角符号使用场景：
  - 英数字
  - 变量
  - URL/邮箱地址
- 混排规则：
  - 土耳其语和英文之间加空格
  - 土耳其语和数字之间加空格
  - 数字和单位之间加空格

### 3.2 数字表达规则

- 数量单位：
  - 一般数量：miktar/sayı
  - 人数：kişi/kullanıcı
  - 时间：dakika, saat, gün
- 数字使用规则：
  - 1-10：可使用土耳其语数字（bir, iki, üç等）
  - 10以上：使用阿拉伯数字
- 土耳其特色：
  - 货币：₺ (Türk Lirası)
  - 小数点：使用逗号（,）
  - 千位分隔符：使用点（.）

示例：

```json
{
  "COUNT": {
    "MESSAGES": "{count} mesaj",
    "USERS": "{count} kullanıcı",
    "TIME": "{minutes} dakika",
    "PRICE": "{amount} ₺"
  }
}
```

## 4. 专业术语规范

### 4.1 系统操作术语

```json
{
  "SYSTEM": {
    "LOGIN": "Giriş Yap",
    "LOGOUT": "Çıkış Yap",
    "SETTINGS": "Ayarlar",
    "DASHBOARD": "Panel",
    "PROFILE": "Profil",
    "NOTIFICATION": "Bildirim"
  }
}
```

### 4.2 状态术语

```json
{
  "STATUS": {
    "ONLINE": "Çevrimiçi",
    "OFFLINE": "Çevrimdışı",
    "BUSY": "Meşgul",
    "AWAY": "Uzakta",
    "AVAILABLE": "Müsait"
  }
}
```

### 4.3 UI要素术语

```json
{
  "UI": {
    "SAVE": "Kaydet",
    "CANCEL": "İptal",
    "DELETE": "Sil",
    "EDIT": "Düzenle",
    "UPDATE": "Güncelle",
    "LOADING": "Yükleniyor",
    "PROCESSING": "İşleniyor",
    "PLEASE_WAIT": "Lütfen bekleyin",
    "COMPLETED": "Tamamlandı"
  }
}
```

## 5. 特殊处理规则

### 5.1 品牌名称处理

- "BetFugu" 保持不变（保持英文）
- "Jackpot" 可翻译为 "Jackpot" 或保持英文
- 技术术语保持不变：API、URL、Email等
- 其他品牌名称保持原文：Facebook、Google、Apple等

### 5.2 语序结构转换

土耳其语基本语序：

- 主宾谓结构（SOV）：与英语不同
- 形容词在名词前面
- 后缀黏着语特征

语序转换示例：

```json
{
  "EXAMPLE": {
    "ENGLISH": "Send message",
    "TURKISH": "Mesaj gönder",
    "ENGLISH_COMPLEX": "New message received",
    "TURKISH_COMPLEX": "Yeni mesaj alındı"
  }
}
```

### 5.3 土耳其语后缀规则

土耳其语后缀变化规则（元音和谐）：

- 前元音：e, i, ö, ü
- 后元音：a, ı, o, u
- 后缀必须遵循元音和谐

示例：

```json
{
  "SUFFIXES": {
    "PLAYER_TO": "oyuncuya",
    "SYSTEM_FROM": "sistemden",
    "MESSAGE_WITH": "mesajla",
    "ACCOUNT_IN": "hesapta"
  }
}
```

## 6. 常见错误示例

### 6.1 错误翻译

❌ 错误示例：

```json
{
  "LOGIN": "Giriş yapmak",     // 动名词，应用命令式
  "SAVE": "Kaydediliyor",     // 进行时，应用命令式
  "ERROR": "Bir hata oldu"    // 过于口语化
}
```

✅ 正确翻译：

```json
{
  "LOGIN": "Giriş Yap",
  "SAVE": "Kaydet",
  "ERROR": "Hata oluştu"
}
```

### 6.2 格式错误修正

❌ 错误格式：

- 变量处理：`Toplam{count}mesaj` → `Toplam {count} mesaj`
- 后缀错误：`oyuncuya` 应根据词根元音调整
- 语序错误：直译英语语序而非土耳其语SOV

## 7. 土耳其语特殊语法规则

### 7.1 元音和谐规律

- **前后元音和谐**：词根决定后缀元音
- **圆唇和谐**：圆唇元音影响后缀
- **软硬辅音和谐**：影响后缀辅音变化

示例：

```json
{
  "HARMONY": {
    "BOOK_TO": "kitaba",      // a词根 + a后缀
    "GAME_TO": "oyuna",       // u词根 + a后缀
    "MESSAGE_FROM": "mesajdan", // a词根 + dan后缀
    "SYSTEM_FROM": "sistemden"  // e词根 + den后缀
  }
}
```

### 7.2 格变化系统

土耳其语六个格：

- 主格：-∅ (零标记)
- 宾格：-(y)ı/i/u/ü
- 与格：-(y)a/e
- 离格：-dan/den
- 处格：-da/de
- 所有格：-(n)ın/in/un/ün

示例：

```json
{
  "CASES": {
    "TO_USER": "kullanıcıya",    // 与格
    "FROM_SYSTEM": "sistemden",  // 离格
    "IN_GAME": "oyunda",         // 处格
    "USER_MESSAGE": "kullanıcının mesajı" // 所有格
  }
}
```

### 7.3 动词活用规则

- **人称变化**：-ım/sin/∅/iz/siniz/lar
- **时态标记**：现在(-yor)、过去(-di)、将来(-ecek)
- **语态标记**：被动(-il/-in)、使役(-dir/-tir)

示例：

```json
{
  "VERBS": {
    "I_SEND": "gönderiyorum",
    "YOU_SEND": "gönderiyorsun",
    "SENT": "gönderildi",
    "WILL_SEND": "gönderilecek"
  }
}
```

## 8. UI长度控制规则

### 8.1 文案长度要求

- **核心原则**：翻译文案长度应与英文原文保持基本一致
- **目标**：避免因文案长度差异导致的界面布局错乱
- **控制范围**：所有UI可见文案，特别是按钮、标签、标题等

### 8.2 长度控制策略

1. **按钮文案**：优先使用简洁表达
   - `"SAVE"` → `"Kaydet"` 而非 `"Kaydetmek"`
   - `"LOGIN"` → `"Giriş"` 而非 `"Giriş Yap"`

2. **技术术语处理**：
   - 系统术语：适度本地化
   - 游戏术语：保持英文或使用简短翻译
   - 操作术语：使用简洁的命令式动词

3. **标题和标签**：使用缩写或简化表达
   - 避免冗长的后缀组合
   - 优先使用名词而非动词短语

### 8.3 长度检查标准

- **字符数控制**：土耳其语翻译不应超过英文原文字符数的135%
- **视觉宽度**：考虑土耳其语后缀可能造成的单词长度增加
- **容器适配**：确保在不同屏幕尺寸下正常显示

### 8.4 特殊情况处理

1. **必要的长翻译**：
   - 使用换行符合理分割
   - 采用缩写策略
   - 考虑上下文提示

2. **缩写策略**：
   - 保留核心词根
   - 简化不必要的后缀
   - 使用常见缩写形式

3. **混合策略**：
   - 技术词汇适度保持英文
   - 动作词汇使用土耳其语
   - 根据土耳其用户习惯调整

### 8.5 质量检查清单

翻译完成后进行以下检查：

- [ ] 字符数是否在控制范围内（≤135%）
- [ ] 在不同设备上显示是否正常
- [ ] 是否保持了原文的核心信息
- [ ] 用户理解是否无障碍
- [ ] 是否符合土耳其语表达习惯
- [ ] 元音和谐是否正确
- [ ] 按钮和标签是否在容器内正常显示
- [ ] 响应式布局是否受到影响

## 9. 土耳其特有考虑事项

### 9.1 文化适应性

- 考虑土耳其的伊斯兰文化背景
- 使用正式而礼貌的语言
- 避免可能引起文化冲突的表达
- 考虑土耳其的社会价值观

### 9.2 本地化特色

- 货币格式：1.234,56 ₺
- 日期格式：dd.mm.yyyy
- 时间格式：24小时制
- 电话格式：+90 (212) xxx xx xx

### 9.3 游戏本地化

- 平衡国际化和本地化需求
- 游戏术语优先考虑玩家习惯
- 保持与国际游戏社区的连接
- 考虑土耳其游戏市场的特点

### 9.4 法律和合规

- 遵守土耳其的数据保护法规
- 注意游戏相关的法律用语
- 确保年龄相关提示的准确性
- 考虑土耳其的消费者权益法规

### 9.5 用户体验优化

- 符合土耳其用户的使用习惯
- 考虑移动设备优先的使用模式
- 保持界面的直观性和友好性
- 确保翻译的一致性和自然度

### 9.6 语言纯化运动考虑

- 优先使用纯土耳其语词汇
- 避免过多的阿拉伯语和波斯语借词
- 使用土耳其语言协会推荐的术语
- 平衡现代性和语言纯化需求

### 9.7 技术本地化

- 键盘输入：支持土耳其语特殊字符（ç, ğ, ı, ö, ş, ü）
- 字符编码：确保UTF-8正确显示
- 排序规则：土耳其语字母排序
- 搜索功能：支持不区分重音符号的搜索

### 9.8 社会语言学考虑

- 年龄层差异：年轻用户vs老年用户的用词偏好
- 教育水平：适应不同教育背景的用户
- 地域差异：避免过于地方化的表达
- 性别中性：使用性别包容的语言

### 9.9 宗教和文化敏感性

- 时间表达：考虑伊斯兰历法的影响
- 节日和特殊日期：尊重宗教节日
- 颜色象征：了解颜色的文化含义
- 图像和符号：避免文化冲突的视觉元素
