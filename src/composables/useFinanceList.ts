/**
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-20 00:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-20 19:45:00
 * @FilePath     : /src/composables/useFinanceList.ts
 * @Description  : 财务记录列表逻辑 composable
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-20 00:00:00
 */

import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  getFinanceList, 
  deleteFinanceRecord,
  getPeriods,
  type FinanceRecord,
  type FinanceListParams,
  type Period
} from '../api/finance'

export function useFinanceList() {
  const router = useRouter()
  
  // 状态管理
  const loading = ref(false)
  const tableData = ref<FinanceRecord[]>([])
  const total = ref(0)
  const periods = ref<Period[]>([])
  
  // 分页参数
  const pagination = reactive({
    page: 1,
    pageSize: 10
  })
  
  // 搜索参数
  const searchParams = reactive({
    period: '',
    startDate: '',
    endDate: ''
  })
  
  // 计算属性
  const hasData = computed(() => tableData.value.length > 0)
  const isEmpty = computed(() => !loading.value && !hasData.value)
  
  // 加载财务记录列表
  const loadFinanceList = async () => {
    try {
      loading.value = true
      
      const params: FinanceListParams = {
        page: pagination.page,
        pageSize: pagination.pageSize,
        ...searchParams
      }
      
      const response = await getFinanceList(params)
      
      tableData.value = response.data
      total.value = response.total
      
      console.log('加载财务记录列表:', response)
    } catch (error) {
      console.error('加载财务记录列表失败:', error)
      ElMessage.error('加载数据失败，请重试')
    } finally {
      loading.value = false
    }
  }
  
  // 加载记账周期列表
  const loadPeriods = async () => {
    try {
      const data = await getPeriods()
      periods.value = data
      console.log('加载记账周期列表:', data)
    } catch (error) {
      console.error('加载记账周期列表失败:', error)
    }
  }
  
  // 搜索
  const handleSearch = () => {
    pagination.page = 1
    loadFinanceList()
  }
  
  // 重置搜索
  const handleReset = () => {
    Object.assign(searchParams, {
      period: '',
      startDate: '',
      endDate: ''
    })
    pagination.page = 1
    loadFinanceList()
  }
  
  // 分页变化
  const handlePageChange = (page: number) => {
    pagination.page = page
    loadFinanceList()
  }
  
  // 页面大小变化
  const handlePageSizeChange = (pageSize: number) => {
    pagination.pageSize = pageSize
    pagination.page = 1
    loadFinanceList()
  }
  
  // 新增记录
  const handleAdd = () => {
    router.push('/finance/add')
  }
  
  // 编辑记录
  const handleEdit = (record: FinanceRecord) => {
    router.push(`/finance/edit/${record.id}`)
  }
  
  // 删除记录
  const handleDelete = async (record: FinanceRecord) => {
    try {
      await ElMessageBox.confirm(
        `确定要删除 "${record.period}" 的财务记录吗？`,
        '删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
      
      await deleteFinanceRecord(record.id)
      ElMessage.success('删除成功')
      
      // 重新加载列表
      loadFinanceList()
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除财务记录失败:', error)
        ElMessage.error('删除失败，请重试')
      }
    }
  }
  
  // 查看详情
  const handleView = (record: FinanceRecord) => {
    router.push(`/finance/view/${record.id}`)
  }
  
  // 格式化货币
  const formatCurrency = (value: number): string => {
    if (value % 1 === 0) {
      return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")
    }
    return value.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",")
  }
  
  // 计算总收入
  const calculateTotalIncome = (record: FinanceRecord): number => {
    return (record.salary || 0) + (record.otherIncome || 0)
  }
  
  // 计算总支出
  const calculateTotalExpense = (record: FinanceRecord): number => {
    return (
      (record.juan || 0) +
      (record.wechat || 0) +
      (record.huabei || 0) +
      (record.spdbCreditCard || 0) +
      (record.commCreditCard || 0) +
      (record.cmCreditCard || 0) +
      (record.jdBaitiao || 0) +
      (record.jdJintiao || 0) +
      (record.rent || 0) +
      (record.mortgage || 0)
    )
  }
  
  // 计算结余
  const calculateBalance = (record: FinanceRecord): number => {
    return calculateTotalIncome(record) - calculateTotalExpense(record)
  }
  
  // 初始化
  const init = async () => {
    await Promise.all([
      loadFinanceList(),
      loadPeriods()
    ])
  }
  
  return {
    // 状态
    loading,
    tableData,
    total,
    periods,
    pagination,
    searchParams,
    
    // 计算属性
    hasData,
    isEmpty,
    
    // 方法
    loadFinanceList,
    loadPeriods,
    handleSearch,
    handleReset,
    handlePageChange,
    handlePageSizeChange,
    handleAdd,
    handleEdit,
    handleDelete,
    handleView,
    formatCurrency,
    calculateTotalIncome,
    calculateTotalExpense,
    calculateBalance,
    init
  }
}
