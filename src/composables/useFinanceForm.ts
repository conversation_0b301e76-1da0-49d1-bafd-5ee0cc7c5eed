/**
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-20 00:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-20 18:49:29
 * @FilePath     : /src/composables/useFinanceForm.ts
 * @Description  : 财务表单逻辑 composable
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-20 00:00:00
 */

import { ref, reactive, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getFinanceRecord,
  createFinanceRecord,
  updateFinanceRecord,
  type FinanceRecord
} from '../api/finance'
import {
  saveToStorage,
  getFromStorage,
  removeFromStorage,
  getStorageTimestamp,
  formatTimestamp,
  STORAGE_KEYS
} from '../utils/storage'
import type { NoteItem } from '../types/note'

export interface FinanceFormData {
  id: string
  periodRange: string[]
  period: string
  salary: number
  otherIncome: number
  juan: number
  wechat: number
  huabei: number
  spdbCreditCard: number
  commCreditCard: number
  cmCreditCard: number
  jdBaitiao: number
  jdJintiao: number
  rent: number
  mortgage: number
  remark: string
  // 现金资产字段
  cmBank: number
  icbcBank: number
  spdbBank: number
  cash: number
  alipay: number
  wechatPay: number
  // 实际结余字段
  actualBalance: number
  // 备注系统
  notes: NoteItem[]
  [key: string]: any
}

export function useFinanceForm() {
  const router = useRouter()
  const route = useRoute()

  // 表单引用和状态
  const formRef = ref()
  const loading = ref(false)

  // 判断是否为编辑模式
  const isEdit = computed(() => route.name === 'FinanceRecordEdit')
  const recordId = computed(() => route.params.id as string)

  // 表单数据
  const formData = reactive<FinanceFormData>({
    id: '',
    periodRange: [],
    period: '',
    salary: 0,
    otherIncome: 0,
    juan: 0,
    wechat: 0,
    huabei: 0,
    spdbCreditCard: 0,
    commCreditCard: 0,
    cmCreditCard: 0,
    jdBaitiao: 0,
    jdJintiao: 0,
    rent: 0,
    mortgage: 0,
    remark: '',
    // 现金资产字段
    cmBank: 0,
    icbcBank: 0,
    spdbBank: 0,
    cash: 0,
    alipay: 0,
    wechatPay: 0,
    // 实际结余字段
    actualBalance: 0,
    // 备注系统
    notes: []
  })

  // 表单验证规则
  const formRules = {
    periodRange: [
      { required: true, message: '请选择记账周期', trigger: 'change' }
    ]
  }

  // 计算总收入
  const totalIncome = computed(() => {
    return (formData.salary || 0) + (formData.otherIncome || 0)
  })

  // 计算总支出
  const totalExpense = computed(() => {
    return (
      (formData.juan || 0) +
      (formData.wechat || 0) +
      (formData.huabei || 0) +
      (formData.spdbCreditCard || 0) +
      (formData.commCreditCard || 0) +
      (formData.cmCreditCard || 0) +
      (formData.jdBaitiao || 0) +
      (formData.jdJintiao || 0) +
      (formData.rent || 0) +
      (formData.mortgage || 0)
    )
  })

  // 计算总现金资产
  const totalCashAssets = computed(() => {
    return (
      (formData.cmBank || 0) +
      (formData.icbcBank || 0) +
      (formData.spdbBank || 0) +
      (formData.cash || 0) +
      (formData.alipay || 0) +
      (formData.wechatPay || 0)
    )
  })

  // 计算当期结余
  const currentBalance = computed(() => {
    return totalIncome.value - totalExpense.value
  })

  // 计算发薪后（总现金资产 + 总收入）
  const afterSalary = computed(() => {
    return totalCashAssets.value + totalIncome.value
  })

  // 计算信用支出（需要还款的部分）
  const creditExpense = computed(() => {
    return (
      (formData.huabei || 0) +
      (formData.spdbCreditCard || 0) +
      (formData.commCreditCard || 0) +
      (formData.cmCreditCard || 0) +
      (formData.jdBaitiao || 0) +
      (formData.jdJintiao || 0)
    )
  })

  // 计算现金支出（已经支付的部分）
  const cashExpense = computed(() => {
    return (
      (formData.juan || 0) +
      (formData.wechat || 0) +
      (formData.rent || 0) +
      (formData.mortgage || 0)
    )
  })

  // 计算还款后（发薪后 - 信用支出）
  const afterPayment = computed(() => {
    return afterSalary.value - creditExpense.value
  })

  // 计算差额（实际结余 - 预期还款后）
  const balanceDifference = computed(() => {
    return (formData.actualBalance || 0) - afterPayment.value
  })

  // 处理周期变化
  const handlePeriodChange = () => {
    if (formData.periodRange && formData.periodRange.length === 2) {
      formData.period = `${formData.periodRange[0]} 至 ${formData.periodRange[1]}`
    } else {
      formData.period = ''
    }
  }

  // 返回上一页
  const goBack = () => {
    router.back()
  }

  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      const valid = await formRef.value.validate()
      if (!valid) return

      loading.value = true

      // 构建提交数据
      const submitData: Partial<FinanceRecord> = {
        period: formData.period,
        periodStart: formData.periodRange[0] || '',
        periodEnd: formData.periodRange[1] || '',
        salary: formData.salary || 0,
        otherIncome: formData.otherIncome || 0,
        juan: formData.juan || 0,
        wechat: formData.wechat || 0,
        huabei: formData.huabei || 0,
        spdbCreditCard: formData.spdbCreditCard || 0,
        commCreditCard: formData.commCreditCard || 0,
        cmCreditCard: formData.cmCreditCard || 0,
        jdBaitiao: formData.jdBaitiao || 0,
        jdJintiao: formData.jdJintiao || 0,
        rent: formData.rent || 0,
        mortgage: formData.mortgage || 0,
        remark: formData.remark || '',
        // 现金资产
        cmBank: formData.cmBank || 0,
        icbcBank: formData.icbcBank || 0,
        spdbBank: formData.spdbBank || 0,
        cash: formData.cash || 0,
        alipay: formData.alipay || 0,
        wechatPay: formData.wechatPay || 0
      }

      if (isEdit.value) {
        // 更新记录
        await updateFinanceRecord(recordId.value, submitData)
        ElMessage.success('财务记录更新成功')
      } else {
        // 创建记录
        await createFinanceRecord(submitData)
        ElMessage.success('财务记录添加成功')
      }

      // 清除暂存数据
      clearDraft()

      // 返回列表页
      router.push('/finance/list')
    } catch (error) {
      console.error('提交表单失败:', error)
      ElMessage.error('操作失败，请重试')
    } finally {
      loading.value = false
    }
  }

  // 生成暂存键名
  const getDraftKey = () => {
    if (isEdit.value && recordId.value) {
      return `${STORAGE_KEYS.FINANCE_FORM_DRAFT}_edit_${recordId.value}`
    }
    return `${STORAGE_KEYS.FINANCE_FORM_DRAFT}_add`
  }

  // 加载记录数据
  const loadRecordData = async () => {
    try {
      loading.value = true

      // 1. 首先尝试从暂存中恢复数据
      const draftKey = getDraftKey()
      const draftData = getFromStorage<FinanceFormData>(draftKey)
      const draftTimestamp = getStorageTimestamp(draftKey)

      let useApiData = true

      // 2. 如果有暂存数据，询问用户是否使用
      if (draftData && draftTimestamp) {
        try {
          await ElMessageBox.confirm(
            `发现 ${formatTimestamp(draftTimestamp)} 的暂存数据，是否恢复？`,
            '发现暂存数据',
            {
              confirmButtonText: '恢复暂存',
              cancelButtonText: '重新加载',
              type: 'info'
            }
          )

          // 用户选择恢复暂存数据
          Object.assign(formData, draftData)
          ElMessage.success('已恢复暂存数据')
          useApiData = false
        } catch (error) {
          // 用户选择重新加载，清除暂存
          removeFromStorage(draftKey)
        }
      }

      // 3. 如果需要从API加载数据
      if (useApiData && isEdit.value && recordId.value) {
        const record = await getFinanceRecord(recordId.value)
        Object.assign(formData, {
          ...record,
          periodRange: [record.periodStart, record.periodEnd]
        })
        console.log('从API加载记录数据:', record)
      }

    } catch (error) {
      console.error('加载记录数据失败:', error)
      ElMessage.error('加载数据失败，请重试')
    } finally {
      loading.value = false
    }
  }

  // 自动暂存数据
  const autoSaveDraft = () => {
    try {
      const draftKey = getDraftKey()
      saveToStorage(draftKey, formData)
      console.log('数据已自动暂存')
    } catch (error) {
      console.error('自动暂存失败:', error)
    }
  }

  // 清除暂存数据
  const clearDraft = () => {
    try {
      const draftKey = getDraftKey()
      removeFromStorage(draftKey)
      console.log('暂存数据已清除')
    } catch (error) {
      console.error('清除暂存失败:', error)
    }
  }

  // 监听表单数据变化，自动暂存
  let saveTimer: number | null = null
  watch(
    () => formData,
    () => {
      // 防抖保存，避免频繁写入
      if (saveTimer) {
        clearTimeout(saveTimer)
      }

      saveTimer = setTimeout(() => {
        autoSaveDraft()
      }, 1000) // 1秒后自动暂存
    },
    { deep: true }
  )

  return {
    // 状态
    formRef,
    loading,
    isEdit,
    recordId,

    // 数据
    formData,
    formRules,

    // 计算属性
    totalIncome,
    totalExpense,
    creditExpense,
    cashExpense,
    totalCashAssets,
    currentBalance,
    afterSalary,
    afterPayment,
    balanceDifference,

    // 方法
    handlePeriodChange,
    goBack,
    handleSubmit,
    loadRecordData
  }
}
