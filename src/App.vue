<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-22 10:48:52
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-17
 * @FilePath     : /src/App.vue
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-22 10:48:52
-->
<template>
  <div id="app">
    <router-view />
    <MockController v-if="isDev" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { useAuthStore } from "./stores/auth";
import { useRouter } from "vue-router";
import { authInterceptor } from "./utils/auth-interceptor";
import MockController from "./components/MockController.vue";

const authStore = useAuthStore();
const router = useRouter();
const isDev = ref(import.meta.env.DEV);

onMounted(async () => {
  try {
    console.log("🚀 [App] Application mounting...");

    // 初始化认证拦截器
    authInterceptor.setRouter(router);
    console.log("🚀 [App] Auth interceptor initialized");

    await authStore.initDatabase();
    console.log("🚀 [App] Database initialized");

    // 先检查localStorage中的认证状态
    const localIsAuthenticated =
      localStorage.getItem("isAuthenticated") === "true";
    console.log("🚀 [App] Local auth status:", localIsAuthenticated);

    if (localIsAuthenticated) {
      // 如果localStorage显示已认证，先设置前端状态为已认证
      console.log(
        "🚀 [App] Setting frontend auth state to true from localStorage"
      );
      authStore.isAuthenticated = true;

      // 然后检查认证状态同步（但不强制要求同步成功）
      try {
        const syncResult = await authStore.syncAuthStatus();
        console.log("🚀 [App] Auth sync result:", syncResult);
      } catch (syncError) {
        console.warn(
          "🚀 [App] Auth sync failed, but maintaining auth state:",
          syncError
        );
      }
    } else {
      console.log("🚀 [App] No local auth found, starting fresh");
    }

    console.log(
      "🚀 [App] Final frontend auth state:",
      authStore.isAuthenticated
    );
  } catch (error) {
    console.error("🚀 [App] Failed to initialize app:", error);
  }
});
</script>

<style>
#app {
  height: 100vh;
  overflow: hidden;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
  background-color: #f5f5f5;
}

* {
  box-sizing: border-box;
}
</style>
