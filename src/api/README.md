# 财务管理 Mock API 接口文档

## 🚀 快速开始

### 启动 Mock 服务器
```bash
# 启动 mock 服务器（端口 3001）
npm run mock

# 或者同时启动前端和 mock 服务器
npm run dev:full
```

### 接口基础信息
- **Base URL**: `http://localhost:3001`
- **数据格式**: JSON
- **Mock 工具**: json-server

## 📋 接口列表

### 1. 财务记录列表
**GET** `/finance-records`

**查询参数**:
- `_page`: 页码 (默认: 1)
- `_limit`: 每页数量 (默认: 10)
- `_sort`: 排序字段 (默认: createdAt)
- `_order`: 排序方式 (desc/asc)
- `period_like`: 周期模糊查询
- `periodStart_gte`: 开始日期大于等于
- `periodEnd_lte`: 结束日期小于等于

**响应示例**:
```json
{
  "data": [
    {
      "id": "1",
      "period": "2025-01-01 至 2025-01-31",
      "periodStart": "2025-01-01",
      "periodEnd": "2025-01-31",
      "salary": 8000,
      "otherIncome": 2000,
      "juan": 500,
      "wechat": 300,
      "huabei": 800,
      "spdbCreditCard": 1200,
      "commCreditCard": 900,
      "cmCreditCard": 1100,
      "jdBaitiao": 400,
      "jdJintiao": 600,
      "rent": 2500,
      "mortgage": 3000,
      "cmBank": 15000,
      "icbcBank": 8000,
      "spdbBank": 5000,
      "cash": 800,
      "alipay": 1200,
      "wechatPay": 500,
      "remark": "一月份财务记录",
      "createdAt": "2025-01-15T10:30:00.000Z",
      "updatedAt": "2025-01-15T10:30:00.000Z"
    }
  ],
  "total": 3
}
```

### 2. 获取财务记录详情
**GET** `/finance-records/{id}`

**响应**: 单个财务记录对象

### 3. 创建财务记录
**POST** `/finance-records`

**请求体**:
```json
{
  "period": "2025-02-01 至 2025-02-28",
  "periodStart": "2025-02-01",
  "periodEnd": "2025-02-28",
  "salary": 8000,
  "otherIncome": 1500,
  "juan": 400,
  "wechat": 250,
  "huabei": 700,
  "spdbCreditCard": 1000,
  "commCreditCard": 800,
  "cmCreditCard": 900,
  "jdBaitiao": 300,
  "jdJintiao": 500,
  "rent": 2500,
  "mortgage": 3000,
  "cmBank": 12000,
  "icbcBank": 7000,
  "spdbBank": 4000,
  "cash": 600,
  "alipay": 1000,
  "wechatPay": 400,
  "remark": "二月份财务记录"
}
```

### 4. 更新财务记录
**PUT** `/finance-records/{id}`

**请求体**: 与创建相同，但字段可选

### 5. 删除财务记录
**DELETE** `/finance-records/{id}`

**响应**: 204 No Content

### 6. 获取记账周期列表
**GET** `/periods`

**响应示例**:
```json
[
  {
    "id": "1",
    "period": "2025-01-01 至 2025-01-31",
    "periodStart": "2025-01-01",
    "periodEnd": "2025-01-31",
    "status": "active"
  }
]
```

### 7. 根据周期查询财务记录
**GET** `/finance-records?period={period}`

**示例**: `/finance-records?period=2025-01-01 至 2025-01-31`

## 🔧 使用方式

### 在组件中使用
```typescript
import { useFinanceList } from '@/composables/useFinanceList'
import { useFinanceForm } from '@/composables/useFinanceForm'

// 列表页面
const {
  tableData,
  loading,
  handleAdd,
  handleEdit,
  handleDelete,
  init
} = useFinanceList()

// 表单页面
const {
  formData,
  handleSubmit,
  loadRecordData
} = useFinanceForm()
```

### 直接调用 API
```typescript
import { 
  getFinanceList, 
  createFinanceRecord,
  updateFinanceRecord,
  deleteFinanceRecord 
} from '@/api/finance'

// 获取列表
const response = await getFinanceList({ page: 1, pageSize: 10 })

// 创建记录
const newRecord = await createFinanceRecord(formData)

// 更新记录
const updatedRecord = await updateFinanceRecord(id, formData)

// 删除记录
await deleteFinanceRecord(id)
```

## 📊 数据字段说明

### 财务记录字段
| 字段 | 类型 | 说明 |
|------|------|------|
| id | string | 记录ID |
| period | string | 记账周期 |
| periodStart | string | 开始日期 |
| periodEnd | string | 结束日期 |
| salary | number | 工资 |
| otherIncome | number | 其他收入 |
| juan | number | 娟 |
| wechat | number | 微信 |
| huabei | number | 花呗 |
| spdbCreditCard | number | 浦发信用卡 |
| commCreditCard | number | 交行信用卡 |
| cmCreditCard | number | 招商信用卡 |
| jdBaitiao | number | 京东白条 |
| jdJintiao | number | 京东金条 |
| rent | number | 房租 |
| mortgage | number | 房贷 |
| cmBank | number | 招商银行 |
| icbcBank | number | 工商银行 |
| spdbBank | number | 浦发银行 |
| cash | number | 现金 |
| alipay | number | 支付宝余额 |
| wechatPay | number | 微信余额 |
| remark | string | 备注 |
| createdAt | string | 创建时间 |
| updatedAt | string | 更新时间 |

## 🛠️ 开发说明

### Mock 数据位置
- 数据文件: `mock/db.json`
- 可以直接编辑此文件来修改 mock 数据

### 扩展接口
如需添加新接口，在 `mock/db.json` 中添加新的数据集合，json-server 会自动生成对应的 RESTful 接口。

### 错误处理
所有接口调用都包含错误处理，失败时会显示用户友好的错误消息。
