/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-21
 * @Description  : 账号相关API
 * Copyright 2025 Bruce, All Rights Reserved.
 */

import { accountsRequest, ApiResponse } from './request';
import { Account, Category, CreateAccountRequest, UpdateAccountRequest, CreateCategoryRequest, UpdateCategoryRequest } from '../stores/accounts';

// 账号相关API
export const accountApi = {
  /**
   * 获取所有账号
   */
  async getAllAccounts(): Promise<Account[]> {
    const response = await accountsRequest.get<Account[]>('/accounts');
    return response.data;
  },

  /**
   * 根据ID获取账号详情
   * @param id 账号ID
   */
  async getAccountById(id: string): Promise<Account> {
    const response = await accountsRequest.get<Account>(`/accounts/${id}`);
    return response.data;
  },

  /**
   * 创建账号
   * @param data 账号数据
   */
  async createAccount(data: CreateAccountRequest): Promise<Account> {
    const response = await accountsRequest.post<Account>('/accounts', data);
    return response.data;
  },

  /**
   * 更新账号
   * @param data 账号数据
   */
  async updateAccount(data: UpdateAccountRequest): Promise<Account> {
    const response = await accountsRequest.put<Account>(`/accounts/${data.id}`, data);
    return response.data;
  },

  /**
   * 删除账号
   * @param id 账号ID
   */
  async deleteAccount(id: string): Promise<void> {
    await accountsRequest.delete(`/accounts/${id}`);
  },

  /**
   * 批量删除账号
   * @param ids 账号ID数组
   */
  async batchDeleteAccounts(ids: string[]): Promise<number> {
    // 在实际项目中，可能需要使用POST请求，并在请求体中传递ids
    // 但对于json-server，我们可以使用多个DELETE请求
    await Promise.all(ids.map(id => accountsRequest.delete(`/accounts/${id}`)));
    return ids.length;
  },

  /**
   * 搜索账号
   * @param keyword 搜索关键词
   */
  async searchAccounts(keyword: string): Promise<Account[]> {
    const response = await accountsRequest.get<Account[]>(`/accounts?q=${encodeURIComponent(keyword)}`);
    return response.data;
  }
};

// 分类相关API
export const categoryApi = {
  /**
   * 获取所有分类
   */
  async getAllCategories(): Promise<Category[]> {
    const response = await accountsRequest.get<Category[]>('/categories');
    return response.data;
  },

  /**
   * 创建分类
   * @param data 分类数据
   */
  async createCategory(data: CreateCategoryRequest): Promise<Category> {
    const response = await accountsRequest.post<Category>('/categories', data);
    return response.data;
  },

  /**
   * 更新分类
   * @param data 分类数据
   */
  async updateCategory(data: UpdateCategoryRequest): Promise<Category> {
    const response = await accountsRequest.put<Category>(`/categories/${data.id}`, data);
    return response.data;
  },

  /**
   * 删除分类
   * @param id 分类ID
   */
  async deleteCategory(id: string): Promise<void> {
    await accountsRequest.delete(`/categories/${id}`);
  }
};