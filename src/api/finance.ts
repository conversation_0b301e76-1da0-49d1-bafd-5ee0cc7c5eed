/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-21
 * @Description  : 财务相关API
 * Copyright 2025 Bruce, All Rights Reserved.
 */

import { financeRequest, ApiResponse } from './request';

// 财务记录类型
export interface FinanceRecord {
  id: string;
  period: string;
  periodStart: string;
  periodEnd: string;
  salary: number;
  otherIncome: number;
  juan: number;
  wechat: number;
  huabei: number;
  spdbCreditCard: number;
  commCreditCard: number;
  cmCreditCard: number;
  jdBaitiao: number;
  jdJintiao: number;
  rent: number;
  mortgage: number;
  cmBank: number;
  icbcBank: number;
  spdbBank: number;
  cash: number;
  alipay: number;
  wechatPay: number;
  remark?: string;
  createdAt: string;
  updatedAt: string;
}

// 周期类型
export interface Period {
  id: string;
  period: string;
  periodStart: string;
  periodEnd: string;
  status: 'active' | 'completed';
}

// 财务相关API
export const financeApi = {
  /**
   * 获取财务记录列表
   * @param params 查询参数
   */
  async getFinanceRecords(params?: Record<string, any>): Promise<FinanceRecord[]> {
    const queryString = params ? new URLSearchParams(params).toString() : '';
    const url = `/finance-records${queryString ? `?${queryString}` : ''}`;
    const response = await financeRequest.get<FinanceRecord[]>(url);
    return response.data;
  },

  /**
   * 根据ID获取财务记录
   * @param id 记录ID
   */
  async getFinanceRecordById(id: string): Promise<FinanceRecord> {
    const response = await financeRequest.get<FinanceRecord>(`/finance-records/${id}`);
    return response.data;
  },

  /**
   * 创建财务记录
   * @param data 记录数据
   */
  async createFinanceRecord(data: Partial<FinanceRecord>): Promise<FinanceRecord> {
    const response = await financeRequest.post<FinanceRecord>('/finance-records', data);
    return response.data;
  },

  /**
   * 更新财务记录
   * @param id 记录ID
   * @param data 记录数据
   */
  async updateFinanceRecord(id: string, data: Partial<FinanceRecord>): Promise<FinanceRecord> {
    const response = await financeRequest.put<FinanceRecord>(`/finance-records/${id}`, data);
    return response.data;
  },

  /**
   * 删除财务记录
   * @param id 记录ID
   */
  async deleteFinanceRecord(id: string): Promise<void> {
    await financeRequest.delete(`/finance-records/${id}`);
  },

  /**
   * 获取所有周期
   */
  async getPeriods(): Promise<Period[]> {
    const response = await financeRequest.get<Period[]>('/periods');
    return response.data;
  },

  /**
   * 获取活跃周期
   */
  async getActivePeriod(): Promise<Period | null> {
    const response = await financeRequest.get<Period[]>('/periods?status=active');
    return response.data.length > 0 ? response.data[0] : null;
  },

  /**
   * 创建新周期
   * @param data 周期数据
   */
  async createPeriod(data: Partial<Period>): Promise<Period> {
    const response = await financeRequest.post<Period>('/periods', data);
    return response.data;
  },

  /**
   * 更新周期
   * @param id 周期ID
   * @param data 周期数据
   */
  async updatePeriod(id: string, data: Partial<Period>): Promise<Period> {
    const response = await financeRequest.put<Period>(`/periods/${id}`, data);
    return response.data;
  }
};

// 为了向后兼容，单独导出常用的函数
export const getFinanceRecord = financeApi.getFinanceRecordById;
export const createFinanceRecord = financeApi.createFinanceRecord;
export const updateFinanceRecord = financeApi.updateFinanceRecord;
export const deleteFinanceRecord = financeApi.deleteFinanceRecord;
export const getFinanceRecords = financeApi.getFinanceRecords;
