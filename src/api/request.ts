/**
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-20 00:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-20 19:45:00
 * @FilePath     : /src/api/request.ts
 * @Description  : HTTP 请求工具
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-20 00:00:00
 */

import { ElMessage } from 'element-plus'
import { mockSystem } from '../utils/mock-system'

// 请求配置
const FINANCE_API_BASE_URL = 'http://localhost:3001'
const ACCOUNTS_API_BASE_URL = 'http://localhost:3002'

// 响应数据类型
interface ApiResponse<T = any> {
  data: T
  headers: Record<string, string>
  status: number
}

// 请求选项
interface RequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  headers?: Record<string, string>
  body?: any
  mockBaseUrl?: string
}

/**
 * 基础请求函数
 */
const baseRequest = async <T = any>(
  url: string,
  options: RequestOptions = {}
): Promise<ApiResponse<T>> => {
  const { method = 'GET', headers = {}, body, mockBaseUrl } = options

  // 构建完整URL
  let fullUrl: string;

  // 如果启用了mock且提供了mockBaseUrl，则使用mockBaseUrl
  if (mockSystem.isMockEnabled() && mockBaseUrl) {
    fullUrl = url.startsWith('http') ? url : `${mockBaseUrl}${url}`;
  } else {
    fullUrl = url.startsWith('http') ? url : `${FINANCE_API_BASE_URL}${url}`;
  }

  // 默认请求头
  const defaultHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
    ...headers
  }

  try {
    // 如果启用了mock，添加模拟延迟
    if (mockSystem.isMockEnabled()) {
      await mockSystem.delay();
    }

    const response = await fetch(fullUrl, {
      method,
      headers: defaultHeaders,
      body: body ? JSON.stringify(body) : undefined
    })

    // 提取响应头
    const responseHeaders: Record<string, string> = {}
    response.headers.forEach((value, key) => {
      responseHeaders[key] = value
    })

    // 检查响应状态
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    // 解析响应数据
    const data = await response.json()

    return {
      data,
      headers: responseHeaders,
      status: response.status
    }
  } catch (error) {
    console.error('Request failed:', error)
    ElMessage.error(`请求失败: ${error instanceof Error ? error.message : '未知错误'}`)
    throw error
  }
}

/**
 * GET 请求
 */
const get = <T = any>(url: string, headers?: Record<string, string>, mockBaseUrl?: string): Promise<ApiResponse<T>> => {
  return baseRequest<T>(url, { method: 'GET', headers, mockBaseUrl })
}

/**
 * POST 请求
 */
const post = <T = any>(
  url: string,
  data?: any,
  headers?: Record<string, string>,
  mockBaseUrl?: string
): Promise<ApiResponse<T>> => {
  return baseRequest<T>(url, { method: 'POST', body: data, headers, mockBaseUrl })
}

/**
 * PUT 请求
 */
const put = <T = any>(
  url: string,
  data?: any,
  headers?: Record<string, string>,
  mockBaseUrl?: string
): Promise<ApiResponse<T>> => {
  return baseRequest<T>(url, { method: 'PUT', body: data, headers, mockBaseUrl })
}

/**
 * DELETE 请求
 */
const del = <T = any>(url: string, headers?: Record<string, string>, mockBaseUrl?: string): Promise<ApiResponse<T>> => {
  return baseRequest<T>(url, { method: 'DELETE', headers, mockBaseUrl })
}

// 导出请求工具
export const request = {
  get,
  post,
  put,
  delete: del
}

// 导出财务模块专用请求工具
export const financeRequest = {
  get: <T = any>(url: string, headers?: Record<string, string>): Promise<ApiResponse<T>> => {
    return get<T>(url, headers, FINANCE_API_BASE_URL)
  },
  post: <T = any>(url: string, data?: any, headers?: Record<string, string>): Promise<ApiResponse<T>> => {
    return post<T>(url, data, headers, FINANCE_API_BASE_URL)
  },
  put: <T = any>(url: string, data?: any, headers?: Record<string, string>): Promise<ApiResponse<T>> => {
    return put<T>(url, data, headers, FINANCE_API_BASE_URL)
  },
  delete: <T = any>(url: string, headers?: Record<string, string>): Promise<ApiResponse<T>> => {
    return del<T>(url, headers, FINANCE_API_BASE_URL)
  }
}

// 导出账号模块专用请求工具
export const accountsRequest = {
  get: <T = any>(url: string, headers?: Record<string, string>): Promise<ApiResponse<T>> => {
    return get<T>(url, headers, ACCOUNTS_API_BASE_URL)
  },
  post: <T = any>(url: string, data?: any, headers?: Record<string, string>): Promise<ApiResponse<T>> => {
    return post<T>(url, data, headers, ACCOUNTS_API_BASE_URL)
  },
  put: <T = any>(url: string, data?: any, headers?: Record<string, string>): Promise<ApiResponse<T>> => {
    return put<T>(url, data, headers, ACCOUNTS_API_BASE_URL)
  },
  delete: <T = any>(url: string, headers?: Record<string, string>): Promise<ApiResponse<T>> => {
    return del<T>(url, headers, ACCOUNTS_API_BASE_URL)
  }
}

// 导出基础请求函数
export { baseRequest }

// 导出类型
export type { ApiResponse, RequestOptions }
