/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-28 11:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-06-28 11:30:37
 * @FilePath     : /src/config/account-fields.ts
 * @Description  : 账号字段配置，统一管理所有账号相关的字段信息
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-28 11:00:00
 */

export interface AccountFieldConfig {
  key: string;
  label: string;
  placeholder?: string;
  required?: boolean;
  type: 'input' | 'password' | 'textarea' | 'select' | 'email' | 'phone' | 'url';
  rules?: Array<{
    required?: boolean;
    message?: string;
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
  }>;
  gridSpan?: number; // 在网格中占用的列数 (12列系统)
  group?: string; // 字段分组
  order?: number; // 显示顺序
  copyable?: boolean; // 是否可复制
  linkable?: boolean; // 是否可点击链接
}

// 账号字段配置
export const ACCOUNT_FIELDS: AccountFieldConfig[] = [
  {
    key: 'platform',
    label: '平台名称',
    placeholder: '如：微信、知乎、GitHub等',
    required: true,
    type: 'input',
    gridSpan: 6,
    group: 'basic',
    order: 1,
    rules: [
      { required: true, message: '请输入平台名称' }
    ]
  },
  {
    key: 'category_id',
    label: '分类',
    placeholder: '选择分类',
    type: 'select',
    gridSpan: 6,
    group: 'basic',
    order: 2
  },
  {
    key: 'website_url',
    label: '网站地址',
    placeholder: '请输入网站地址（可选）',
    type: 'url',
    gridSpan: 12,
    group: 'basic',
    order: 3,
    linkable: true
  },
  {
    key: 'username',
    label: '登录账号',
    placeholder: '用户名、邮箱或手机号',
    required: true,
    type: 'input',
    gridSpan: 6,
    group: 'auth',
    order: 4,
    copyable: true,
    rules: [
      { required: true, message: '请输入登录账号' }
    ]
  },
  {
    key: 'display_name',
    label: '用户名',
    placeholder: '显示用的用户名（可选）',
    type: 'input',
    gridSpan: 6,
    group: 'auth',
    order: 5,
    copyable: true
  },
  {
    key: 'password',
    label: '密码',
    placeholder: '请输入密码',
    required: true,
    type: 'password',
    gridSpan: 6,
    group: 'auth',
    order: 6,
    copyable: true,
    rules: [
      { required: true, message: '请输入密码' },
      { minLength: 1, message: '密码不能为空' }
    ]
  },
  {
    key: 'email',
    label: '邮箱',
    placeholder: '请输入邮箱（可选）',
    type: 'email',
    gridSpan: 6,
    group: 'contact',
    order: 7,
    copyable: true
  },
  {
    key: 'phone',
    label: '手机号',
    placeholder: '请输入手机号（可选）',
    type: 'phone',
    gridSpan: 6,
    group: 'contact',
    order: 8,
    copyable: true
  },
  {
    key: 'notes',
    label: '备注',
    placeholder: '请输入备注信息（可选）',
    type: 'textarea',
    gridSpan: 12,
    group: 'additional',
    order: 9
  }
];

// 字段分组配置
export const FIELD_GROUPS = {
  basic: {
    title: '基本信息',
    order: 1
  },
  auth: {
    title: '登录信息',
    order: 2
  },
  contact: {
    title: '联系方式',
    order: 3
  },
  additional: {
    title: '附加信息',
    order: 4
  },
  custom: {
    title: '自定义字段',
    order: 5
  }
};

// 获取指定字段的配置
export const getFieldConfig = (key: string): AccountFieldConfig | undefined => {
  return ACCOUNT_FIELDS.find(field => field.key === key);
};

// 获取按分组排序的字段
export const getFieldsByGroup = (group?: string): AccountFieldConfig[] => {
  const fields = group
    ? ACCOUNT_FIELDS.filter(field => field.group === group)
    : ACCOUNT_FIELDS;

  return fields.sort((a, b) => (a.order || 0) - (b.order || 0));
};

// 获取所有分组
export const getAllGroups = () => {
  return Object.entries(FIELD_GROUPS)
    .sort(([, a], [, b]) => a.order - b.order)
    .map(([key, value]) => ({ key, ...value }));
};

// 获取必填字段
export const getRequiredFields = (): AccountFieldConfig[] => {
  return ACCOUNT_FIELDS.filter(field => field.required);
};

// 获取可复制字段
export const getCopyableFields = (): AccountFieldConfig[] => {
  return ACCOUNT_FIELDS.filter(field => field.copyable);
};

// 获取字段的验证规则
export const getFieldRules = (key: string) => {
  const field = getFieldConfig(key);
  return field?.rules || [];
};

// 生成表单验证规则对象
export const generateFormRules = () => {
  const rules: Record<string, any[]> = {};

  ACCOUNT_FIELDS.forEach(field => {
    if (field.rules && field.rules.length > 0) {
      rules[field.key] = field.rules;
    }
  });

  return rules;
};