/**
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-20 00:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-20 18:49:29
 * @FilePath     : /src/config/financeConfig.ts
 * @Description  : 财务表单配置
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-20 00:00:00
 */

// SVG 图标路径
export const ICONS = {
  salary: '<path d="M21 12V7H5a2 2 0 0 1 0-4h14v4" /><path d="M3 5v14a2 2 0 0 0 2 2h16v-5" /><path d="M18 12a2 2 0 0 0 0 4h4v-4Z" />',
  otherIncome: '<path d="M12 8v4" /><path d="M12 16h.01" /><path d="M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" /><path d="M4.22 16.22a2.5 2.5 0 0 1 0-3.54" /><path d="M19.78 7.78a2.5 2.5 0 0 1 0 3.54" />',
  huabei: '<path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z" /><path d="m9 12 2 2 4-4" />',
  wechat: '<path d="M2 12h20" /><path d="M12 2v20" />',
  creditCard: '<rect width="20" height="14" x="2" y="5" rx="2" /><line x1="2" x2="22" y1="10" y2="10" />',
  rent: '<path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" /><polyline points="9 22 9 12 15 12 15 22" />',
  mortgage: '<path d="M12 22V8.4C12 7.1 12.8 6 14.5 6h0C15.9 6 17 7 17 8.4V22" /><path d="M17 11h.5a2.5 2.5 0 0 1 0 5H17" /><path d="M7 22v-5a2 2 0 0 1 2-2h1" /><path d="M7 15h1.5a2.5 2.5 0 0 1 0 5H7" />',
  jd: '<line x1="12" x2="12" y1="2" y2="22" /><path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />',
  juan: '<path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z" />',
  bank: '<path d="M3 21h18" /><path d="M5 21V7l8-4v18" /><path d="M19 21V11l-6-4" /><path d="M9 9v12" /><path d="M15 11v10" />'
}

// 收入项目配置
export const INCOME_FIELDS = [
  {
    name: 'salary',
    label: '工资',
    icon: ICONS.salary,
    color: '#4F46E5',
    step: 100,
    span: 12
  },
  {
    name: 'otherIncome',
    label: '其他收入',
    icon: ICONS.otherIncome,
    color: '#4F46E5',
    step: 100,
    span: 12
  }
]

// 支出分类配置
export const EXPENSE_CATEGORIES = [
  {
    title: '日常开销',
    fields: [
      {
        name: 'huabei',
        label: '花呗',
        icon: ICONS.huabei,
        color: '#10B981',
        step: 10
      },
      {
        name: 'wechat',
        label: '微信',
        icon: ICONS.wechat,
        color: '#10B981',
        step: 10
      }
    ]
  },
  {
    title: '信用卡还款',
    fields: [
      {
        name: 'spdbCreditCard',
        label: '浦发',
        icon: ICONS.creditCard,
        color: '#3B82F6',
        step: 10
      },
      {
        name: 'commCreditCard',
        label: '交行',
        icon: ICONS.creditCard,
        color: '#3B82F6',
        step: 10
      },
      {
        name: 'cmCreditCard',
        label: '招商',
        icon: ICONS.creditCard,
        color: '#3B82F6',
        step: 10
      }
    ]
  },
  {
    title: '住房支出',
    fields: [
      {
        name: 'rent',
        label: '房租',
        icon: ICONS.rent,
        color: '#F97316',
        step: 100
      },
      {
        name: 'mortgage',
        label: '房贷',
        icon: ICONS.mortgage,
        color: '#F97316',
        step: 100
      }
    ]
  },
  {
    title: '京东金融',
    fields: [
      {
        name: 'jdBaitiao',
        label: '京东白条',
        icon: ICONS.jd,
        color: '#EF4444',
        step: 10
      },
      {
        name: 'jdJintiao',
        label: '京东金条',
        icon: ICONS.jd,
        color: '#EF4444',
        step: 10
      }
    ]
  },
  {
    title: '其他',
    fields: [
      {
        name: 'juan',
        label: '娟',
        icon: ICONS.juan,
        color: '#8B5CF6',
        step: 10,
        span: 12
      }
    ]
  }
]

// 实际结余配置 - 简化为单个字段
export const ACTUAL_BALANCE_FIELD = {
  name: 'actualBalance',
  label: '实际结余',
  icon: ICONS.money,
  color: '#52C41A',
  step: 100,
  span: 24
}

// 现金资产分类配置
export const CASH_CATEGORIES = [
  {
    title: '储蓄卡余额',
    fields: [
      {
        name: 'cmBank',
        label: '招商银行',
        icon: ICONS.bank,
        color: '#1890FF',
        step: 100,
        span: 8
      },
      {
        name: 'icbcBank',
        label: '工商银行',
        icon: ICONS.bank,
        color: '#1890FF',
        step: 100,
        span: 8
      },
      {
        name: 'spdbBank',
        label: '浦发银行',
        icon: ICONS.bank,
        color: '#1890FF',
        step: 100,
        span: 8
      }
    ]
  },
  {
    title: '现金及其他',
    fields: [
      {
        name: 'cash',
        label: '现金',
        icon: ICONS.salary,
        color: '#52C41A',
        step: 10,
        span: 8
      },
      {
        name: 'alipay',
        label: '支付宝余额',
        icon: ICONS.huabei,
        color: '#1677FF',
        step: 10,
        span: 8
      },
      {
        name: 'wechatPay',
        label: '微信余额',
        icon: ICONS.wechat,
        color: '#07C160',
        step: 10,
        span: 8
      }
    ]
  }
]
