/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-22 10:54:44
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-17 00:54:34
 * @FilePath     : /src/router/index.ts
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-22 10:54:44
 */
import { createRouter, createWebHashHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { Message } from '@arco-design/web-vue'
import { mockSystem } from '../utils/mock-system'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard' // 默认重定向到仪表盘而不是登录页
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue')
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('../views/Dashboard.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/accounts',
    name: 'Accounts',
    component: () => import('../views/Accounts.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/account/new',
    name: 'NewAccount',
    component: () => import('../views/AccountAdd.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/account/detail/:id',
    name: 'AccountDetail',
    component: () => import('../views/AccountDetail.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/categories',
    name: 'Categories',
    component: () => import('../views/Categories.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/finance',
    name: 'Finance',
    redirect: '/finance/dashboard',
    meta: { requiresAuth: true }
  },
  {
    path: '/finance/dashboard',
    name: 'FinanceDashboard',
    component: () => import('../views/finance/Dashboard.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/finance/list',
    name: 'FinanceList',
    component: () => import('../views/finance/List.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/finance/record/new',
    name: 'FinanceRecordAdd',
    component: () => import('../views/finance/RecordForm.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/finance/record/edit/:id',
    name: 'FinanceRecordEdit',
    component: () => import('../views/finance/RecordForm.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/finance/settings',
    name: 'FinanceSettings',
    component: () => import('../views/finance/Settings.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/finance/comparison',
    name: 'FinanceComparison',
    component: () => import('../views/finance/ComparisonView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: () => import('../views/Settings.vue'),
    meta: { requiresAuth: true }
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  try {
    console.log('🛡️ [Route Guard] Navigating from', from.path, 'to', to.path)

    // 如果是访问登录页面
    if (to.path === '/login') {
      const isAuthenticated = localStorage.getItem('isAuthenticated') === 'true'
      if (isAuthenticated) {
        console.log('🛡️ [Route Guard] Already authenticated, redirecting to dashboard')
        next('/dashboard')
      } else {
        next()
      }
      return
    }

    // 如果是需要认证的页面
    if (to.meta.requiresAuth) {
      console.log('🛡️ [Route Guard] Route requires authentication, checking...')

      // 1. 首先检查localStorage中的认证状态
      const localIsAuthenticated = localStorage.getItem('isAuthenticated') === 'true'
      console.log('🛡️ [Route Guard] Local auth status:', localIsAuthenticated)

      if (!localIsAuthenticated) {
        console.log('🛡️ [Route Guard] No local auth, redirecting to login')
        next('/login')
        return
      }

      // 2. 检查后端认证状态
      try {
        const authStore = useAuthStore()
        console.log('🛡️ [Route Guard] Checking backend auth status...')

        // 如果在浏览器环境中使用 mock API，简化认证检查
        if (mockSystem.isMockEnabled()) {
          console.log('🛡️ [Route Guard] Using mock API, skipping detailed backend check')
          next()
          return
        }

        const backendIsAuthenticated = await authStore.checkAuthStatus()
        console.log('🛡️ [Route Guard] Backend auth status:', backendIsAuthenticated)

        if (!backendIsAuthenticated) {
          console.log('🛡️ [Route Guard] Backend auth failed, clearing local state and redirecting')
          // 清除前端认证状态
          authStore.isAuthenticated = false
          localStorage.removeItem('isAuthenticated')
          // 显示提示信息
          Message.warning('认证状态已失效，请重新登录')
          next('/login')
          return
        }

        // 3. 认证通过，允许访问
        console.log('🛡️ [Route Guard] Authentication successful, allowing access')
        next()
      } catch (error) {
        console.error('🛡️ [Route Guard] Auth check failed:', error)

        // 在浏览器环境中使用 mock API 时，忽略认证错误
        if (mockSystem.isMockEnabled()) {
          console.log('🛡️ [Route Guard] Using mock API, ignoring auth error')
          next()
          return
        }

        // 认证检查失败，也跳转到登录页面
        const authStore = useAuthStore()
        authStore.isAuthenticated = false
        localStorage.removeItem('isAuthenticated')
        Message.error('认证检查失败，请重新登录')
        next('/login')
      }
    } else {
      // 不需要认证的页面，直接通过
      next()
    }
  } catch (error) {
    console.error('🛡️ [Route Guard] Route guard error:', error)
    next()
  }
})

// 路由错误处理
router.onError((error) => {
  console.error('Router error:', error)
})

export default router