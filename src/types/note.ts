/**
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-20 00:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-20 23:35:00
 * @FilePath     : /src/types/note.ts
 * @Description  : 备注相关类型定义
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-20 00:00:00
 */

// 标签类型
export interface TagType {
  id: string
  name: string
  color: string
  icon?: string
  type: 'priority' | 'category' | 'custom'
}

// 备注条目
export interface NoteItem {
  id: string
  tags: string[]           // 标签ID数组
  content: string          // 备注内容
  createdAt: Date         // 创建时间
  updatedAt: Date         // 更新时间
  priority: number        // 优先级（用于排序）
}

// 预设标签配置
export const PRESET_TAGS: TagType[] = [
  // 优先级标签
  {
    id: 'important',
    name: '重要',
    color: '#ff4d4f',
    icon: '!',
    type: 'priority'
  },
  {
    id: 'reminder',
    name: '提醒',
    color: '#faad14',
    icon: '⏰',
    type: 'priority'
  },
  {
    id: 'plan',
    name: '计划',
    color: '#52c41a',
    icon: '🎯',
    type: 'priority'
  },
  {
    id: 'record',
    name: '记录',
    color: '#1890ff',
    icon: '📝',
    type: 'priority'
  },
  {
    id: 'abnormal',
    name: '异常',
    color: '#722ed1',
    icon: '⚠️',
    type: 'priority'
  },
  
  // 分类标签
  {
    id: 'rent',
    name: '房租',
    color: '#8c8c8c',
    type: 'category'
  },
  {
    id: 'credit-card',
    name: '信用卡',
    color: '#8c8c8c',
    type: 'category'
  },
  {
    id: 'investment',
    name: '投资',
    color: '#8c8c8c',
    type: 'category'
  },
  {
    id: 'salary',
    name: '工资',
    color: '#8c8c8c',
    type: 'category'
  },
  {
    id: 'insurance',
    name: '保险',
    color: '#8c8c8c',
    type: 'category'
  },
  {
    id: 'budget',
    name: '预算',
    color: '#8c8c8c',
    type: 'category'
  }
]
