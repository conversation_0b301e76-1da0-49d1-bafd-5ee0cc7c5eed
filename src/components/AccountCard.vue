<template>
  <el-card class="account-card" shadow="hover">
    <template #header>
      <div class="card-title">
        <div class="platform-info">
          <div
            class="platform-icon"
            :style="{ backgroundColor: categoryColor }"
          >
            {{ account.platform.charAt(0).toUpperCase() }}
          </div>
          <div class="platform-name">{{ account.platform }}</div>
        </div>
        <div class="card-actions">
          <el-dropdown @command="onActionSelect">
            <el-button text size="small">
              <el-icon><More /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="edit">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-dropdown-item>
                <el-dropdown-item command="delete">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </template>

    <div class="account-info">
      <div class="info-row">
        <label>用户名</label>
        <div class="info-value">
          <span>{{ account.username }}</span>
          <el-button
            text
            size="small"
            @click="copyToClipboard(account.username)"
          >
            <el-icon><CopyDocument /></el-icon>
          </el-button>
        </div>
      </div>

      <div class="info-row">
        <label>密码</label>
        <div class="info-value">
          <span v-if="showPassword">{{
            decryptedPassword || "解密中..."
          }}</span>
          <span v-else>••••••••</span>
          <div class="password-actions">
            <el-button text size="small" @click="togglePasswordVisibility">
              <el-icon>
                <View v-if="!showPassword" />
                <Hide v-else />
              </el-icon>
            </el-button>
            <el-button
              text
              size="small"
              @click="copyPassword"
              :disabled="!decryptedPassword"
            >
              <el-icon><CopyDocument /></el-icon>
            </el-button>
          </div>
        </div>
      </div>

      <div v-if="account.email" class="info-row">
        <label>邮箱</label>
        <div class="info-value">
          <span>{{ account.email }}</span>
          <el-button text size="small" @click="copyToClipboard(account.email!)">
            <el-icon><CopyDocument /></el-icon>
          </el-button>
        </div>
      </div>

      <div v-if="account.phone" class="info-row">
        <label>手机号</label>
        <div class="info-value">
          <span>{{ account.phone }}</span>
          <el-button text size="small" @click="copyToClipboard(account.phone!)">
            <el-icon><CopyDocument /></el-icon>
          </el-button>
        </div>
      </div>

      <div v-if="account.website_url" class="info-row">
        <label>网址</label>
        <div class="info-value">
          <a :href="account.website_url" target="_blank" class="website-link">
            {{ account.website_url }}
          </a>
          <el-button
            text
            size="small"
            @click="copyToClipboard(account.website_url!)"
          >
            <el-icon><CopyDocument /></el-icon>
          </el-button>
        </div>
      </div>

      <div v-if="account.notes" class="info-row">
        <label>备注</label>
        <div class="info-value">
          <span class="notes">{{ account.notes }}</span>
        </div>
      </div>

      <div v-if="categoryName" class="info-row">
        <label>分类</label>
        <div class="info-value">
          <el-tag :color="categoryColor" size="small">
            {{ categoryName }}
          </el-tag>
        </div>
      </div>
    </div>

    <div class="card-footer">
      <span class="update-time">
        更新于 {{ formatDate(account.updated_at) }}
      </span>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import {
  More,
  Edit,
  Delete,
  CopyDocument,
  View,
  Hide,
} from "@element-plus/icons-vue";
import { useAccountsStore } from "../stores/accounts";
import type { Account } from "../stores/accounts";
import { formatDate } from "../utils";

interface Props {
  account: Account;
}

interface Emits {
  (e: "edit", account: Account): void;
  (e: "delete", account: Account): void;
  (e: "copy", text: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const accountsStore = useAccountsStore();

const showPassword = ref(false);
const decryptedPassword = ref("");

const categoryInfo = computed(() => {
  if (!props.account.category_id) return null;
  return accountsStore.categories.find(
    (cat) => cat.id === props.account.category_id
  );
});

const categoryName = computed(() => categoryInfo.value?.name || "");
const categoryColor = computed(() => categoryInfo.value?.color || "#1890ff");

// 监听密码显示状态变化
watch(showPassword, async (newValue) => {
  if (newValue && !decryptedPassword.value) {
    try {
      decryptedPassword.value = await accountsStore.decryptPassword(
        props.account.encrypted_password
      );
    } catch (error) {
      console.error("Failed to decrypt password:", error);
      decryptedPassword.value = "解密失败";
    }
  }
});

const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value;
};

const copyPassword = async () => {
  if (!decryptedPassword.value) {
    try {
      decryptedPassword.value = await accountsStore.decryptPassword(
        props.account.encrypted_password
      );
    } catch (error) {
      console.error("Failed to decrypt password:", error);
      return;
    }
  }

  emit("copy", decryptedPassword.value);
};

const copyToClipboard = (text: string) => {
  emit("copy", text);
};

const onActionSelect = (value: string) => {
  if (value === "edit") {
    emit("edit", props.account);
  } else if (value === "delete") {
    emit("delete", props.account);
  }
};

// 使用导入的工具方法
</script>

<style lang="scss" scoped>
// 使用自动导入的变量和混入
.account-card {
  @include card-shadow;
  @include card-hover;

  // 使用 BEM 命名约定
  &__title {
    @include flex-center-between;
  }

  &__platform-info {
    @include flex-center;
    gap: $spacing-sm;

    .platform-icon {
      @include flex-center;
      width: 32px;
      height: 32px;
      border-radius: $border-radius-base;
      color: $text-color-white;
      font-weight: $font-weight-semibold;
      font-size: $font-size-sm;
    }

    .platform-name {
      font-size: $font-size-base;
      font-weight: $font-weight-semibold;
      color: $text-color-primary;
    }
  }

  &__actions {
    @include flex-center;
    gap: $spacing-xs;
  }
}

// 兼容当前模板中的类名
.card-title {
  @include flex-center-between;
}

.platform-info {
  @include flex-center;
  gap: $spacing-sm;
}

.platform-icon {
  @include flex-center;
  width: 32px;
  height: 32px;
  border-radius: $border-radius-base;
  color: $text-color-white;
  font-weight: $font-weight-semibold;
  font-size: $font-size-sm;
}

.platform-name {
  font-size: $font-size-base;
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
}

.account-info {
  margin-top: $spacing-base;
}

.info-row {
  @include flex-center-between;
  margin-bottom: $spacing-sm;
  min-height: 24px;

  &:last-child {
    margin-bottom: 0;
  }

  label {
    font-size: $font-size-xs;
    color: $text-color-secondary;
    font-weight: $font-weight-medium;
    min-width: 60px;
    text-align: left;
  }
}

.info-value {
  @include flex-center;
  gap: $spacing-xs;
  flex: 1;
  justify-content: flex-end;

  span {
    font-size: $font-size-sm;
    color: $text-color-primary;
    word-break: break-all;
    text-align: right;
  }
}

.password-actions {
  @include flex-center;
  gap: $spacing-xs;
}

.notes {
  max-width: 200px;
  @include text-ellipsis;
}

.website-link {
  color: $primary-color;
  text-decoration: none;
  max-width: 200px;
  @include text-ellipsis;
  @include smooth-transition(color);

  &:hover {
    color: $primary-color-hover;
    text-decoration: underline;
  }
}

.card-footer {
  margin-top: $spacing-base;
  padding-top: $spacing-base;
  border-top: 1px solid $border-color-light;
}

.update-time {
  font-size: $font-size-xs;
  color: $text-color-disabled;
}

// 响应式设计
@include mobile {
  .account-card {
    .platform-icon {
      width: 28px;
      height: 28px;
      font-size: $font-size-xs;
    }

    .platform-name {
      font-size: $font-size-sm;
    }

    .info-row {
      @include flex-column;
      align-items: flex-start;
      gap: $spacing-xs;

      label {
        min-width: auto;
      }

      .info-value {
        justify-content: flex-start;
        width: 100%;
      }
    }

    .notes,
    .website-link {
      max-width: 100%;
    }
  }
}
</style>
