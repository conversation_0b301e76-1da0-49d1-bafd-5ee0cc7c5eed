<template>
  <!-- 批量操作工具栏 -->
  <div v-if="selectedRowKeys.length > 0" class="batch-actions-bar">
    <div class="batch-info">
      <icon-info-circle class="info-icon" />
      已选择 <strong>{{ selectedRowKeys.length }}</strong> 个账号
    </div>
    <div class="batch-actions">
      <a-button
        @click="handleSelectAll"
        size="small"
        v-if="selectedRowKeys.length < accounts.length"
      >
        全选
      </a-button>
      <a-button @click="handleClearSelection" size="small"> 取消选择 </a-button>
      <a-button
        type="primary"
        status="danger"
        @click="handleBatchDelete"
        size="small"
      >
        <template #icon><icon-delete /></template>
        批量删除
      </a-button>
    </div>
  </div>

  <BaseTable
    :columns="columns"
    :data="accounts"
    :loading="loading"
    :empty-title="emptyTitle"
    :empty-description="emptyDescription"
    :show-pagination="showPagination"
    :current-page="currentPage"
    :page-size="pageSize"
    :total="total"
    :row-selection="rowSelection"
    row-key="id"
    @row-click="handleRowClick"
    @select="handleTableSelect"
    @select-all="handleTableSelectAll"
    @page-change="$emit('page-change', $event)"
    @page-size-change="$emit('page-size-change', $event)"
    @pagination-change="$emit('pagination-change', $event)"
  >
    <!-- 账号ID列自定义渲染 -->
    <template #id="{ row }">
      <div class="id-cell">
        <span class="id-text">{{ row.id.slice(0, 8) }}...</span>
        <el-button
          type="primary"
          link
          size="small"
          @click="$emit('copy', row.id)"
          title="复制完整ID"
          class="copy-btn"
        >
          <el-icon><CopyDocument /></el-icon>
        </el-button>
      </div>
    </template>

    <!-- 平台列自定义渲染 -->
    <template #platform="{ row }">
      <div class="platform-cell">
        <!-- <div
          class="platform-avatar"
          :style="{ backgroundColor: getPlatformColor(row.platform) }"
        >
          {{ row.platform.charAt(0).toUpperCase() }}
        </div> -->
        <div class="platform-info">
          <div class="platform-name">{{ row.platform }}</div>
          <div v-if="row.website_url" class="platform-url">
            <a :href="row.website_url" target="_blank" class="url-link">
              {{ getDomainFromUrl(row.website_url) }}
            </a>
          </div>
        </div>
      </div>
    </template>

    <!-- 用户名列自定义渲染 -->
    <template #username="{ row }">
      <div class="copyable-cell">
        <span class="cell-value">{{ row.username }}</span>
        <el-button
          type="primary"
          link
          size="small"
          class="copy-btn"
          @click="$emit('copy', row.username)"
        >
          <el-icon><CopyDocument /></el-icon>
        </el-button>
      </div>
    </template>

    <!-- 分类列自定义渲染 -->
    <template #category_id="{ row }">
      <div v-if="getCategoryName(row.category_id)" class="category-tag">
        <div
          class="category-dot"
          :style="{ backgroundColor: getCategoryColor(row.category_id) }"
        ></div>
        <span class="category-name">{{
          getCategoryName(row.category_id)
        }}</span>
      </div>
      <span v-else class="empty-value">-</span>
    </template>

    <!-- 更新时间列自定义渲染 -->
    <template #updated_at="{ row }">
      <div class="time-cell">
        <el-icon class="time-icon"><Clock /></el-icon>
        <span class="time-text">{{ formatDate(row.updated_at) }}</span>
      </div>
    </template>

    <!-- 操作列自定义渲染 -->
    <template #actions="{ row }">
      <div class="action-cell">
        <el-button
          type="primary"
          link
          size="small"
          class="view-btn"
          @click="$emit('view-detail', row)"
          title="查看详情"
        >
          <el-icon><View /></el-icon>
          详情
        </el-button>
        <el-popconfirm
          title="确定要删除这个账号吗？此操作不可恢复。"
          @confirm="$emit('delete', row)"
        >
          <template #reference>
            <el-button
              type="danger"
              link
              size="small"
              class="delete-btn"
              title="删除账号"
            >
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-popconfirm>
      </div>
    </template>

    <!-- 空状态操作 -->
    <template #empty-action>
      <el-button
        v-if="!searchKeyword"
        type="primary"
        @click="$emit('add-account')"
      >
        添加账号
      </el-button>
    </template>
  </BaseTable>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import {
  Clock,
  Delete,
  CopyDocument,
  View,
  InfoFilled,
} from "@element-plus/icons-vue";
import BaseTable from "./BaseTable.vue";
import type { Account } from "../stores/accounts";
import { getDomainFromUrl, formatDate } from "../utils";

interface Props {
  accounts: Account[];
  loading?: boolean;
  searchKeyword?: string;
  categories: Array<{ id: string; name: string; color?: string }>;

  // 分页配置
  showPagination?: boolean;
  currentPage?: number;
  pageSize?: number;
  total?: number;
}

interface Emits {
  (e: "view-detail", account: Account): void;
  (e: "delete", account: Account): void;
  (e: "copy", text: string): void;
  (e: "add-account"): void;
  (e: "row-click", account: Account): void;

  // 批量操作事件
  (e: "batch-delete", accountIds: string[]): void;

  // 分页事件
  (e: "page-change", page: number): void;
  (e: "page-size-change", pageSize: number): void;
  (e: "pagination-change", data: { page: number; pageSize: number }): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  searchKeyword: "",
  showPagination: false,
  currentPage: 1,
  pageSize: 10,
  total: 0,
});

const emit = defineEmits<Emits>();

// 多选相关状态
const selectedRowKeys = ref<string[]>([]);

// 行选择配置
const rowSelection = computed(() => ({
  type: "checkbox",
  selectedRowKeys: selectedRowKeys.value,
  onChange: (selectedKeys: string[], selectedRows: any[]) => {
    console.log("rowSelection onChange:", { selectedKeys, selectedRows });
    selectedRowKeys.value = selectedKeys;
  },
  onSelect: (
    record: any,
    selected: boolean,
    selectedRows: any[],
    nativeEvent: Event
  ) => {
    console.log("rowSelection onSelect:", {
      recordId: record.id,
      selected,
      selectedRowsCount: selectedRows.length,
    });
  },
  onSelectAll: (selected: boolean, selectedRows: any[], changeRows: any[]) => {
    console.log("rowSelection onSelectAll:", {
      selected,
      selectedCount: selectedRows.length,
      changeCount: changeRows.length,
    });
  },
}));

// 表格选择事件处理
const handleTableSelect = (selectedKeys: string[]) => {
  console.log("handleTableSelect triggered:", selectedKeys);
  selectedRowKeys.value = selectedKeys;
};

const handleTableSelectAll = (event: any) => {
  console.log("handleTableSelectAll triggered:", event);
};

// 多选操作方法
const handleSelectAll = () => {
  selectedRowKeys.value = props.accounts.map((account) => account.id);
};

const handleClearSelection = () => {
  selectedRowKeys.value = [];
};

const handleBatchDelete = () => {
  if (selectedRowKeys.value.length > 0) {
    emit("batch-delete", [...selectedRowKeys.value]);
    selectedRowKeys.value = []; // 清空选择
  }
};

// 空状态文案
const emptyTitle = computed(() =>
  props.searchKeyword ? "未找到匹配的账号" : "暂无账号"
);

const emptyDescription = computed(() =>
  props.searchKeyword ? "请尝试其他关键词" : "点击右上角按钮添加您的第一个账号"
);

// 表格列配置 - 使用 slotName 属性来指定自定义渲染的列
const columns = [
  {
    title: "账号ID",
    dataIndex: "id",
    minWidth: 160,
    slotName: "id", // 自定义渲染
    align: "left",
  },
  {
    title: "平台",
    dataIndex: "platform",
    minWidth: 280,
    slotName: "platform", // 自定义渲染
    align: "left",
  },
  {
    title: "用户名",
    dataIndex: "username",
    minWidth: 250,
    slotName: "username", // 自定义渲染
    align: "left",
  },
  {
    title: "分类",
    dataIndex: "category_id",
    minWidth: 150,
    slotName: "category_id", // 自定义渲染
    align: "left",
  },
  {
    title: "更新时间",
    dataIndex: "updated_at",
    minWidth: 180,
    slotName: "updated_at", // 自定义渲染
    align: "center",
  },
  {
    title: "操作",
    dataIndex: "actions",
    width: 200,
    slotName: "actions", // 自定义渲染
    align: "center",
  },
];

// 事件处理
const handleRowClick = (event: any) => {
  emit("row-click", event.record);
};

// 业务逻辑函数
const getCategoryName = (categoryId?: string) => {
  if (!categoryId) return "";
  const category = props.categories.find((c) => c.id === categoryId);
  return category?.name || "";
};

const getCategoryColor = (categoryId?: string) => {
  if (!categoryId) return "#8c8c8c";
  const category = props.categories.find((c) => c.id === categoryId);
  return category?.color || "#8c8c8c";
};

// const getPlatformColor = (platform: string) => {
//   const colors = [
//     "#1890ff",
//     "#52c41a",
//     "#faad14",
//     "#f5222d",
//     "#722ed1",
//     "#13c2c2",
//     "#eb2f96",
//     "#fa541c",
//     "#2f54eb",
//     "#a0d911",
//   ];
//   const index = platform
//     .split("")
//     .reduce((acc, char) => acc + char.charCodeAt(0), 0);
//   return colors[index % colors.length];
// };

// 使用导入的工具方法
</script>

<style lang="scss" scoped>
// 批量操作工具栏样式
.batch-actions-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border: 1px solid #1890ff;
  border-radius: 8px;
  margin-bottom: 16px;
  animation: slideDown 0.3s ease-out;

  .batch-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #1890ff;
    font-size: 14px;

    .info-icon {
      font-size: 16px;
    }

    strong {
      color: #1565c0;
      font-weight: 600;
    }
  }

  .batch-actions {
    display: flex;
    gap: 8px;
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 账号表格专用样式
:deep(.base-table) {
  .id-cell {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;

    .id-text {
      font-family: "SF Mono", Monaco, Inconsolata, "Roboto Mono", Consolas,
        "Courier New", monospace;
      font-size: 12px;
      color: #8c8c8c;
      background: #f5f5f5;
      padding: 4px 8px;
      border-radius: 4px;
      border: 1px solid #e8e8e8;
      flex-shrink: 0;
      min-width: 80px;
    }

    .copy-btn {
      opacity: 0;
      transition: opacity 0.2s ease, transform 0.2s ease;
      flex-shrink: 0;

      &:hover {
        transform: scale(1.1);
      }
    }

    &:hover .copy-btn {
      opacity: 1;
    }
  }

  .platform-cell {
    display: flex;
    align-items: center;
    gap: 12px;
    width: 100%;

    .platform-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%; // 改为圆形
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 16px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      transition: transform 0.2s ease;
      flex-shrink: 0;

      &:hover {
        transform: scale(1.05);
      }
    }

    .platform-info {
      flex: 1;
      overflow: hidden;

      .platform-name {
        font-size: 14px;
        font-weight: 600;
        color: #262626;
        margin-bottom: 2px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .platform-url {
        .url-link {
          color: #1890ff;
          text-decoration: none;
          font-size: 12px;
          transition: color 0.2s ease;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          display: block;

          &:hover {
            color: #40a9ff;
            text-decoration: underline;
          }
        }
      }
    }
  }

  .copyable-cell {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;

    .cell-value {
      font-size: 14px;
      color: #262626;
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      &.email-text {
        color: #1890ff;
      }
    }

    .copy-btn {
      opacity: 0;
      transition: opacity 0.2s ease, transform 0.2s ease;
      flex-shrink: 0;

      &:hover {
        transform: scale(1.1);
      }
    }

    &:hover .copy-btn {
      opacity: 1;
    }
  }

  .category-tag {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 8px;
    background: rgba(0, 0, 0, 0.04);
    border-radius: 50px;
    width: fit-content;
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.05);
    }

    .category-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
    }

    .category-name {
      font-size: 12px;
      font-weight: 500;
      color: #262626;
    }
  }

  .time-cell {
    display: flex;
    align-items: center;
    gap: 6px;

    .time-icon {
      font-size: 12px;
      color: #bfbfbf;
    }

    .time-text {
      font-size: 12px;
      color: #8c8c8c;
    }
  }

  .action-cell {
    display: flex;
    align-items: center;
    gap: 8px;

    .view-btn,
    .delete-btn {
      transition: all 0.2s ease;

      &:hover {
        transform: scale(1.05);
      }
    }

    .view-btn:hover {
      background: rgba(24, 144, 255, 0.1);
      color: #1890ff;
    }

    .delete-btn:hover {
      background: rgba(255, 77, 79, 0.1);
      color: #ff4d4f;
    }
  }

  .empty-value {
    color: #c9cdd4;
    font-style: italic;
  }
}

// 响应式设计
@media (max-width: 768px) {
  :deep(.base-table) {
    .platform-cell {
      gap: 8px;

      .platform-avatar {
        width: 32px;
        height: 32px;
        font-size: 14px;
        border-radius: 50%; // 确保移动端也是圆形
      }

      .platform-info {
        .platform-name {
          font-size: 13px;
        }
      }
    }

    .copyable-cell,
    .password-cell {
      gap: 4px;

      .cell-value {
        font-size: 13px;
      }
    }
  }
}
</style>
