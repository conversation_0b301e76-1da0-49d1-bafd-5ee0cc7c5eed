<template>
  <div class="example-card">
    <div class="example-card__header">
      <h3 class="example-card__title">SCSS 样式示例</h3>
      <div class="example-card__actions">
        <button class="btn btn-primary btn-sm">主要按钮</button>
        <button class="btn btn-secondary btn-sm">次要按钮</button>
      </div>
    </div>

    <div class="example-card__content">
      <div class="example-form">
        <div class="form-item">
          <label class="form-label required">用户名</label>
          <input type="text" class="form-input" placeholder="请输入用户名" />
          <div class="form-error">用户名不能为空</div>
        </div>

        <div class="form-item">
          <label class="form-label">邮箱</label>
          <input type="email" class="form-input" placeholder="请输入邮箱" />
          <div class="form-help">请输入有效的邮箱地址</div>
        </div>
      </div>

      <div class="example-grid">
        <div class="grid-item" v-for="item in items" :key="item.id">
          <div class="item-icon" :style="{ backgroundColor: item.color }">
            {{ item.name.charAt(0) }}
          </div>
          <div class="item-content">
            <h4 class="item-title">{{ item.name }}</h4>
            <p class="item-description">{{ item.description }}</p>
          </div>
          <div class="item-actions">
            <button class="btn btn-sm">编辑</button>
          </div>
        </div>
      </div>
    </div>

    <div class="example-card__footer">
      <div class="toast toast-success">
        <div class="toast-content">
          <span class="toast-message">操作成功！</span>
          <button class="toast-close">&times;</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";

const items = ref([
  {
    id: 1,
    name: "示例项目 1",
    description: "这是一个使用 SCSS 变量和混入的示例",
    color: "#1890ff",
  },
  {
    id: 2,
    name: "示例项目 2",
    description: "展示响应式设计和工具类的使用",
    color: "#52c41a",
  },
  {
    id: 3,
    name: "示例项目 3",
    description: "演示 BEM 命名约定和组件样式",
    color: "#faad14",
  },
]);
</script>

<style lang="scss" scoped>
// 使用 BEM 命名约定的主组件
.example-card {
  @include card-shadow;
  padding: $spacing-lg;
  border-radius: $border-radius-lg;
  max-width: 800px;
  margin: $spacing-xl auto;

  // 使用嵌套选择器
  &__header {
    @include flex-center-between;
    margin-bottom: $spacing-lg;
    padding-bottom: $spacing-base;
    border-bottom: 1px solid $border-color-light;
  }

  &__title {
    margin: 0;
    font-size: $font-size-xl;
    font-weight: $font-weight-semibold;
    color: $text-color-primary;

    // 使用文本渐变混入
    @include text-gradient($primary-color, $primary-color-hover);
  }

  &__actions {
    @include flex-center;
    gap: $spacing-sm;
  }

  &__content {
    margin-bottom: $spacing-lg;
  }

  &__footer {
    padding-top: $spacing-base;
    border-top: 1px solid $border-color-light;
  }
}

// 表单示例
.example-form {
  margin-bottom: $spacing-xl;
  padding: $spacing-lg;
  background: $background-color-secondary;
  border-radius: $border-radius-base;

  .form-item {
    margin-bottom: $spacing-lg;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 网格布局示例
.example-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: $spacing-base;

  .grid-item {
    @include card-shadow;
    padding: $spacing-base;
    border-radius: $border-radius-base;
    @include smooth-transition(transform, box-shadow);

    &:hover {
      transform: translateY(-2px);
      box-shadow: $shadow-lg;
    }

    .item-icon {
      @include flex-center;
      width: 40px;
      height: 40px;
      border-radius: $border-radius-circle;
      color: $text-color-white;
      font-weight: $font-weight-bold;
      font-size: $font-size-lg;
      margin-bottom: $spacing-sm;
    }

    .item-content {
      margin-bottom: $spacing-sm;

      .item-title {
        margin: 0 0 $spacing-xs 0;
        font-size: $font-size-base;
        font-weight: $font-weight-medium;
        color: $text-color-primary;
        @include text-ellipsis;
      }

      .item-description {
        margin: 0;
        font-size: $font-size-sm;
        color: $text-color-secondary;
        line-height: $line-height-relaxed;
        @include text-multiline-ellipsis(2);
      }
    }

    .item-actions {
      @include flex-center;
      justify-content: flex-end;
    }
  }
}

// 工具类使用示例
.utility-examples {
  .text-example {
    @extend .text-center;
    @extend .text-primary;
    @extend .font-bold;
    @extend .text-lg;
  }

  .spacing-example {
    @extend .p-lg;
    @extend .mt-xl;
    @extend .mb-base;
  }

  .layout-example {
    @extend .flex;
    @extend .flex-center-between;
    @extend .items-center;
  }
}

// 响应式设计示例
@include mobile {
  .example-card {
    margin: $spacing-base;
    padding: $spacing-base;

    &__header {
      @include flex-column;
      align-items: flex-start;
      gap: $spacing-sm;
    }

    &__actions {
      width: 100%;
      justify-content: stretch;

      .btn {
        flex: 1;
      }
    }
  }

  .example-grid {
    grid-template-columns: 1fr;

    .grid-item {
      .item-content {
        .item-description {
          @include text-multiline-ellipsis(3);
        }
      }
    }
  }
}

@include tablet {
  .example-card {
    &__title {
      font-size: $font-size-lg;
    }
  }

  .example-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

// 深度选择器示例（影响子组件）
:deep(.arco-btn) {
  border-radius: $border-radius-lg;
  font-weight: $font-weight-medium;
  @include smooth-transition(all);
}

// 全局样式覆盖示例
:global(.custom-global-class) {
  background: $primary-color-light;
  padding: $spacing-sm;
  border-radius: $border-radius-base;
}

// 动画示例
.fade-enter-active,
.fade-leave-active {
  @include smooth-transition(opacity);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 主题变量使用示例
.theme-example {
  // 使用 CSS 变量（支持主题切换）
  background: var(--color-bg-2);
  color: var(--color-text-1);
  border: 1px solid var(--color-border-2);

  // 使用 SCSS 变量（编译时确定）
  padding: $spacing-base;
  border-radius: $border-radius-base;
  box-shadow: $shadow-sm;
}
</style>
