<template>
  <div class="mock-controller" :class="{ expanded: isExpanded }">
    <div class="mock-controller-toggle" @click="toggleExpand">
      <el-badge :is-dot="mockEnabled" type="success">
        <el-avatar size="small">M</el-avatar>
      </el-badge>
    </div>

    <div v-if="isExpanded" class="mock-controller-panel">
      <div class="mock-controller-header">
        <h3>Mock 系统控制面板</h3>
        <el-switch
          v-model="mockEnabledValue"
          @change="handleSwitchChange"
          size="small"
        />
      </div>

      <div class="mock-controller-content">
        <div class="mock-controller-item">
          <span>Mock 状态:</span>
          <span class="mock-status" :class="{ active: mockEnabled }">
            {{ mockEnabled ? "已启用" : "已禁用" }}
          </span>
        </div>

        <div class="mock-controller-item">
          <span>延迟时间 (ms):</span>
          <el-slider
            v-model="delay"
            :min="0"
            :max="2000"
            :step="100"
            show-ticks
          />
        </div>

        <div class="mock-controller-item">
          <el-button type="danger" size="small" plain @click="resetData">
            重置数据
          </el-button>
        </div>
      </div>

      <div class="mock-controller-footer">
        <span>
          <el-tag :type="mockEnabled ? 'success' : 'info'">
            {{ mockEnabled ? "使用Mock数据" : "使用真实接口" }}
          </el-tag>
        </span>
        <el-button type="text" size="small" @click="isExpanded = false">
          关闭
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from "vue";
import { useAccountsStore } from "../stores/accounts";
import { mockSystem } from "../utils/mock-system";

// 获取store
const accountsStore = useAccountsStore();

// 控制面板展开状态
const isExpanded = ref(false);

// 使用计算属性绑定mock状态
const mockEnabled = computed(() => mockSystem.isMockEnabled());

// 创建一个可变的值来绑定el-switch
const mockEnabledValue = computed({
  get: () => mockSystem.isMockEnabled(),
  set: (value) => handleSwitchChange(value),
});

// 延迟时间设置
const delay = computed({
  get: () => mockSystem.getDelay(),
  set: (value: number) => mockSystem.setDelay(value),
});

// 在组件挂载时同步状态
onMounted(() => {
  // 初始化时从localStorage或URL参数读取mock状态
  mockSystem.init();
  console.log("MockController mounted, mockEnabled:", mockEnabled.value);
});

// 处理开关变化
const handleSwitchChange = (value: boolean | string | number) => {
  // 确保转换为布尔值
  const boolValue = Boolean(value);
  console.log("Switch changed to:", boolValue);
  toggleMock(boolValue);
};

// 切换mock状态
const toggleMock = (value: boolean) => {
  console.log("Toggling mock to:", value);
  // 设置全局mock状态
  mockSystem.setEnabled(value);
  console.log("After setEnabled, mockEnabled is:", mockSystem.isMockEnabled());

  // 重新加载页面以应用更改
  // 在实际项目中，可能需要更复杂的逻辑来处理状态切换
  // 例如通知各个模块重新加载数据等
  console.log("Will reload page in 300ms");
  setTimeout(() => {
    window.location.reload();
  }, 300);
};

// 重置数据
const resetData = () => {
  console.log("Resetting mock data");
  mockSystem.resetData();
};

// 切换展开状态
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
  console.log("Panel expanded:", isExpanded.value);
};

// 监听ESC键关闭面板
watch(isExpanded, (value) => {
  if (value) {
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        isExpanded.value = false;
      }
    };
    document.addEventListener("keydown", handleEsc);
    return () => {
      document.removeEventListener("keydown", handleEsc);
    };
  }
});
</script>

<style lang="scss" scoped>
.mock-controller {
  position: fixed;
  right: 20px;
  bottom: 20px;
  z-index: 1000;

  &-toggle {
    cursor: pointer;
    background-color: $background-color-secondary;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: $shadow-base;
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.1);
      box-shadow: $shadow-lg;
    }
  }

  &-panel {
    position: absolute;
    bottom: 50px;
    right: 0;
    width: 280px;
    background-color: $background-color-primary;
    border-radius: $border-radius-base;
    box-shadow: $shadow-lg;
    overflow: hidden;
  }

  &-header {
    padding: $spacing-sm;
    background-color: $background-color-secondary;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      font-size: $font-size-sm;
      font-weight: $font-weight-medium;
    }
  }

  &-content {
    padding: $spacing-sm;
  }

  &-item {
    margin-bottom: $spacing-xs;
    padding: $spacing-xs 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    border-radius: $border-radius-base;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: $background-color-secondary;
    }

    span {
      font-size: $font-size-sm;
      color: $text-color-secondary;
    }
  }

  &-footer {
    padding: $spacing-xs $spacing-sm;
    border-top: 1px solid $border-color-light;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.mock-status {
  font-size: $font-size-xs;
  color: $text-color-disabled;

  &.active {
    color: $success-color;
    font-weight: $font-weight-medium;
  }
}

// 动画
.expanded {
  .mock-controller-toggle {
    box-shadow: $shadow-xl;
    background-color: rgba($primary-color, 0.1);
  }
}
</style>
