<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-22 22:04:51
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-24 08:10:34
 * @FilePath     : /src/components/BaseTable.vue
 * @Description  : 基础表格组件 - 使用 Element Plus
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-22 22:04:51
-->
<template>
  <div class="base-table">
    <!-- 表格 -->
    <div class="table-wrapper">
      <el-table
        :data="paginatedData"
        :loading="loading"
        :row-key="rowKey"
        :border="true"
        :size="size"
        :stripe="stripe"
        :highlight-current-row="hoverable"
        :empty-text="emptyDescription"
        :element-loading-text="loadingText"
        :header-cell-style="{
          color: '#303133',
          fontWeight: '500',
          fontSize: '14px',
          textAlign: 'left',
          borderBottom: '1px solid #EBEEF5',
          backgroundColor: '#FAFAFA',
          padding: '12px 16px',
          height: '48px',
        }"
        style="width: 100%"
        class="custom-base-table"
        v-bind="$attrs"
        @select="$emit('select', $event)"
        @select-all="$emit('select-all', $event)"
        @selection-change="$emit('selection-change', $event)"
        @expand-change="$emit('expand-change', $event)"
        @sort-change="$emit('sort-change', $event)"
        @filter-change="$emit('filter-change', $event)"
        @cell-click="$emit('cell-click', $event)"
        @row-click="$emit('row-click', $event)"
        @header-click="$emit('header-click', $event)"
        @cell-dblclick="$emit('cell-dblclick', $event)"
        @row-dblclick="$emit('row-dblclick', $event)"
        @cell-contextmenu="$emit('cell-contextmenu', $event)"
        @row-contextmenu="$emit('row-contextmenu', $event)"
      >
        <!-- 动态生成列 -->
        <template
          v-for="column in processedColumns"
          :key="column.prop || column.dataIndex"
        >
          <!-- 选择列 -->
          <el-table-column
            v-if="column.type === 'selection'"
            type="selection"
            :width="column.width || 55"
            :fixed="column.fixed"
          />
          <!-- 索引列 -->
          <el-table-column
            v-else-if="column.type === 'index'"
            type="index"
            :label="column.label || column.title || '#'"
            :width="column.width || 60"
            :fixed="column.fixed"
          />
          <!-- 展开列 -->
          <el-table-column
            v-else-if="column.type === 'expand'"
            type="expand"
            :width="column.width || 55"
            :fixed="column.fixed"
          >
            <template #default="scope">
              <slot name="expand" :row="scope.row" :index="scope.$index" />
            </template>
          </el-table-column>
          <!-- 普通列 -->
          <el-table-column
            v-else
            :prop="column.prop || column.dataIndex"
            :label="column.label || column.title"
            :width="column.width"
            :min-width="column.minWidth"
            :fixed="column.fixed"
            :sortable="column.sortable"
            :sort-by="column.sortBy"
            :sort-orders="column.sortOrders"
            :resizable="column.resizable !== false"
            :show-overflow-tooltip="column.showOverflowTooltip !== false"
            :align="column.align || 'left'"
            :header-align="column.headerAlign || column.align || 'left'"
            :class-name="column.className"
            :label-class-name="column.labelClassName"
            :filters="column.filters"
            :filter-method="column.filterMethod"
            :filter-multiple="column.filterMultiple"
            :filter-placement="column.filterPlacement"
          >
            <!-- 自定义表头 -->
            <template v-if="column.headerSlot" #header="scope">
              <slot
                :name="column.headerSlot"
                :column="scope.column"
                :index="scope.$index"
              />
            </template>

            <!-- 自定义内容 -->
            <template #default="scope">
              <slot
                v-if="column.slotName || column.slot"
                :name="column.slotName || column.slot"
                :row="scope.row"
                :column="scope.column"
                :index="scope.$index"
                :value="scope.row[column.prop || column.dataIndex]"
              >
                <!-- 默认渲染：显示原始数据 -->
                {{ scope.row[column.prop || column.dataIndex] || "-" }}
              </slot>
              <span v-else>
                {{ scope.row[column.prop || column.dataIndex] || "-" }}
              </span>
            </template>
          </el-table-column>
        </template>

        <!-- 空状态插槽 -->
        <template #empty>
          <div class="empty-state">
            <el-icon :size="64" color="#c9cdd4">
              <User />
            </el-icon>
            <h3>{{ emptyTitle }}</h3>
            <p>{{ emptyDescription }}</p>
            <slot name="empty-action"></slot>
          </div>
        </template>
      </el-table>

      <!-- 分页器 -->
      <div v-if="showPagination && totalRecords > 0" class="custom-pagination">
        <div class="pagination-info">
          <span>
            显示 {{ startRecord }}-{{ endRecord }} 条，共
            {{ totalRecords }} 条记录
          </span>
        </div>

        <div class="pagination-controls">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="currentPageSize"
            :total="totalRecords"
            :page-sizes="pageSizeOptions"
            :layout="paginationLayout"
            :small="paginationSize === 'small'"
            :background="true"
            @current-change="handlePageChange"
            @size-change="handlePageSizeChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { User } from "@element-plus/icons-vue";

interface Props {
  // 基础数据
  columns: any[];
  data: any[];
  loading?: boolean;

  // 分页配置
  pagination?: any;
  showPagination?: boolean;
  currentPage?: number;
  pageSize?: number;
  total?: number;
  pageSizeOptions?: number[];
  showPageSizeSelector?: boolean;
  showJumper?: boolean;
  simplePagination?: boolean;
  paginationSize?: "small" | "default" | "large";
  paginationLayout?: string;

  // 表格配置
  rowKey?: string | ((record: any) => string);
  scroll?: { x?: number | string; y?: number | string };
  bordered?: boolean;
  size?: "mini" | "small" | "medium" | "large";
  stripe?: boolean;
  hoverable?: boolean;

  // 选择配置
  rowSelection?: any;
  selectedKeys?: (string | number)[];

  // 展开配置
  expandable?: any;
  expandedKeys?: (string | number)[];
  defaultExpandAllRows?: boolean;
  hideExpandButtonOnEmpty?: boolean;

  // 其他配置
  virtualListProps?: any;
  tableLayoutFixed?: boolean;
  hideHeader?: boolean;
  showHeader?: boolean;
  filterIconAlignLeft?: boolean;
  spanMethod?: any;
  spanAll?: boolean;
  draggable?: any;
  stickyHeader?: boolean | number;
  summary?: boolean;
  summaryText?: string;
  summarySpanMethod?: any;

  // 空状态配置
  emptyTitle?: string;
  emptyDescription?: string;
  loadingText?: string;
}

interface Emits {
  // 选择事件
  (e: "select", event: any): void;
  (e: "select-all", event: any): void;
  (e: "selection-change", event: any): void;

  // 展开事件
  (e: "expand", event: any): void;
  (e: "expanded-change", event: any): void;

  // 排序筛选事件
  (e: "sorter-change", event: any): void;
  (e: "filter-change", event: any): void;

  // 分页事件
  (e: "page-change", page: number): void;
  (e: "page-size-change", pageSize: number): void;
  (e: "pagination-change", data: { page: number; pageSize: number }): void;

  // 点击事件
  (e: "cell-click", event: any): void;
  (e: "row-click", event: any): void;
  (e: "header-click", event: any): void;
  (e: "cell-dblclick", event: any): void;
  (e: "row-dblclick", event: any): void;
  (e: "cell-contextmenu", event: any): void;
  (e: "row-contextmenu", event: any): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  pagination: false,
  showPagination: false,
  currentPage: 1,
  pageSize: 10,
  total: 0,
  pageSizeOptions: () => [10, 20, 50, 100],
  showPageSizeSelector: true,
  showJumper: true,
  simplePagination: false,
  paginationSize: "default",
  paginationLayout: "total, sizes, prev, pager, next, jumper",
  rowKey: "id",
  bordered: false,
  size: "large",
  stripe: false,
  hoverable: true,
  tableLayoutFixed: true,
  hideHeader: false,
  showHeader: true,
  filterIconAlignLeft: false,
  spanAll: false,
  stickyHeader: false,
  summary: false,
  defaultExpandAllRows: false,
  hideExpandButtonOnEmpty: false,
  emptyTitle: "暂无数据",
  emptyDescription: "当前没有可显示的数据",
  loadingText: "加载中...",
});

const emit = defineEmits<Emits>();

// 分页相关计算属性和响应式数据

const currentPage = ref(props.currentPage);
const currentPageSize = ref(props.pageSize);

// 处理列配置，兼容 Arco Design 和 Element Plus 的列配置
const processedColumns = computed(() => {
  return props.columns.map((column) => {
    return {
      ...column,
      // 兼容 Arco Design 的 dataIndex 和 Element Plus 的 prop
      prop: column.prop || column.dataIndex,
      // 兼容 Arco Design 的 title 和 Element Plus 的 label
      label: column.label || column.title,
      // 兼容插槽名称
      slotName: column.slotName || column.slot,
    };
  });
});

// 总记录数
const totalRecords = computed(() => props.total || props.data.length);

// 分页后的数据
const paginatedData = computed(() => {
  if (!props.showPagination) {
    return props.data;
  }

  const start = (currentPage.value - 1) * currentPageSize.value;
  const end = start + currentPageSize.value;
  return props.data.slice(start, end);
});

// 当前页显示的记录范围
const startRecord = computed(() => {
  if (totalRecords.value === 0) return 0;
  return (currentPage.value - 1) * currentPageSize.value + 1;
});

const endRecord = computed(() => {
  const end = currentPage.value * currentPageSize.value;
  return Math.min(end, totalRecords.value);
});

// 分页事件处理
const handlePageChange = (page: number) => {
  currentPage.value = page;
  emit("page-change", page);
  emit("pagination-change", { page, pageSize: currentPageSize.value });
};

const handlePageSizeChange = (pageSize: number) => {
  currentPageSize.value = pageSize;
  currentPage.value = 1; // 重置到第一页
  emit("page-size-change", pageSize);
  emit("pagination-change", { page: 1, pageSize });
};

// 监听外部 props 变化
watch(
  () => props.currentPage,
  (newPage) => {
    currentPage.value = newPage;
  }
);

watch(
  () => props.pageSize,
  (newPageSize) => {
    currentPageSize.value = newPageSize;
  }
);
</script>

<style lang="scss" scoped>
.base-table {
  width: 100%;

  .table-wrapper {
    width: 100%;
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 20px;
    text-align: center;

    h3 {
      margin: 16px 0 8px 0;
      color: #262626;
      font-size: 20px;
      font-weight: 600;
    }

    p {
      margin: 0 0 24px 0;
      color: #8c8c8c;
      font-size: 14px;
    }
  }

  :deep(.custom-base-table) {
    border: 1px solid #dcdfe6;
    border-radius: 8px;
    overflow: hidden;
    width: 100%;
    table-layout: fixed;

    .el-table {
      border-radius: 8px;
      border: none;
      width: 100%;
      table-layout: fixed;
    }

    .el-table__header th {
      background: linear-gradient(180deg, #fafafa 0%, #f5f5f5 100%);
      color: #303133;
      font-weight: 500;
      font-size: 14px;
      text-align: left;
      border-bottom: 1px solid #ebeef5;
      border-right: 1px solid #ebeef5;
      padding: 12px 16px;
      height: 48px;
      position: relative;

      &:hover {
        background: linear-gradient(180deg, #f8f8f8 0%, #f0f0f0 100%);
      }

      &:last-child {
        border-right: none;
      }

      .cell {
        font-weight: 500;
        color: #303133;
        line-height: 1.5;
      }
    }

    .el-table__cell {
      border-bottom: 1px solid #ebeef5;
      white-space: nowrap;
      padding: 12px 16px;
    }

    .el-table__body td {
      padding: 12px 16px;
      height: 52px;
      vertical-align: middle;
      border-right: 1px solid #ebeef5;
      background-color: #ffffff;

      &:last-child {
        border-right: none;
      }

      .cell {
        color: #606266;
        font-size: 14px;
        line-height: 1.5;
      }
    }

    .el-table__row {
      &:hover td {
        background-color: #f5f7fa !important;
      }

      &:nth-child(even) td {
        background-color: #fafafa;
      }
    }

    // 确保固定列正确显示
    .el-table__fixed-right {
      right: 0 !important;
    }

    .el-table__fixed-left {
      left: 0 !important;
    }

    // 表格主体滚动区域 - 只在内容真正超出时才滚动
    .el-table__body-wrapper {
      overflow-x: hidden;
    }

    .el-table__header-wrapper {
      overflow-x: hidden;
    }

    // 空状态样式
    .el-table__empty-block {
      background: transparent;
    }

    // 加载状态样式
    .el-loading-mask {
      border-radius: 12px;
    }
  }

  .table-wrapper {
    .custom-pagination {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 0;
      margin-top: 16px;
      border-top: 1px solid #f0f0f0;

      .pagination-info {
        span {
          color: #8c8c8c;
          font-size: 14px;
        }
      }

      .pagination-controls {
        :deep(.el-pagination) {
          .el-pager {
            li {
              border-radius: 6px;
              transition: all 0.2s ease;

              &:hover {
                transform: translateY(-1px);
              }

              &.is-active {
                background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
                border-color: #409eff;
                box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
              }
            }
          }

          .el-input__wrapper {
            border-radius: 6px;
            transition: all 0.2s ease;

            &:focus-within {
              box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
            }
          }

          .el-select {
            .el-select__wrapper {
              border-radius: 6px;
            }
          }

          .btn-prev,
          .btn-next {
            border-radius: 6px;
            transition: all 0.2s ease;

            &:hover {
              transform: translateY(-1px);
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .base-table {
    :deep(.custom-base-table) {
      .el-table__body-wrapper {
        .el-table__body {
          tr {
            td {
              padding: 8px 12px;
            }
          }
        }
      }
    }

    .table-wrapper {
      .custom-pagination {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;

        .pagination-info {
          text-align: center;
        }

        .pagination-controls {
          display: flex;
          justify-content: center;

          :deep(.el-pagination) {
            .el-pagination__sizes {
              margin-left: 8px;
            }
          }
        }
      }
    }
  }
}
</style>
