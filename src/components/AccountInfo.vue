<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-28 11:20:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-06-29 00:35:01
 * @FilePath     : /src/components/AccountInfo.vue
 * @Description  : 通用账号信息显示组件，基于字段配置动态显示账号信息
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-28 11:20:00
-->

<template>
  <div class="account-info">
    <!-- 按分组显示字段 -->
    <template v-for="group in groupedFields" :key="group.key">
      <div
        v-if="
          group.fields.length > 0 ||
          (group.key === 'custom' && customFields.length > 0)
        "
        class="info-section"
      >
        <h4 v-if="showGroupTitles" class="section-title">{{ group.title }}</h4>

        <!-- 动态网格布局 -->
        <div class="info-grid">
          <div
            v-for="field in group.fields"
            :key="field.key"
            :class="['info-item', { 'full-width': field.gridSpan === 12 }]"
          >
            <label class="info-label">{{ field.label }}</label>

            <div class="info-value">
              <!-- 分类字段特殊处理 -->
              <template v-if="field.key === 'category_id'">
                <div
                  v-if="getCategoryInfo(getFieldValue(field.key))"
                  class="category-tag"
                >
                  <div
                    class="category-dot"
                    :style="{
                      backgroundColor: getCategoryInfo(getFieldValue(field.key))
                        ?.color,
                    }"
                  ></div>
                  <span>{{
                    getCategoryInfo(getFieldValue(field.key))?.name
                  }}</span>
                </div>
                <span v-else class="empty-text">未分类</span>
              </template>

              <!-- 密码字段特殊处理 -->
              <template v-else-if="field.key === 'password'">
                <div class="password-section">
                  <div class="password-display">
                    <span v-if="!showPassword" class="password-masked"
                      >••••••••</span
                    >
                    <span v-else class="password-text">{{
                      decryptedPassword || "解密中..."
                    }}</span>
                  </div>
                  <div class="password-actions">
                    <el-button
                      text
                      size="small"
                      @click="togglePassword"
                      :loading="passwordLoading"
                      title="显示/隐藏密码"
                    >
                      <el-icon>
                        <Hide v-if="showPassword" />
                        <View v-else />
                      </el-icon>
                    </el-button>
                    <el-button
                      text
                      size="small"
                      @click="copyPassword"
                      :disabled="!decryptedPassword"
                      title="复制密码"
                    >
                      <el-icon><CopyDocument /></el-icon>
                    </el-button>
                  </div>
                </div>
              </template>

              <!-- 网址字段特殊处理 -->
              <template v-else-if="field.linkable && getFieldValue(field.key)">
                <a
                  :href="getFieldValue(field.key)"
                  target="_blank"
                  class="website-link"
                >
                  {{ getFieldValue(field.key) }}
                </a>
              </template>

              <!-- 邮箱字段特殊处理 -->
              <template
                v-else-if="field.key === 'email' && getFieldValue(field.key)"
              >
                <span class="email-text">{{ getFieldValue(field.key) }}</span>
                <el-button
                  text
                  size="small"
                  @click="handleCopy(getFieldValue(field.key))"
                  title="复制邮箱"
                >
                  <el-icon><CopyDocument /></el-icon>
                </el-button>
              </template>

              <!-- 备注字段特殊处理 -->
              <template
                v-else-if="field.key === 'notes' && getFieldValue(field.key)"
              >
                <span class="notes-text">{{ getFieldValue(field.key) }}</span>
              </template>

              <!-- 普通字段 -->
              <template v-else>
                <span v-if="getFieldValue(field.key)">{{
                  getFieldValue(field.key)
                }}</span>
                <span v-else class="empty-text">
                  {{ getEmptyText(field.key) }}
                </span>

                <!-- 可复制字段的复制按钮 -->
                <el-button
                  v-if="field.copyable && getFieldValue(field.key)"
                  text
                  size="small"
                  @click="handleCopy(getFieldValue(field.key))"
                  :title="`复制${field.label}`"
                >
                  <el-icon><CopyDocument /></el-icon>
                </el-button>
              </template>
            </div>
          </div>

          <!-- 自定义字段显示（仅在custom分组中） -->
          <template v-if="group.key === 'custom'">
            <div
              v-for="customField in customFields"
              :key="customField.key"
              class="info-item"
            >
              <label class="info-label">{{ customField.label }}</label>
              <div class="info-value">
                <span v-if="customField.value">{{ customField.value }}</span>
                <span v-else class="empty-text">未设置</span>
                <!-- 自定义字段也可以复制 -->
                <el-button
                  v-if="customField.value"
                  text
                  size="small"
                  @click="handleCopy(customField.value)"
                  :title="`复制${customField.label}`"
                >
                  <el-icon><CopyDocument /></el-icon>
                </el-button>
              </div>
            </div>
          </template>
        </div>
      </div>
    </template>

    <!-- 时间信息 -->
    <div v-if="showTimeInfo" class="info-section">
      <h4 class="section-title">时间信息</h4>
      <div class="info-grid">
        <div class="info-item">
          <label class="info-label">创建时间</label>
          <div class="info-value">
            <span>{{ formatDateTime(accountData.created_at) }}</span>
          </div>
        </div>
        <div class="info-item">
          <label class="info-label">更新时间</label>
          <div class="info-value">
            <span>{{ formatDateTime(accountData.updated_at) }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { View, Hide, CopyDocument } from "@element-plus/icons-vue";
import {
  copyToClipboard,
  formatDateTime,
  showWarning,
  showError,
} from "../utils";
import { FIELD_GROUPS, getFieldsByGroup } from "../config/account-fields";
import type { Account } from "../stores/accounts";

interface Props {
  accountData: Account;
  showGroupTitles?: boolean;
  showTimeInfo?: boolean;
  categories?: Array<{ id: string; name: string; color?: string }>;
  onDecryptPassword?: (encryptedPassword: string) => Promise<string>;
}

interface Emits {
  (e: "copy", value: string): void;
  (e: "togglePassword"): void;
}

const props = withDefaults(defineProps<Props>(), {
  showGroupTitles: true,
  showTimeInfo: true,
  categories: () => [],
});

const emit = defineEmits<Emits>();

const showPassword = ref(false);
const decryptedPassword = ref("");
const passwordLoading = ref(false);

// 按分组整理字段
const groupedFields = computed(() => {
  const groups = Object.entries(FIELD_GROUPS)
    .sort(([, a], [, b]) => a.order - b.order)
    .map(([key, value]) => ({
      key,
      title: value.title,
      fields: getFieldsByGroup(key),
    }));

  return groups;
});

// 提取自定义字段
const customFields = computed(() => {
  const fields: Array<{ key: string; label: string; value: string }> = [];

  if (
    props.accountData.custom_fields &&
    typeof props.accountData.custom_fields === "object"
  ) {
    Object.entries(props.accountData.custom_fields).forEach(
      ([label, value]) => {
        if (label && value) {
          fields.push({
            key: `custom_${label}`,
            label,
            value: value as string,
          });
        }
      }
    );
  }

  return fields;
});

// 安全获取字段值的方法
const getFieldValue = (fieldKey: string): string => {
  const accountRecord = props.accountData as Record<string, any>;
  return accountRecord[fieldKey] || "";
};

// 获取分类信息
const getCategoryInfo = (categoryId?: string) => {
  if (!categoryId) return null;
  return props.categories.find((cat) => cat.id === categoryId);
};

// 获取空值显示文本
const getEmptyText = (fieldKey: string) => {
  const emptyTexts: Record<string, string> = {
    display_name: "未设置",
    email: "未设置",
    phone: "未设置",
    website_url: "未设置",
    notes: "无备注",
    category_id: "未分类",
  };
  return emptyTexts[fieldKey] || "未设置";
};

// 切换密码显示/隐藏
const togglePassword = async () => {
  if (!props.onDecryptPassword) {
    showWarning("密码解密功能未配置");
    return;
  }

  if (showPassword.value) {
    showPassword.value = false;
    decryptedPassword.value = "";
  } else {
    try {
      passwordLoading.value = true;
      const password = await props.onDecryptPassword(
        props.accountData.encrypted_password
      );
      decryptedPassword.value = password;
      showPassword.value = true;
      emit("togglePassword");
    } catch (error) {
      console.error("Failed to decrypt password:", error);
      showError("密码解密失败");
    } finally {
      passwordLoading.value = false;
    }
  }
};

// 复制密码
const copyPassword = async () => {
  if (decryptedPassword.value) {
    await copyToClipboard(decryptedPassword.value, "密码已复制");
    emit("copy", decryptedPassword.value);
  } else {
    showWarning("请先查看密码");
  }
};

// 复制文本的处理方法
const handleCopy = async (text: string) => {
  await copyToClipboard(text);
  emit("copy", text);
};
</script>

<style lang="scss" scoped>
.account-info {
  .info-section {
    margin-bottom: 32px;

    &:last-child {
      margin-bottom: 0;
    }

    .section-title {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
      padding: 0 0 8px 0;
      border-bottom: 2px solid #e5e7eb;
      position: relative;

      &::before {
        content: "";
        position: absolute;
        left: 0;
        bottom: -2px;
        width: 60px;
        height: 2px;
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      }
    }

    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 24px;

      // 在中等屏幕上使用3列
      @media (min-width: 900px) {
        grid-template-columns: 1fr 1fr 1fr;
        gap: 28px;
      }

      // 在大屏幕上使用更多列
      @media (min-width: 1200px) {
        grid-template-columns: 1fr 1fr 1fr 1fr;
        gap: 32px;
      }

      @media (min-width: 1600px) {
        grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
        gap: 40px;
      }

      .info-item {
        padding: 12px 0;
        border-bottom: 1px solid #f3f4f6;

        &:last-child {
          border-bottom: none;
        }

        &.full-width {
          // 在小屏幕上占满整行
          grid-column: 1 / -1;

          // 在中等屏幕及以上，让网站地址等字段正常排列
          @media (min-width: 900px) {
            grid-column: auto;
          }
        }

        .info-label {
          display: block;
          margin-bottom: 4px;
          font-weight: 500;
          color: #6b7280;
          font-size: 13px;
        }

        .info-value {
          display: flex;
          align-items: center;
          justify-content: space-between;
          min-height: 24px;

          span {
            color: #1f2937;
            font-size: 14px;
            font-weight: 500;

            &.empty-text {
              color: #9ca3af;
              font-style: italic;
              font-weight: 400;
            }

            &.email-text {
              color: #3b82f6;
            }

            &.notes-text {
              line-height: 1.5;
              white-space: pre-wrap;
              font-weight: 400;
            }
          }

          .website-link {
            color: #1890ff;
            text-decoration: none;
            transition: color 0.2s ease;

            &:hover {
              color: #40a9ff;
              text-decoration: underline;
            }
          }

          .category-tag {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 4px 12px;
            background: rgba(0, 0, 0, 0.04);
            border-radius: 50px;

            .category-dot {
              width: 8px;
              height: 8px;
              border-radius: 50%;
            }
          }

          .password-section {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;

            .password-display {
              .password-masked {
                color: #8c8c8c;
                font-family: monospace;
                letter-spacing: 2px;
              }

              .password-text {
                font-family: monospace;
                background: #f0f7ff;
                padding: 4px 8px;
                border-radius: 4px;
                border: 1px solid #d1e9ff;
                color: #0050b3;
              }
            }

            .password-actions {
              display: flex;
              align-items: center;
              gap: 4px;
            }
          }

          // 优化按钮样式
          :deep(.el-button) {
            &.is-text {
              padding: 4px;
              min-height: 24px;

              .el-icon {
                font-size: 14px;
              }
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .account-info {
    .info-section {
      margin-bottom: 24px;

      .section-title {
        font-size: 15px;
        margin-bottom: 12px;

        &::before {
          width: 40px;
        }
      }

      .info-grid {
        grid-template-columns: 1fr;
        gap: 8px;
      }

      .info-item {
        padding: 8px 0;

        .info-label {
          font-size: 12px;
          margin-bottom: 4px;
        }

        .info-value span {
          font-size: 13px;
        }
      }
    }
  }
}
</style>
