<template>
  <el-container class="app-layout">
    <el-aside
      :width="collapsed ? '64px' : '200px'"
      class="app-sider"
      :class="{ collapsed }"
    >
      <div class="logo">
        <span v-if="!collapsed">账号管理</span>
        <span v-else">账</span>
      </div>

      <div class="custom-menu">
        <div
          class="menu-item"
          :class="{ active: currentRoute === 'dashboard' }"
          @click="navigateTo('/dashboard')"
        >
          <el-icon class="menu-icon"><Odometer /></el-icon>
          <span v-if="!collapsed" class="menu-text">仪表板</span>
        </div>

        <!-- 账号管理菜单（带子菜单） -->
        <div
          class="menu-item"
          :class="{
            active:
              currentRoute === 'accounts' ||
              currentRoute === 'account-new' ||
              currentRoute === 'categories',
          }"
          @click="(e) => toggleAccountsSubmenu(e)"
        >
          <el-icon class="menu-icon"><User /></el-icon>
          <span v-if="!collapsed" class="menu-text">账号管理</span>
          <el-icon
            v-if="!collapsed"
            class="submenu-arrow"
            :class="{ 'arrow-rotate': showAccountsSubmenu }"
          ><ArrowDown /></el-icon>
        </div>

        <!-- 账号管理子菜单 -->
        <div class="submenu" v-if="showAccountsSubmenu && !collapsed">
          <div
            class="submenu-item"
            :class="{ active: currentRoute === 'accounts' }"
            @click.stop="navigateTo('/accounts')"
          >
            <el-icon class="submenu-icon"><User /></el-icon>
            <span class="submenu-text">所有账号</span>
          </div>

          <div
            class="submenu-item"
            :class="{ active: currentRoute === 'account-new' }"
            @click.stop="navigateTo('/account/new')"
          >
            <el-icon class="submenu-icon"><Plus /></el-icon>
            <span class="submenu-text">添加账号</span>
          </div>

          <div
            class="submenu-item"
            :class="{ active: currentRoute === 'categories' }"
            @click.stop="navigateTo('/categories')"
          >
            <el-icon class="submenu-icon"><Collection /></el-icon>
            <span class="submenu-text">分类管理</span>
          </div>
        </div>

        <!-- 账号管理折叠状态下的子菜单项 -->
        <template v-if="collapsed">
          <el-tooltip content="所有账号" placement="right">
            <div
              class="menu-item collapsed-submenu-item"
              :class="{ active: currentRoute === 'accounts' }"
              @click.stop="navigateTo('/accounts')"
            >
              <el-icon class="menu-icon"><User /></el-icon>
            </div>
          </el-tooltip>

          <el-tooltip content="添加账号" placement="right">
            <div
              class="menu-item collapsed-submenu-item"
              :class="{ active: currentRoute === 'account-new' }"
              @click.stop="navigateTo('/account/new')"
            >
              <el-icon class="menu-icon"><Plus /></el-icon>
            </div>
          </el-tooltip>

          <el-tooltip content="分类管理" placement="right">
            <div
              class="menu-item collapsed-submenu-item"
              :class="{ active: currentRoute === 'categories' }"
              @click.stop="navigateTo('/categories')"
            >
              <el-icon class="menu-icon"><Collection /></el-icon>
            </div>
          </el-tooltip>
        </template>

        <div
          class="menu-item"
          :class="{
            active:
              currentRoute === 'finance' ||
              currentRoute === 'finance-dashboard' ||
              currentRoute === 'finance-list' ||
              currentRoute === 'finance-settings',
          }"
          @click="(e) => toggleFinanceSubmenu(e)"
        >
          <el-icon class="menu-icon"><Moon /></el-icon>
          <span v-if="!collapsed" class="menu-text">财务</span>
          <el-icon
            v-if="!collapsed"
            class="submenu-arrow"
            :class="{ 'arrow-rotate': showFinanceSubmenu }"
          ><ArrowDown /></el-icon>
        </div>

        <div class="submenu" v-if="showFinanceSubmenu && !collapsed">
          <div
            class="submenu-item"
            :class="{ active: currentRoute === 'finance-dashboard' }"
            @click.stop="navigateTo('/finance/dashboard')"
          >
            <el-icon class="submenu-icon"><Odometer /></el-icon>
            <span class="submenu-text">仪表板</span>
          </div>

          <div
            class="submenu-item"
            :class="{ active: currentRoute === 'finance-list' }"
            @click.stop="navigateTo('/finance/list')"
          >
            <el-icon class="submenu-icon"><List /></el-icon>
            <span class="submenu-text">列表</span>
          </div>

          <div
            class="submenu-item"
            :class="{ active: currentRoute === 'finance-settings' }"
            @click.stop="navigateTo('/finance/settings')"
          >
            <el-icon class="submenu-icon"><Setting /></el-icon>
            <span class="submenu-text">设置</span>
          </div>
        </div>

        <!-- 财务折叠状态下的子菜单项 -->
        <template v-if="collapsed">
          <el-tooltip content="财务仪表板" placement="right">
            <div
              class="menu-item collapsed-submenu-item"
              :class="{ active: currentRoute === 'finance-dashboard' }"
              @click.stop="navigateTo('/finance/dashboard')"
            >
              <el-icon class="menu-icon"><Odometer /></el-icon>
            </div>
          </el-tooltip>

          <el-tooltip content="财务列表" placement="right">
            <div
              class="menu-item collapsed-submenu-item"
              :class="{ active: currentRoute === 'finance-list' }"
              @click.stop="navigateTo('/finance/list')"
            >
              <el-icon class="menu-icon"><List /></el-icon>
            </div>
          </el-tooltip>

          <el-tooltip content="财务设置" placement="right">
            <div
              class="menu-item collapsed-submenu-item"
              :class="{ active: currentRoute === 'finance-settings' }"
              @click.stop="navigateTo('/finance/settings')"
            >
              <el-icon class="menu-icon"><Setting /></el-icon>
            </div>
          </el-tooltip>
        </template>

        <div
          class="menu-item"
          :class="{ active: currentRoute === 'settings' }"
          @click="navigateTo('/settings')"
        >
          <el-icon class="menu-icon"><Setting /></el-icon>
          <span v-if="!collapsed" class="menu-text">设置</span>
        </div>
      </div>
    </el-aside>

    <el-container>
      <el-header class="app-header">
        <div class="header-left">
          <el-button text size="large" @click="toggleCollapse">
            <el-icon>
              <Expand v-if="collapsed" />
              <Fold v-else />
            </el-icon>
          </el-button>

          <el-breadcrumb class="breadcrumb" separator="/">
            <el-breadcrumb-item v-for="item in breadcrumbs" :key="item.path">
              {{ item.name }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>

        <div class="header-right">
          <div class="header-actions">
            <el-button text size="large" @click="lockApp">
              <el-icon><Lock /></el-icon>
              锁定
            </el-button>

            <el-dropdown @command="onUserMenuSelect">
              <el-button text size="large">
                <el-icon><User /></el-icon>
                用户
                <el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="lock">
                    <el-icon><Lock /></el-icon>
                    锁定应用
                  </el-dropdown-item>
                  <el-dropdown-item command="logout">
                    <el-icon><SwitchButton /></el-icon>
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </el-header>

      <el-main class="app-content">
        <slot />
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessageBox } from "element-plus";
import {
  Odometer,
  User,
  Setting,
  Lock,
  SwitchButton,
  ArrowDown,
  Expand,
  Fold,
  Moon,
  List,
  Plus,
  Collection,
} from "@element-plus/icons-vue";
import { useAuthStore } from "../stores/auth";

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();

const collapsed = ref(false);
const showFinanceSubmenu = ref(false);
const showAccountsSubmenu = ref(false);

// 监听路由变化，自动展开相应的子菜单

// 检查当前路径是否是财务相关路径，如果是则展开财务子菜单
const checkAndExpandSubmenus = () => {
  const path = route.path;
  if (path.startsWith("/finance")) {
    showFinanceSubmenu.value = true;
  }
  if (
    path === "/accounts" ||
    path === "/account/new" ||
    path === "/categories" ||
    path.startsWith("/account/edit")
  ) {
    showAccountsSubmenu.value = true;
  }
};

// 在组件挂载时检查
onMounted(() => {
  checkAndExpandSubmenus();
});

// 监听路由变化
watch(
  () => route.path,
  () => {
    checkAndExpandSubmenus();
  }
);

const currentRoute = computed(() => {
  const path = route.path;
  console.log("Current route path:", path); // 添加调试日志
  if (path === "/dashboard") return "dashboard";
  if (path === "/accounts") return "accounts";
  if (path === "/account/new") return "account-new";
  if (path.startsWith("/account/edit")) return "accounts";
  if (path === "/categories") return "categories";
  if (path === "/finance") return "finance";
  if (path === "/finance/dashboard") return "finance-dashboard";
  if (path === "/finance/list") return "finance-list";
  if (path === "/finance/settings") return "finance-settings";
  if (path === "/settings") return "settings";
  return "";
});

const breadcrumbs = computed(() => {
  const path = route.path;
  const crumbs = [{ name: "首页", path: "/dashboard" }];

  if (path === "/dashboard") return [{ name: "仪表板", path: "/dashboard" }];
  if (path === "/accounts")
    return [...crumbs, { name: "账号管理", path: "/accounts" }];
  if (path === "/account/new")
    return [
      ...crumbs,
      { name: "账号管理", path: "/accounts" },
      { name: "添加账号", path: "/account/new" },
    ];
  if (path.startsWith("/account/edit"))
    return [
      ...crumbs,
      { name: "账号管理", path: "/accounts" },
      { name: "编辑账号", path: path },
    ];
  if (path === "/categories")
    return [...crumbs, { name: "分类管理", path: "/categories" }];
  if (path === "/finance")
    return [...crumbs, { name: "财务", path: "/finance" }];
  if (path === "/finance/dashboard")
    return [
      ...crumbs,
      { name: "财务", path: "/finance" },
      { name: "仪表板", path: "/finance/dashboard" },
    ];
  if (path === "/finance/list")
    return [
      ...crumbs,
      { name: "财务", path: "/finance" },
      { name: "列表", path: "/finance/list" },
    ];
  if (path === "/finance/settings")
    return [
      ...crumbs,
      { name: "财务", path: "/finance" },
      { name: "设置", path: "/finance/settings" },
    ];
  if (path === "/settings")
    return [...crumbs, { name: "设置", path: "/settings" }];

  return crumbs;
});

const toggleCollapse = () => {
  collapsed.value = !collapsed.value;
};

const navigateTo = (path: string) => {
  try {
    if (route.path !== path) {
      router.push(path);

      // 如果是财务相关路由，确保子菜单保持展开
      if (path.startsWith("/finance")) {
        showFinanceSubmenu.value = true;
      }
      if (
        path === "/accounts" ||
        path === "/account/new" ||
        path === "/categories" ||
        path.startsWith("/account/edit")
      ) {
        showAccountsSubmenu.value = true;
      }
    }
  } catch (error) {
    console.error("Navigation error:", error);
  }
};

const toggleFinanceSubmenu = (event: MouseEvent) => {
  // 阻止事件冒泡，避免点击子菜单项时触发父菜单的点击事件
  event.stopPropagation();
  showFinanceSubmenu.value = !showFinanceSubmenu.value;
};

const toggleAccountsSubmenu = (event: MouseEvent) => {
  // 阻止事件冒泡，避免点击子菜单项时触发父菜单的点击事件
  event.stopPropagation();
  showAccountsSubmenu.value = !showAccountsSubmenu.value;
};

const lockApp = async () => {
  ElMessageBox.confirm(
    "确定要锁定应用吗？您需要重新输入主密码才能继续使用。",
    "确认锁定",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  ).then(async () => {
    await authStore.lockApp();
    router.push("/login");
  }).catch(() => {
    // 用户取消操作
  });
};

const onUserMenuSelect = (value: string) => {
  if (value === "lock") {
    lockApp();
  } else if (value === "logout") {
    ElMessageBox.confirm(
      "确定要退出登录吗？",
      "确认退出",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    ).then(() => {
      authStore.logout();
      router.push("/login");
    }).catch(() => {
      // 用户取消操作
    });
  }
};
</script>

<style lang="scss" scoped>
.app-layout {
  height: 100vh;
}

.app-sider {
  background: #001529;
  overflow: hidden !important;
  transition: width 0.3s ease;

  .logo {
    height: 64px;
    @include flex-center;
    background: rgba(255, 255, 255, 0.1);
    color: $text-color-white;
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    margin-bottom: $spacing-base;
    @include smooth-transition(all);
  }

  &.collapsed {
    .logo {
      font-size: $font-size-base;
    }

    .menu-item {
      justify-content: center;
      padding: $spacing-sm;
      margin: $spacing-xs $spacing-xs;

      .menu-icon {
        margin-right: 0 !important;
      }

      .menu-text {
        display: none;
      }
    }

    .collapsed-submenu-item {
      margin-top: 0;
      padding: $spacing-xs;

      &::before {
        left: 0;
      }
    }
  }
}

.custom-menu {
  padding: $spacing-base 0;
  height: calc(100vh - 80px);
  overflow: hidden;

  .menu-item {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    margin: 4px 8px;
    border-radius: 4px;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 40px;
    position: relative;

    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 3px;
      background: #1890ff;
      border-radius: 0 2px 2px 0;
      transform: scaleY(0);
      transition: transform 0.2s ease;
    }

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
      color: white;
      transform: translateX(2px);

      &::before {
        transform: scaleY(0.6);
      }
    }

    &.active {
      background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
      color: white;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);

      &::before {
        transform: scaleY(1);
      }
    }

    .menu-icon {
      font-size: 16px;
      margin-right: 8px;
      flex-shrink: 0;
    }

    .menu-text {
      font-size: 14px;
      white-space: nowrap;
      font-weight: 500;
      flex-grow: 1;
    }

    .submenu-arrow {
      font-size: 12px;
      transition: transform 0.3s ease;

      &.arrow-rotate {
        transform: rotate(180deg);
      }
    }

    &:hover .menu-icon {
      transform: scale(1.05);
    }
  }

  .submenu {
    margin-left: $spacing-lg;
    margin-right: $spacing-xs;

    .submenu-item {
      display: flex;
      align-items: center;
      padding: 6px 12px;
      margin: 2px 0;
      border-radius: 4px;
      color: rgba(255, 255, 255, 0.7);
      cursor: pointer;
      transition: all 0.2s ease;
      position: relative;

      &::before {
        content: "";
        position: absolute;
        left: -8px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #1890ff;
        border-radius: 0 2px 2px 0;
        transform: scaleY(0);
        transition: transform 0.2s ease;
      }

      &:hover {
        background-color: rgba(255, 255, 255, 0.08);
        color: white;
        transform: translateX(2px);

        &::before {
          transform: scaleY(0.6);
        }
      }

      &.active {
        background-color: rgba(24, 144, 255, 0.2);
        color: white;

        &::before {
          transform: scaleY(1);
        }
      }

      .submenu-icon {
        font-size: 14px;
        margin-right: 8px;
        flex-shrink: 0;
      }

      .submenu-text {
        font-size: 13px;
        white-space: nowrap;
        font-weight: 400;
      }
    }
  }
}

.app-header {
  background: $background-color-primary;
  padding: 0 $spacing-lg;
  @include flex-center-between;
  border-bottom: 1px solid $border-color-light;
  height: 64px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

  .header-left {
    @include flex-center;
    gap: $spacing-base;

    // 折叠按钮样式增强
    :deep(.el-button) {
      @include smooth-transition(all);
      border-radius: $border-radius-base;

      &:hover {
        background: $background-color-secondary;
        transform: scale(1.05);
      }
    }
  }

  .header-right {
    @include flex-center;

    .header-actions {
      @include flex-center;
      gap: $spacing-sm;

      .el-button {
        @include smooth-transition(all);
        font-weight: $font-weight-medium;

        &:hover {
          background: $background-color-secondary;
          transform: translateY(-1px);
        }
      }
    }
  }

  .breadcrumb {
    margin-left: $spacing-base;

    :deep(.el-breadcrumb__item) {
      color: $text-color-secondary;
      font-size: $font-size-sm;
      font-weight: $font-weight-medium;

      &:last-child {
        color: $text-color-primary;
      }
    }
  }
}

.app-content {
  background: $background-color-secondary;
  overflow: auto;
  @include custom-scrollbar;
  position: relative;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(
      90deg,
      transparent 0%,
      $border-color 50%,
      transparent 100%
    );
  }
}

// 下拉菜单样式增强
:deep(.el-dropdown-menu) {
  .el-dropdown-menu__item {
    @include smooth-transition(all);
    border-radius: $border-radius-xs;
    margin: $spacing-xs;

    &:hover {
      background: $primary-color;
      color: $text-color-white;
      transform: translateX(2px);
    }

    .el-icon {
      margin-right: $spacing-xs;
    }
  }
}

// 响应式设计
@include mobile {
  .app-header {
    padding: 0 $spacing-base;

    .header-left {
      gap: $spacing-sm;
    }

    .breadcrumb {
      display: none;
    }
  }

  .app-sider {
    .logo {
      font-size: $font-size-base;
    }

    .custom-menu {
      .menu-item {
        padding: $spacing-xs $spacing-sm;
        margin: $spacing-xs;

        .menu-text {
          font-size: $font-size-xs;
        }
      }
    }
  }
}

// 移除了从左侧滑入的动画效果

// 特殊状态样式
.app-sider.collapsed {
  .menu-item {
    .menu-text {
      opacity: 0;
      @include smooth-transition(opacity);
    }
  }
}
</style>
