<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-20 00:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-20 18:49:29
 * @FilePath     : /src/components/finance/ExpenseCard.vue
 * @Description  : 支出分类卡片组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-20 00:00:00
-->

<template>
  <div class="expense-card">
    <h3 class="card-title">
      <svg
        class="title-icon"
        viewBox="0 0 24 24"
        fill="none"
        stroke="#EF4444"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      >
        <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
      </svg>
      {{ title }}
    </h3>
    <el-row :gutter="24">
      <el-col v-for="field in fields" :key="field.name" :span="field.span || 8">
        <CurrencyInput
          :label="field.label"
          :icon="field.icon"
          :color="field.color"
          :step="field.step"
          :model-value="modelValue[field.name]"
          @update:model-value="updateField(field.name, $event)"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import CurrencyInput from "./CurrencyInput.vue";

interface Field {
  name: string;
  label: string;
  icon: string;
  color?: string;
  step?: number;
  span?: number;
}

interface Props {
  title: string;
  fields: Field[];
  modelValue: any;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  "update:modelValue": [value: any];
}>();

const updateField = (fieldName: string, value: number) => {
  // 创建新对象并更新指定字段
  emit("update:modelValue", {
    ...props.modelValue,
    [fieldName]: value,
  });
};
</script>

<style lang="scss" scoped>
.expense-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  .card-title {
    display: flex;
    align-items: center;
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;

    .title-icon {
      width: 1.25rem;
      height: 1.25rem;
      margin-right: 0.5rem;
    }
  }
}
</style>
