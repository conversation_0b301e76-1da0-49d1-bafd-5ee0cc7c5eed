# 自动暂存功能说明

## 🎯 功能目标

实现智能的自动暂存功能，防止用户因页面刷新、意外关闭等情况丢失已输入的数据。

## 🔄 工作原理

### 1. 自动暂存机制
- **触发时机**: 用户在任意输入框输入内容后
- **防抖延迟**: 1秒后自动保存，避免频繁写入
- **存储位置**: 浏览器 localStorage
- **适用场景**: 新增模式和编辑模式

### 2. 数据恢复优先级
```
页面加载 → 检查暂存数据 → 询问用户 → 选择数据源
                ↓
        有暂存数据 → 用户选择恢复 → 使用暂存数据
                ↓
        用户选择重新加载 → 使用API数据（编辑模式）
                ↓
        无暂存数据 → 直接使用API数据（编辑模式）
```

## 🔧 技术实现

### 1. 存储键名策略
```typescript
// 新增模式
const draftKey = 'finance_form_draft_add'

// 编辑模式
const draftKey = 'finance_form_draft_edit_${recordId}'
```

### 2. 自动暂存逻辑
```typescript
// 监听表单数据变化
watch(
  () => formData,
  () => {
    // 防抖保存，避免频繁写入
    if (saveTimer) {
      clearTimeout(saveTimer)
    }
    
    saveTimer = setTimeout(() => {
      autoSaveDraft()
    }, 1000) // 1秒后自动暂存
  },
  { deep: true }
)
```

### 3. 数据加载逻辑
```typescript
const loadRecordData = async () => {
  // 1. 首先检查暂存数据
  const draftData = getFromStorage(draftKey)
  
  if (draftData) {
    // 2. 询问用户是否恢复暂存
    const result = await ElMessageBox.confirm(
      `发现 ${formatTimestamp(timestamp)} 的暂存数据，是否恢复？`,
      '发现暂存数据'
    )
    
    if (result === 'confirm') {
      // 3. 恢复暂存数据
      Object.assign(formData, draftData)
    } else {
      // 4. 清除暂存，使用API数据
      removeFromStorage(draftKey)
      loadApiData()
    }
  } else {
    // 5. 无暂存数据，直接使用API数据
    loadApiData()
  }
}
```

## 📊 用户体验流程

### 场景1: 新增记录时页面刷新
```
用户输入数据 → 自动暂存 → 页面刷新 → 
检测到暂存 → 询问恢复 → 用户确认 → 数据恢复
```

### 场景2: 编辑记录时页面刷新
```
加载API数据 → 用户修改 → 自动暂存 → 页面刷新 → 
检测到暂存 → 询问恢复 → 用户确认 → 使用暂存数据
                    ↓
            用户拒绝 → 清除暂存 → 重新加载API数据
```

### 场景3: 正常提交
```
用户输入/修改 → 自动暂存 → 点击保存 → 
提交成功 → 清除暂存 → 跳转列表页
```

## 🎨 用户界面

### 暂存提示对话框
```vue
ElMessageBox.confirm(
  `发现 ${formatTimestamp(timestamp)} 的暂存数据，是否恢复？`,
  '发现暂存数据',
  {
    confirmButtonText: '恢复暂存',
    cancelButtonText: '重新加载',
    type: 'info'
  }
)
```

### 时间格式化
- **刚刚**: < 1分钟
- **X分钟前**: < 1小时
- **X小时前**: < 24小时
- **X天前**: < 7天
- **具体日期**: ≥ 7天

## 🔒 数据安全

### 1. 存储安全
- **本地存储**: 数据仅存储在用户本地浏览器
- **自动清理**: 提交成功后自动清除暂存
- **过期处理**: 可设置暂存数据过期时间

### 2. 数据完整性
- **深度监听**: 监听所有表单字段变化
- **防抖机制**: 避免频繁写入影响性能
- **错误处理**: 存储失败时不影响正常使用

## 📱 兼容性

### 浏览器支持
- ✅ **现代浏览器**: 完全支持 localStorage
- ✅ **移动浏览器**: 正常工作
- ⚠️ **隐私模式**: 可能受限，但不影响基本功能

### 存储限制
- **容量**: localStorage 通常 5-10MB
- **数据量**: 财务表单数据很小，无容量问题
- **清理**: 用户可手动清理浏览器数据

## ⚡ 性能优化

### 1. 防抖机制
```typescript
// 1秒防抖，避免频繁写入
setTimeout(() => {
  autoSaveDraft()
}, 1000)
```

### 2. 选择性存储
- **只存储表单数据**: 不存储计算属性
- **JSON序列化**: 高效的数据格式
- **增量更新**: 只在数据变化时写入

### 3. 内存管理
- **及时清理**: 提交后立即清除暂存
- **定时器管理**: 正确清理防抖定时器
- **事件监听**: 组件销毁时清理监听

## ✅ 功能验证

### 测试场景
1. **输入暂存**: 输入数据 → 等待1秒 → 检查localStorage
2. **页面刷新**: 刷新页面 → 检查是否提示恢复
3. **数据恢复**: 选择恢复 → 检查数据是否正确填充
4. **提交清理**: 提交表单 → 检查暂存是否清除
5. **编辑模式**: 编辑记录 → 修改数据 → 刷新 → 检查优先级

### 边界情况
- **网络断开**: 暂存功能正常工作
- **存储满了**: 优雅降级，不影响使用
- **数据损坏**: 自动忽略，使用API数据

## 🎯 用户价值

### 1. 数据安全
- **防止丢失**: 意外情况下保护用户数据
- **提高信心**: 用户敢于输入大量数据
- **减少重复**: 避免重复输入相同数据

### 2. 用户体验
- **无感知**: 自动工作，无需用户操作
- **智能提示**: 友好的恢复确认对话框
- **快速恢复**: 一键恢复所有数据

### 3. 工作效率
- **节省时间**: 避免重新输入数据
- **降低错误**: 减少因重复输入导致的错误
- **提升满意度**: 更好的产品体验

现在用户可以放心地输入大量财务数据，不用担心意外丢失！🎉
