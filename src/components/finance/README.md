# 财务组件封装说明

## 概述

原始的 `RecordForm.vue` 文件有 1285 行代码，包含大量重复的表单项和样式。通过组件化封装，现在已经重构为更简洁、可维护的结构。

## 封装的组件

### 1. CurrencyInput.vue
**功能**: 货币输入框组件
**Props**:
- `label`: 标签文本
- `modelValue`: 绑定值
- `icon`: SVG 图标路径
- `color`: 图标颜色
- `step`: 步长
- `min`: 最小值
- `placeholder`: 占位符

**特点**:
- 统一的货币符号显示
- 动态 SVG 图标支持
- 右对齐数字输入
- 响应式设计

### 2. ExpenseCard.vue
**功能**: 支出分类卡片组件
**Props**:
- `title`: 卡片标题
- `fields`: 字段配置数组
- `modelValue`: 表单数据对象

**特点**:
- 动态渲染表单项
- 支持不同的列宽配置
- 统一的卡片样式

### 3. SummaryCard.vue
**功能**: 财务总结卡片组件
**Props**:
- `type`: 卡片类型 ('income' | 'expense' | 'balance')
- `label`: 标签文本
- `value`: 数值

**特点**:
- 自动格式化货币显示
- 根据类型显示不同颜色和图标
- 悬停动画效果

## 逻辑封装

### useFinanceForm.ts
**功能**: 财务表单逻辑 composable
**提供**:
- 表单状态管理
- 计算属性（总收入、总支出、结余）
- 表单验证规则
- 提交和加载逻辑

### financeConfig.ts
**功能**: 配置文件
**包含**:
- SVG 图标路径定义
- 收入项目配置
- 支出分类配置

## 重构效果

### 代码行数对比
- **原始文件**: 1285 行
- **重构后主文件**: 508 行
- **减少**: 约 60%

### 组件结构
```
RecordForm.vue (508 行)
├── CurrencyInput.vue (120 行)
├── ExpenseCard.vue (77 行)
├── SummaryCard.vue (180 行)
├── useFinanceForm.ts (220 行)
└── financeConfig.ts (100 行)
```

### 优势
1. **可维护性**: 组件职责单一，易于修改
2. **可复用性**: 组件可在其他页面复用
3. **可测试性**: 每个组件可独立测试
4. **可读性**: 代码结构清晰，逻辑分离
5. **扩展性**: 新增字段只需修改配置文件

## 使用示例

```vue
<template>
  <!-- 收入项目 -->
  <CurrencyInput
    label="工资"
    icon="<path d='...' />"
    color="#4F46E5"
    v-model="salary"
  />

  <!-- 支出分类 -->
  <ExpenseCard
    title="日常开销"
    :fields="dailyExpenseFields"
    v-model="formData"
  />

  <!-- 财务总结 -->
  <SummaryCard
    type="income"
    label="总收入"
    :value="totalIncome"
  />
</template>
```

## 配置化添加新字段

要添加新的收入或支出项目，只需在 `financeConfig.ts` 中添加配置：

```typescript
// 添加新的收入项目
export const INCOME_FIELDS = [
  // ... 现有字段
  {
    name: 'bonus',
    label: '奖金',
    icon: ICONS.bonus,
    color: '#4F46E5',
    step: 100,
    span: 12
  }
]

// 添加新的支出分类
export const EXPENSE_CATEGORIES = [
  // ... 现有分类
  {
    title: '投资理财',
    fields: [
      {
        name: 'investment',
        label: '投资',
        icon: ICONS.investment,
        color: '#8B5CF6',
        step: 100
      }
    ]
  }
]
```
