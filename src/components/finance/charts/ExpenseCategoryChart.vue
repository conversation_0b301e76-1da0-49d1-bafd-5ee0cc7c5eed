<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-20 00:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-22 00:13:21
 * @FilePath     : /src/components/finance/charts/ExpenseCategoryChart.vue
 * @Description  : 支出分类饼图组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-20 00:00:00
-->

<template>
  <div class="expense-category-chart">
    <div class="chart-header">
      <h3 class="chart-title">
        <el-icon class="title-icon">
          <DataBoard />
        </el-icon>
        支出分类分析
      </h3>
      <div class="chart-controls">
        <div class="control-group">
          <!-- <span class="control-label">图表类型:</span> -->
          <el-radio-group v-model="chartType" size="small">
            <el-radio-button value="pie">饼图</el-radio-button>
            <el-radio-button value="line">折线图</el-radio-button>
            <el-radio-button value="bar">柱状图</el-radio-button>
          </el-radio-group>
        </div>

        <div v-if="showPeriodSelector" class="control-group">
          <span class="control-label">周期:</span>
          <el-select v-model="selectedPeriod" size="small" style="width: 120px">
            <el-option
              v-for="period in periods"
              :key="period"
              :label="period"
              :value="period"
            />
          </el-select>
        </div>
      </div>
    </div>

    <div class="chart-container">
      <v-chart
        :key="`${chartType}-${selectedPeriod}-${
          props.selectedPeriodProp || ''
        }`"
        class="chart"
        :option="chartOption"
        :loading="loading"
        autoresize
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watchEffect } from "vue";
import { use } from "echarts/core";
import { CanvasRenderer } from "echarts/renderers";
import { PieChart, LineChart, BarChart } from "echarts/charts";
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
} from "echarts/components";
import VChart from "vue-echarts";
import { DataBoard } from "@element-plus/icons-vue";
import type { FinanceRecord } from "../../../utils/mock/finance";

// 注册 ECharts 组件
use([
  CanvasRenderer,
  PieChart,
  LineChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
]);

interface Props {
  data: FinanceRecord[];
  loading?: boolean;
  showPeriodSelector?: boolean;
  selectedPeriodProp?: string; // 外部控制的选中周期
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  showPeriodSelector: true,
});

// 支出分类配置
const expenseCategories = {
  juan: { name: "娟", color: "#ff4d4f" },
  wechat: { name: "微信", color: "#52c41a" },
  huabei: { name: "花呗", color: "#1890ff" },
  spdbCreditCard: { name: "浦发信用卡", color: "#722ed1" },
  commCreditCard: { name: "交通信用卡", color: "#fa8c16" },
  cmCreditCard: { name: "招商信用卡", color: "#13c2c2" },
  jdBaitiao: { name: "京东白条", color: "#eb2f96" },
  jdJintiao: { name: "京东金条", color: "#f5222d" },
  rent: { name: "房租", color: "#faad14" },
  mortgage: { name: "房贷", color: "#a0d911" },
};

// 控制状态
const chartType = ref("pie");

// 周期列表
const periods = computed(() => props.data.map((item) => item.period));

// 选中的周期
const selectedPeriod = ref("");

// 监听数据变化，自动设置默认周期
watchEffect(() => {
  // 如果外部传入了选中周期，优先使用外部的
  if (
    props.selectedPeriodProp &&
    periods.value.includes(props.selectedPeriodProp)
  ) {
    selectedPeriod.value = props.selectedPeriodProp;
  } else if (periods.value.length > 0 && !selectedPeriod.value) {
    // 否则按时间排序，选择最新的周期
    const sortedPeriods = [...periods.value].sort((a, b) => b.localeCompare(a));
    selectedPeriod.value = sortedPeriods[0];
  }
});

// 当前周期数据
const currentPeriodData = computed(() => {
  return props.data.find((item) => item.period === selectedPeriod.value);
});

// 处理数据
const chartData = computed(() => {
  if (!currentPeriodData.value) return [];

  const data = Object.entries(expenseCategories)
    .map(([key, config]) => {
      const value = (currentPeriodData.value as any)[key] || 0;
      return {
        name: config.name,
        value,
        itemStyle: {
          color: config.color,
        },
      };
    })
    .filter((item) => item.value > 0);

  // 按金额排序
  return data.sort((a, b) => b.value - a.value);
});

// 总支出
const totalExpense = computed(() => {
  return chartData.value.reduce((sum, item) => sum + item.value, 0);
});

// 图表配置
const chartOption = computed(() => {
  if (chartData.value.length === 0) {
    return {
      title: {
        text: "暂无数据",
        left: "center",
        top: "center",
      },
    };
  }

  // 通用的 tooltip 格式化函数
  const formatTooltip = (params: any) => {
    const percentage = ((params.value / totalExpense.value) * 100).toFixed(1);
    return `
      <div style="font-weight: bold; margin-bottom: 8px;">${params.name}</div>
      <div style="display: flex; align-items: center; margin-bottom: 4px;">
        <span style="display: inline-block; width: 10px; height: 10px; background-color: ${
          params.color
        }; border-radius: 50%; margin-right: 8px;"></span>
        <span style="flex: 1;">金额:</span>
        <span style="font-weight: bold; color: #ff4d4f;">¥${params.value.toLocaleString()}</span>
      </div>
      <div style="display: flex; align-items: center;">
        <span style="flex: 1;">占比:</span>
        <span style="font-weight: bold;">${percentage}%</span>
      </div>
    `;
  };

  // 饼图配置
  if (chartType.value === "pie") {
    return {
      backgroundColor: "transparent",
      tooltip: {
        trigger: "item",
        formatter: formatTooltip,
      },
      legend: {
        type: "scroll",
        orient: "vertical",
        right: 10,
        top: 20,
        bottom: 20,
        textStyle: {
          fontSize: 12,
        },
        formatter: (name: string) => {
          const item = chartData.value.find((d) => d.name === name);
          if (item) {
            const percentage = (
              (item.value / totalExpense.value) *
              100
            ).toFixed(1);
            return `${name} ${percentage}%`;
          }
          return name;
        },
      },
      series: [
        {
          name: "支出分类",
          type: "pie",
          radius: ["40%", "70%"],
          center: ["40%", "50%"],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: "center",
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 16,
              fontWeight: "bold",
              formatter: (params: any) => {
                const percentage = (
                  (params.value / totalExpense.value) *
                  100
                ).toFixed(1);
                return `${
                  params.name
                }\n¥${params.value.toLocaleString()}\n${percentage}%`;
              },
            },
          },
          labelLine: {
            show: false,
          },
          data: chartData.value,
        },
      ],
    };
  }

  // 折线图配置
  if (chartType.value === "line") {
    return {
      backgroundColor: "transparent",
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
        },
        formatter: (params: any) => {
          if (params && params.length > 0) {
            return formatTooltip(params[0]);
          }
          return "";
        },
      },
      legend: {
        data: chartData.value.map((item) => item.name),
        top: 10,
        textStyle: {
          fontSize: 12,
        },
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "3%",
        top: "15%",
        containLabel: true,
      },
      xAxis: {
        type: "category",
        data: chartData.value.map((item) => item.name),
        axisLabel: {
          rotate: 45,
          fontSize: 11,
        },
      },
      yAxis: {
        type: "value",
        axisLabel: {
          formatter: (value: number) => {
            if (value >= 1000) {
              return `${(value / 1000).toFixed(1)}k`;
            }
            return value.toString();
          },
        },
      },
      series: [
        {
          name: "支出金额",
          type: "line",
          data: chartData.value.map((item) => item.value),
          smooth: true,
          symbol: "circle",
          symbolSize: 8,
          lineStyle: {
            width: 3,
            color: "#1890ff",
          },
          itemStyle: {
            color: "#1890ff",
          },
          areaStyle: {
            opacity: 0.3,
            color: "#1890ff",
          },
        },
      ],
    };
  }

  // 水平柱状图配置
  if (chartType.value === "bar") {
    return {
      backgroundColor: "transparent",
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "shadow",
        },
        formatter: (params: any) => {
          if (params && params.length > 0) {
            return formatTooltip(params[0]);
          }
          return "";
        },
      },
      grid: {
        left: "15%",
        right: "4%",
        bottom: "3%",
        top: "10%",
        containLabel: true,
      },
      xAxis: {
        type: "value",
        axisLabel: {
          formatter: (value: number) => {
            if (value >= 1000) {
              return `${(value / 1000).toFixed(1)}k`;
            }
            return value.toString();
          },
        },
      },
      yAxis: {
        type: "category",
        data: chartData.value.map((item) => item.name),
        axisLabel: {
          fontSize: 11,
        },
      },
      series: [
        {
          name: "支出金额",
          type: "bar",
          data: chartData.value.map((item) => ({
            value: item.value,
            itemStyle: {
              color: item.itemStyle?.color || "#1890ff",
            },
          })),
          barWidth: "60%",
          label: {
            show: true,
            position: "right",
            formatter: (params: any) => {
              return `¥${params.value.toLocaleString()}`;
            },
          },
        },
      ],
    };
  }

  return {};
});

// 监听数据变化，自动选择第一个周期
computed(() => {
  if (periods.value.length > 0 && !selectedPeriod.value) {
    selectedPeriod.value = periods.value[0];
  }
});
</script>

<style lang="scss" scoped>
.expense-category-chart {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;

    .chart-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
      color: #262626;
      margin: 0;

      .title-icon {
        color: #1890ff;
      }
    }

    .chart-controls {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      gap: 16px;

      .control-group {
        display: flex;
        align-items: center;
        gap: 8px;

        .control-label {
          font-size: 12px;
          color: #8c8c8c;
          white-space: nowrap;
        }
      }
    }
  }

  .chart-container {
    height: 400px;

    .chart {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
