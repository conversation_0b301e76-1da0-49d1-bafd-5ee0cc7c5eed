<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-21 00:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-21 01:00:00
 * @FilePath     : /src/components/finance/charts/MonthlyCashFlowChart.vue
 * @Description  : 月度现金流折线图组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-21 01:00:00
-->

<template>
  <div class="monthly-cash-flow-chart">
    <div class="chart-header">
      <h3 class="chart-title">
        <el-icon class="title-icon">
          <DataLine />
        </el-icon>
        月度现金流趋势
      </h3>
      
      <div class="chart-controls">
        <!-- 显示选项 -->
        <div class="control-group">
          <el-checkbox-group v-model="selectedMetrics" size="small">
            <el-checkbox value="income">收入</el-checkbox>
            <el-checkbox value="expense">支出</el-checkbox>
            <el-checkbox value="balance">结余</el-checkbox>
            <el-checkbox value="cumulative">累计结余</el-checkbox>
          </el-checkbox-group>
        </div>
        
        <!-- 时间范围 -->
        <div class="control-group">
          <span class="control-label">显示月数:</span>
          <el-select v-model="monthsToShow" size="small" style="width: 100px">
            <el-option label="3个月" :value="3" />
            <el-option label="6个月" :value="6" />
            <el-option label="12个月" :value="12" />
            <el-option label="全部" :value="0" />
          </el-select>
        </div>
      </div>
    </div>
    
    <div class="chart-container">
      <v-chart 
        class="chart" 
        :option="chartOption" 
        :loading="loading"
        autoresize
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent,
  MarkLineComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import { DataLine } from '@element-plus/icons-vue'
import type { FinanceRecord } from '../../../utils/mock/finance'

// 注册 ECharts 组件
use([
  CanvasRenderer,
  LineChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent,
  MarkLineComponent
])

interface Props {
  data: FinanceRecord[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// 控制状态
const selectedMetrics = ref(['income', 'expense', 'balance'])
const monthsToShow = ref(6)

// 处理数据
const chartData = computed(() => {
  let records = [...props.data].sort((a, b) => a.period.localeCompare(b.period))
  
  // 限制显示的月数
  if (monthsToShow.value > 0 && records.length > monthsToShow.value) {
    records = records.slice(-monthsToShow.value)
  }
  
  const periods = records.map(item => {
    // 提取月份信息，格式化为更简洁的显示
    const match = item.period.match(/(\d{4})-(\d{2})-\d{2}/)
    if (match) {
      return `${match[1]}-${match[2]}`
    }
    return item.period.substring(0, 7) // 取前7个字符作为备选
  })
  
  // 计算各项指标
  const incomeData = records.map(item => (item.salary || 0) + (item.otherIncome || 0))
  const expenseData = records.map(item => {
    return (item.juan || 0) + (item.wechat || 0) + (item.huabei || 0) + 
           (item.spdbCreditCard || 0) + (item.commCreditCard || 0) + 
           (item.cmCreditCard || 0) + (item.jdBaitiao || 0) + 
           (item.jdJintiao || 0) + (item.rent || 0) + (item.mortgage || 0)
  })
  const balanceData = incomeData.map((income, index) => income - expenseData[index])
  
  // 计算累计结余
  const cumulativeData: number[] = []
  let cumulative = 0
  balanceData.forEach(balance => {
    cumulative += balance
    cumulativeData.push(cumulative)
  })
  
  return {
    periods,
    incomeData,
    expenseData,
    balanceData,
    cumulativeData
  }
})

// 图表配置
const chartOption = computed(() => {
  const { periods, incomeData, expenseData, balanceData, cumulativeData } = chartData.value
  
  if (periods.length === 0) {
    return {
      title: {
        text: '暂无数据',
        left: 'center',
        top: 'center'
      }
    }
  }
  
  const series: any[] = []
  
  // 收入线
  if (selectedMetrics.value.includes('income')) {
    series.push({
      name: '收入',
      type: 'line',
      data: incomeData,
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        width: 3,
        color: '#52c41a'
      },
      itemStyle: {
        color: '#52c41a'
      },
      areaStyle: {
        opacity: 0.1,
        color: '#52c41a'
      }
    })
  }
  
  // 支出线
  if (selectedMetrics.value.includes('expense')) {
    series.push({
      name: '支出',
      type: 'line',
      data: expenseData,
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        width: 3,
        color: '#ff4d4f'
      },
      itemStyle: {
        color: '#ff4d4f'
      },
      areaStyle: {
        opacity: 0.1,
        color: '#ff4d4f'
      }
    })
  }
  
  // 结余线
  if (selectedMetrics.value.includes('balance')) {
    series.push({
      name: '结余',
      type: 'line',
      data: balanceData,
      smooth: true,
      symbol: 'circle',
      symbolSize: 8,
      lineStyle: {
        width: 4,
        color: '#1890ff'
      },
      itemStyle: {
        color: '#1890ff'
      },
      markLine: {
        data: [
          {
            type: 'average',
            name: '平均结余',
            lineStyle: {
              color: '#1890ff',
              type: 'dashed'
            }
          },
          {
            yAxis: 0,
            name: '收支平衡线',
            lineStyle: {
              color: '#8c8c8c',
              type: 'solid',
              width: 1
            }
          }
        ]
      }
    })
  }
  
  // 累计结余线
  if (selectedMetrics.value.includes('cumulative')) {
    series.push({
      name: '累计结余',
      type: 'line',
      data: cumulativeData,
      smooth: true,
      symbol: 'diamond',
      symbolSize: 6,
      lineStyle: {
        width: 3,
        color: '#722ed1',
        type: 'dashed'
      },
      itemStyle: {
        color: '#722ed1'
      }
    })
  }
  
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      },
      formatter: (params: any) => {
        let result = `<div style="font-weight: bold; margin-bottom: 8px;">${params[0].axisValue}</div>`
        
        params.forEach((param: any) => {
          const value = param.value
          const color = param.color
          const trend = getTrendIcon(param.seriesName, param.dataIndex)
          
          result += `
            <div style="display: flex; align-items: center; margin-bottom: 4px;">
              <span style="display: inline-block; width: 10px; height: 10px; background-color: ${color}; border-radius: 50%; margin-right: 8px;"></span>
              <span style="flex: 1;">${param.seriesName}:</span>
              <span style="font-weight: bold; color: ${value >= 0 ? '#52c41a' : '#ff4d4f'};">¥${Math.abs(value).toLocaleString()}</span>
              <span style="margin-left: 4px;">${trend}</span>
            </div>
          `
        })
        
        return result
      }
    },
    legend: {
      data: selectedMetrics.value.map(metric => {
        const names = {
          income: '收入',
          expense: '支出',
          balance: '结余',
          cumulative: '累计结余'
        }
        return names[metric as keyof typeof names]
      }),
      top: 10,
      textStyle: {
        fontSize: 12
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: periods,
      boundaryGap: false,
      axisLabel: {
        rotate: periods.length > 8 ? 45 : 0,
        fontSize: 11
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value: number) => {
          if (Math.abs(value) >= 10000) {
            return `${(value / 10000).toFixed(1)}万`
          }
          return value.toLocaleString()
        }
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          opacity: 0.5
        }
      }
    },
    dataZoom: periods.length > 12 ? [
      {
        type: 'slider',
        show: true,
        xAxisIndex: [0],
        start: 0,
        end: 80
      }
    ] : [],
    series
  }
})

// 获取趋势图标
const getTrendIcon = (seriesName: string, dataIndex: number): string => {
  const { incomeData, expenseData, balanceData, cumulativeData } = chartData.value
  
  if (dataIndex === 0) return ''
  
  let currentValue = 0
  let previousValue = 0
  
  switch (seriesName) {
    case '收入':
      currentValue = incomeData[dataIndex]
      previousValue = incomeData[dataIndex - 1]
      break
    case '支出':
      currentValue = expenseData[dataIndex]
      previousValue = expenseData[dataIndex - 1]
      break
    case '结余':
      currentValue = balanceData[dataIndex]
      previousValue = balanceData[dataIndex - 1]
      break
    case '累计结余':
      currentValue = cumulativeData[dataIndex]
      previousValue = cumulativeData[dataIndex - 1]
      break
  }
  
  if (currentValue > previousValue) return '↗'
  if (currentValue < previousValue) return '↘'
  return '→'
}
</script>

<style lang="scss" scoped>
.monthly-cash-flow-chart {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .chart-header {
    margin-bottom: 20px;

    .chart-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
      color: #262626;
      margin: 0 0 16px 0;

      .title-icon {
        color: #1890ff;
      }
    }

    .chart-controls {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      align-items: center;

      .control-group {
        display: flex;
        align-items: center;
        gap: 8px;

        .control-label {
          font-size: 12px;
          color: #8c8c8c;
          white-space: nowrap;
        }
      }
    }
  }

  .chart-container {
    height: 400px;

    .chart {
      width: 100%;
      height: 100%;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .monthly-cash-flow-chart {
    .chart-controls {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
    }
    
    .chart-container {
      height: 350px;
    }
  }
}
</style>
