<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-20 00:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-20 23:50:00
 * @FilePath     : /src/components/finance/charts/AssetTrendChart.vue
 * @Description  : 资产趋势图表组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-20 00:00:00
-->

<template>
  <div class="asset-trend-chart">
    <div class="chart-header">
      <h3 class="chart-title">
        <el-icon class="title-icon">
          <DataLine />
        </el-icon>
        资产趋势分析
      </h3>
      <div class="chart-controls">
        <el-checkbox-group v-model="selectedAssets" size="small">
          <el-checkbox value="alipay">支付宝</el-checkbox>
          <el-checkbox value="wechatPay">微信支付</el-checkbox>
          <el-checkbox value="actualBalance">实际结余</el-checkbox>
        </el-checkbox-group>
      </div>
    </div>

    <div class="chart-container">
      <v-chart
        class="chart"
        :option="chartOption"
        :loading="loading"
        autoresize
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { use } from "echarts/core";
import { CanvasRenderer } from "echarts/renderers";
import { LineChart } from "echarts/charts";
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent,
  MarkLineComponent,
} from "echarts/components";
import VChart from "vue-echarts";
import { DataLine } from "@element-plus/icons-vue";
import type { FinanceRecord } from "../../../utils/mock/finance";

// 注册 ECharts 组件
use([
  CanvasRenderer,
  LineChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent,
  MarkLineComponent,
]);

interface Props {
  data: FinanceRecord[];
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
});

// 选中的资产类型
const selectedAssets = ref(["alipay", "wechatPay", "actualBalance"]);

// 资产配置
const assetConfig = {
  alipay: {
    name: "支付宝",
    color: "#1677ff",
    key: "alipay",
  },
  wechatPay: {
    name: "微信支付",
    color: "#52c41a",
    key: "wechatPay",
  },
  actualBalance: {
    name: "实际结余",
    color: "#fa8c16",
    key: "actualBalance",
  },
};

// 处理数据
const chartData = computed(() => {
  const periods = props.data.map((item) => item.period);
  const assets: Record<string, number[]> = {};

  // 计算各项资产数据
  Object.keys(assetConfig).forEach((key) => {
    assets[key] = props.data.map(
      (item) => (item[key as keyof FinanceRecord] as number) || 0
    );
  });

  return {
    periods,
    assets,
  };
});

// 图表配置
const chartOption = computed(() => {
  const { periods, assets } = chartData.value;

  const series = selectedAssets.value.map((assetKey) => {
    const config = assetConfig[assetKey as keyof typeof assetConfig];
    return {
      name: config.name,
      type: "line",
      data: assets[assetKey],
      smooth: true,
      symbol: "circle",
      symbolSize: 6,
      lineStyle: {
        width: 3,
      },
      itemStyle: {
        color: config.color,
      },
      areaStyle: {
        opacity: 0.1,
        color: config.color,
      },
      markLine:
        assetKey === "actualBalance"
          ? {
              data: [
                {
                  type: "average",
                  name: "平均值",
                  lineStyle: {
                    color: config.color,
                    type: "dashed",
                  },
                },
              ],
            }
          : undefined,
    };
  });

  return {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
      },
      formatter: (params: any) => {
        let result = `<div style="font-weight: bold; margin-bottom: 8px;">${params[0].axisValue}</div>`;
        params.forEach((param: any) => {
          if (param.seriesType === "line") {
            result += `
              <div style="display: flex; align-items: center; margin-bottom: 4px;">
                <span style="display: inline-block; width: 10px; height: 10px; background-color: ${
                  param.color
                }; border-radius: 50%; margin-right: 8px;"></span>
                <span style="flex: 1;">${param.seriesName}:</span>
                <span style="font-weight: bold; color: ${
                  param.value >= 0 ? "#52c41a" : "#ff4d4f"
                };">¥${param.value.toLocaleString()}</span>
              </div>
            `;
          }
        });
        return result;
      },
    },
    legend: {
      data: selectedAssets.value.map(
        (key) => assetConfig[key as keyof typeof assetConfig].name
      ),
      top: 10,
      textStyle: {
        fontSize: 12,
      },
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "15%",
      top: "15%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: periods,
      boundaryGap: false,
      axisLabel: {
        rotate: periods.length > 6 ? 45 : 0,
        fontSize: 11,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        formatter: (value: number) => {
          if (Math.abs(value) >= 10000) {
            return `${(value / 10000).toFixed(1)}万`;
          }
          return value.toLocaleString();
        },
      },
      splitLine: {
        lineStyle: {
          type: "dashed",
          opacity: 0.5,
        },
      },
    },
    dataZoom:
      periods.length > 8
        ? [
            {
              type: "slider",
              show: true,
              xAxisIndex: [0],
              start: 0,
              end: 80,
            },
          ]
        : [],
    series,
  };
});
</script>

<style lang="scss" scoped>
.asset-trend-chart {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;

    .chart-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
      color: #262626;
      margin: 0;

      .title-icon {
        color: #1890ff;
      }
    }

    .chart-controls {
      :deep(.el-checkbox-group) {
        display: flex;
        gap: 12px;

        .el-checkbox {
          margin-right: 0;
        }
      }
    }
  }

  .chart-container {
    height: 400px;

    .chart {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
