<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-20 00:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-20 23:50:00
 * @FilePath     : /src/components/finance/charts/IncomeExpenseChart.vue
 * @Description  : 收支对比图表组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-20 00:00:00
-->

<template>
  <div class="income-expense-chart">
    <div class="chart-header">
      <h3 class="chart-title">
        <el-icon class="title-icon">
          <TrendCharts />
        </el-icon>
        收支对比分析
      </h3>
      <div class="chart-controls">
        <el-radio-group v-model="chartType" size="small">
          <el-radio-button value="bar">柱状图</el-radio-button>
          <el-radio-button value="line">折线图</el-radio-button>
        </el-radio-group>
      </div>
    </div>
    
    <div class="chart-container">
      <v-chart 
        class="chart" 
        :option="chartOption" 
        :loading="loading"
        autoresize
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { BarChart, LineChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import { TrendCharts } from '@element-plus/icons-vue'
import type { FinanceRecord } from '../../../utils/mock/finance'

// 注册 ECharts 组件
use([
  CanvasRenderer,
  BarChart,
  LineChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent
])

interface Props {
  data: FinanceRecord[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// 图表类型
const chartType = ref<'bar' | 'line'>('bar')

// 处理数据
const chartData = computed(() => {
  const periods = props.data.map(item => item.period)
  const incomeData = props.data.map(item => (item.salary || 0) + (item.otherIncome || 0))
  const expenseData = props.data.map(item => {
    return (item.juan || 0) + 
           (item.wechat || 0) + 
           (item.huabei || 0) + 
           (item.spdbCreditCard || 0) + 
           (item.commCreditCard || 0) + 
           (item.cmCreditCard || 0) + 
           (item.jdBaitiao || 0) + 
           (item.jdJintiao || 0) + 
           (item.rent || 0) + 
           (item.mortgage || 0)
  })
  const balanceData = incomeData.map((income, index) => income - expenseData[index])

  return {
    periods,
    incomeData,
    expenseData,
    balanceData
  }
})

// 图表配置
const chartOption = computed(() => {
  const { periods, incomeData, expenseData, balanceData } = chartData.value
  
  const baseOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      },
      formatter: (params: any) => {
        let result = `<div style="font-weight: bold; margin-bottom: 8px;">${params[0].axisValue}</div>`
        params.forEach((param: any) => {
          const value = param.value >= 0 ? `+${param.value.toLocaleString()}` : param.value.toLocaleString()
          result += `
            <div style="display: flex; align-items: center; margin-bottom: 4px;">
              <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 50%; margin-right: 8px;"></span>
              <span style="flex: 1;">${param.seriesName}:</span>
              <span style="font-weight: bold; color: ${param.value >= 0 ? '#52c41a' : '#ff4d4f'};">¥${value}</span>
            </div>
          `
        })
        return result
      }
    },
    legend: {
      data: ['收入', '支出', '结余'],
      top: 10,
      textStyle: {
        fontSize: 12
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: periods,
      axisPointer: {
        type: 'shadow'
      },
      axisLabel: {
        rotate: periods.length > 6 ? 45 : 0,
        fontSize: 11
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value: number) => {
          if (Math.abs(value) >= 10000) {
            return `${(value / 10000).toFixed(1)}万`
          }
          return value.toLocaleString()
        }
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          opacity: 0.5
        }
      }
    },
    dataZoom: periods.length > 8 ? [
      {
        type: 'slider',
        show: true,
        xAxisIndex: [0],
        start: 0,
        end: 80
      }
    ] : [],
    series: [
      {
        name: '收入',
        type: chartType.value,
        data: incomeData,
        itemStyle: {
          color: '#52c41a'
        },
        smooth: chartType.value === 'line'
      },
      {
        name: '支出',
        type: chartType.value,
        data: expenseData,
        itemStyle: {
          color: '#ff4d4f'
        },
        smooth: chartType.value === 'line'
      },
      {
        name: '结余',
        type: chartType.value,
        data: balanceData,
        itemStyle: {
          color: '#1890ff'
        },
        smooth: chartType.value === 'line'
      }
    ]
  }

  return baseOption
})
</script>

<style lang="scss" scoped>
.income-expense-chart {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;

    .chart-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
      color: #262626;
      margin: 0;

      .title-icon {
        color: #1890ff;
      }
    }

    .chart-controls {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }

  .chart-container {
    height: 400px;

    .chart {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
