<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-20 00:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-20 23:50:00
 * @FilePath     : /src/components/finance/charts/ComprehensiveRadar.vue
 * @Description  : 综合雷达图组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-20 00:00:00
-->

<template>
  <div class="comprehensive-radar">
    <div class="chart-header">
      <h3 class="chart-title">
        <el-icon class="title-icon">
          <DataAnalysis />
        </el-icon>
        综合财务雷达图
      </h3>
      <div class="chart-controls">
        <el-tooltip content="显示各周期在收入、支出、储蓄等维度的综合表现">
          <el-icon class="help-icon">
            <QuestionFilled />
          </el-icon>
        </el-tooltip>
      </div>
    </div>

    <div class="chart-container">
      <v-chart
        class="chart"
        :option="chartOption"
        :loading="loading"
        autoresize
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { use } from "echarts/core";
import { CanvasRenderer } from "echarts/renderers";
import { RadarChart } from "echarts/charts";
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  RadarComponent,
} from "echarts/components";
import VChart from "vue-echarts";
import { DataAnalysis, QuestionFilled } from "@element-plus/icons-vue";
import type { FinanceRecord } from "../../../utils/mock/finance";

// 注册 ECharts 组件
use([
  CanvasRenderer,
  RadarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  RadarComponent,
]);

interface Props {
  data: FinanceRecord[];
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
});

// 雷达图指标配置
const indicators = [
  { name: "收入水平", max: 100 },
  { name: "支出控制", max: 100 },
  { name: "储蓄能力", max: 100 },
  { name: "资产增长", max: 100 },
  { name: "财务稳定", max: 100 },
];

// 处理数据
const chartData = computed(() => {
  if (props.data.length === 0) return [];

  // 计算各项指标的最大值用于标准化
  const incomes = props.data.map(
    (item) => (item.salary || 0) + (item.otherIncome || 0)
  );
  const expenses = props.data.map((item) => {
    return (
      (item.juan || 0) +
      (item.wechat || 0) +
      (item.huabei || 0) +
      (item.spdbCreditCard || 0) +
      (item.commCreditCard || 0) +
      (item.cmCreditCard || 0) +
      (item.jdBaitiao || 0) +
      (item.jdJintiao || 0) +
      (item.rent || 0) +
      (item.mortgage || 0)
    );
  });
  const balances = incomes.map((income, index) => income - expenses[index]);
  const assets = props.data.map(
    (item) => (item.alipay || 0) + (item.wechatPay || 0)
  );

  const maxIncome = Math.max(...incomes);
  const maxExpense = Math.max(...expenses);
  const maxBalance = Math.max(...balances.map(Math.abs));
  const maxAsset = Math.max(...assets);

  // 为每个周期计算雷达图数据
  return props.data.map((item, index) => {
    const income = incomes[index];
    const expense = expenses[index];
    const balance = balances[index];
    const asset = assets[index];

    // 计算各维度得分（0-100）
    const incomeScore = maxIncome > 0 ? (income / maxIncome) * 100 : 0;
    const expenseScore =
      maxExpense > 0 ? Math.max(0, 100 - (expense / maxExpense) * 100) : 100; // 支出越低得分越高
    const savingScore =
      maxBalance > 0 ? Math.max(0, (balance / maxBalance) * 100) : 0;
    const assetScore = maxAsset > 0 ? (asset / maxAsset) * 100 : 0;

    // 财务稳定性：基于收支比和资产变化
    const incomeExpenseRatio =
      income > 0 ? Math.min(100, (balance / income) * 100) : 0;
    const stabilityScore = Math.max(0, (incomeExpenseRatio + assetScore) / 2);

    return {
      name: item.period,
      value: [
        Math.round(incomeScore),
        Math.round(expenseScore),
        Math.round(savingScore),
        Math.round(assetScore),
        Math.round(stabilityScore),
      ],
      itemStyle: {
        color: getColorByIndex(index),
      },
      areaStyle: {
        opacity: 0.1,
      },
    };
  });
});

// 根据索引获取颜色
const getColorByIndex = (index: number) => {
  const colors = [
    "#1890ff",
    "#52c41a",
    "#fa8c16",
    "#eb2f96",
    "#722ed1",
    "#13c2c2",
    "#f5222d",
    "#faad14",
  ];
  return colors[index % colors.length];
};

// 图表配置
const chartOption = computed(() => {
  return {
    tooltip: {
      trigger: "item",
      formatter: (params: any) => {
        const data = params.data;
        let result = `<div style="font-weight: bold; margin-bottom: 8px;">${data.name}</div>`;

        indicators.forEach((indicator, index) => {
          result += `
            <div style="display: flex; align-items: center; margin-bottom: 4px;">
              <span style="flex: 1;">${indicator.name}:</span>
              <span style="font-weight: bold;">${data.value[index]}分</span>
            </div>
          `;
        });

        return result;
      },
    },
    legend: {
      data: chartData.value.map((item) => item.name),
      top: 10,
      textStyle: {
        fontSize: 12,
      },
    },
    radar: {
      indicator: indicators,
      center: ["50%", "55%"],
      radius: "70%",
      axisName: {
        fontSize: 12,
        color: "#666",
      },
      splitLine: {
        lineStyle: {
          color: "#e8e8e8",
        },
      },
      splitArea: {
        show: true,
        areaStyle: {
          color: ["rgba(250, 250, 250, 0.3)", "rgba(200, 200, 200, 0.1)"],
        },
      },
    },
    series: [
      {
        name: "财务指标",
        type: "radar",
        data: chartData.value,
        symbol: "circle",
        symbolSize: 6,
        lineStyle: {
          width: 2,
        },
      },
    ],
  };
});
</script>

<style lang="scss" scoped>
.comprehensive-radar {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;

    .chart-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
      color: #262626;
      margin: 0;

      .title-icon {
        color: #1890ff;
      }
    }

    .chart-controls {
      .help-icon {
        color: #8c8c8c;
        cursor: help;

        &:hover {
          color: #1890ff;
        }
      }
    }
  }

  .chart-container {
    height: 400px;

    .chart {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
