<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-20 00:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-21 00:53:45
 * @FilePath     : /src/components/finance/charts/ItemComparisonChart.vue
 * @Description  : 支出项目逐项对比图表组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-21 00:00:00
-->

<template>
  <div class="item-comparison-chart">
    <div class="chart-header">
      <h3 class="chart-title">
        <el-icon class="title-icon">
          <DataAnalysis />
        </el-icon>
        支出项目对比分析
      </h3>

      <div class="chart-controls">
        <!-- 分组筛选 -->
        <div class="control-group">
          <span class="control-label">分组:</span>
          <el-radio-group v-model="selectedGroup" size="small">
            <el-radio-button value="all">全部</el-radio-button>
            <el-radio-button value="income">收入</el-radio-button>
            <el-radio-button value="expense">支出</el-radio-button>
            <el-radio-button value="credit">信用卡</el-radio-button>
            <el-radio-button value="consumer">消费信贷</el-radio-button>
            <el-radio-button value="cash">现金支出</el-radio-button>
            <el-radio-button value="fixed">固定支出</el-radio-button>
          </el-radio-group>
        </div>

        <!-- 排序选项 -->
        <div class="control-group">
          <span class="control-label">排序:</span>
          <el-select v-model="sortBy" size="small" style="width: 120px">
            <el-option label="按名称" value="name" />
            <el-option label="按金额" value="amount" />
            <el-option label="按变化率" value="change" />
          </el-select>
        </div>

        <!-- 显示选项 -->
        <div class="control-group">
          <el-checkbox v-model="showValues" size="small">显示数值</el-checkbox>
          <el-checkbox v-model="showChange" size="small">显示变化</el-checkbox>
        </div>
      </div>
    </div>

    <div class="chart-container">
      <v-chart
        class="chart"
        :option="chartOption"
        :loading="loading"
        autoresize
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { use } from "echarts/core";
import { CanvasRenderer } from "echarts/renderers";
import { BarChart } from "echarts/charts";
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent,
} from "echarts/components";
import VChart from "vue-echarts";
import { DataAnalysis } from "@element-plus/icons-vue";
import type { FinanceRecord } from "../../../utils/mock/finance";

// 注册 ECharts 组件
use([
  CanvasRenderer,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent,
]);

interface Props {
  data: FinanceRecord[];
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
});

// 控制状态
const selectedGroup = ref("all");
const sortBy = ref("name");
const showValues = ref(true);
const showChange = ref(true);

// 项目分组配置
const itemGroups = {
  income: {
    name: "收入",
    items: ["salary", "otherIncome"],
    labels: { salary: "工资", otherIncome: "其他收入" },
  },
  expense: {
    name: "支出",
    items: [
      "juan",
      "wechat",
      "huabei",
      "spdbCreditCard",
      "commCreditCard",
      "cmCreditCard",
      "jdBaitiao",
      "jdJintiao",
      "rent",
      "mortgage",
    ],
    labels: {
      juan: "娟",
      wechat: "微信",
      huabei: "花呗",
      spdbCreditCard: "浦发信用卡",
      commCreditCard: "交通信用卡",
      cmCreditCard: "招商信用卡",
      jdBaitiao: "京东白条",
      jdJintiao: "京东金条",
      rent: "房租",
      mortgage: "房贷",
    },
  },
  credit: {
    name: "信用卡",
    items: ["spdbCreditCard", "commCreditCard", "cmCreditCard"],
    labels: {
      spdbCreditCard: "浦发信用卡",
      commCreditCard: "交通信用卡",
      cmCreditCard: "招商信用卡",
    },
  },
  consumer: {
    name: "消费信贷",
    items: ["huabei", "jdBaitiao", "jdJintiao"],
    labels: {
      huabei: "花呗",
      jdBaitiao: "京东白条",
      jdJintiao: "京东金条",
    },
  },
  cash: {
    name: "现金支出",
    items: ["juan", "wechat"],
    labels: { juan: "娟", wechat: "微信" },
  },
  fixed: {
    name: "固定支出",
    items: ["rent", "mortgage"],
    labels: { rent: "房租", mortgage: "房贷" },
  },
};

// 获取当前显示的项目
const currentItems = computed(() => {
  if (selectedGroup.value === "all") {
    // 返回所有项目
    const allItems: Array<{ key: string; label: string; group: string }> = [];
    Object.entries(itemGroups).forEach(([groupKey, group]) => {
      group.items.forEach((item) => {
        allItems.push({
          key: item,
          label: group.labels[item as keyof typeof group.labels],
          group: groupKey,
        });
      });
    });
    return allItems;
  } else {
    const group = itemGroups[selectedGroup.value as keyof typeof itemGroups];
    return group.items.map((item) => ({
      key: item,
      label: group.labels[item as keyof typeof group.labels],
      group: selectedGroup.value,
    }));
  }
});

// 处理数据
const chartData = computed(() => {
  const periods = props.data.map((item) => item.period);
  const items = currentItems.value;

  // 计算每个项目的数据和变化率
  const itemsData = items.map((item) => {
    const values = props.data.map((record) => (record as any)[item.key] || 0);

    // 计算变化率（相对于第一个周期）
    const changeRates = values.map((value, index) => {
      if (index === 0 || values[0] === 0) return 0;
      return ((value - values[0]) / values[0]) * 100;
    });

    return {
      ...item,
      values,
      changeRates,
      avgValue: values.reduce((sum, val) => sum + val, 0) / values.length,
      maxChange: Math.max(...changeRates.map(Math.abs)),
    };
  });

  // 排序
  let sortedItems = [...itemsData];
  if (sortBy.value === "amount") {
    sortedItems.sort((a, b) => b.avgValue - a.avgValue);
  } else if (sortBy.value === "change") {
    sortedItems.sort((a, b) => b.maxChange - a.maxChange);
  } else {
    sortedItems.sort((a, b) => a.label.localeCompare(b.label));
  }

  return {
    periods,
    items: sortedItems,
  };
});

// 图表配置
const chartOption = computed(() => {
  const { periods, items } = chartData.value;

  if (items.length === 0) {
    return {
      title: {
        text: "暂无数据",
        left: "center",
        top: "center",
      },
    };
  }

  // 构建系列数据
  const series = periods.map((period, periodIndex) => ({
    name: period,
    type: "bar",
    data: items.map((item) => item.values[periodIndex]),
    itemStyle: {
      color: getColorByIndex(periodIndex),
    },
    label: showValues.value
      ? {
          show: true,
          position: "top",
          formatter: (params: any) => {
            const value = params.value;
            if (value === 0) return "";
            return value >= 1000
              ? `${(value / 1000).toFixed(1)}k`
              : value.toString();
          },
        }
      : { show: false },
  }));

  return {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
      formatter: (params: any) => {
        const itemIndex = params[0].dataIndex;
        const item = items[itemIndex];

        let result = `<div style="font-weight: bold; margin-bottom: 8px;">${item.label}</div>`;

        params.forEach((param: any, index: number) => {
          const value = param.value;
          const changeRate = item.changeRates[index];
          const changeText =
            showChange.value && index > 0
              ? ` (${changeRate > 0 ? "+" : ""}${changeRate.toFixed(1)}%)`
              : "";

          result += `
            <div style="display: flex; align-items: center; margin-bottom: 4px;">
              <span style="display: inline-block; width: 10px; height: 10px; background-color: ${
                param.color
              }; border-radius: 50%; margin-right: 8px;"></span>
              <span style="flex: 1;">${param.seriesName}:</span>
              <span style="font-weight: bold;">¥${value.toLocaleString()}${changeText}</span>
            </div>
          `;
        });

        return result;
      },
    },
    legend: {
      data: periods,
      top: 10,
      textStyle: {
        fontSize: 12,
      },
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "15%",
      top: "15%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: items.map((item) => item.label),
      axisLabel: {
        rotate: items.length > 8 ? 45 : 0,
        fontSize: 11,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        formatter: (value: number) => {
          if (Math.abs(value) >= 10000) {
            return `${(value / 10000).toFixed(1)}万`;
          }
          return value.toLocaleString();
        },
      },
      splitLine: {
        lineStyle: {
          type: "dashed",
          opacity: 0.5,
        },
      },
    },
    dataZoom:
      items.length > 10
        ? [
            {
              type: "slider",
              show: true,
              xAxisIndex: [0],
              start: 0,
              end: 80,
            },
          ]
        : [],
    series,
  };
});

// 根据索引获取颜色
const getColorByIndex = (index: number) => {
  const colors = [
    "#1890ff",
    "#52c41a",
    "#fa8c16",
    "#eb2f96",
    "#722ed1",
    "#13c2c2",
    "#f5222d",
    "#faad14",
  ];
  return colors[index % colors.length];
};
</script>

<style lang="scss" scoped>
.item-comparison-chart {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .chart-header {
    margin-bottom: 20px;

    .chart-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
      color: #262626;
      margin: 0 0 16px 0;

      .title-icon {
        color: #1890ff;
      }
    }

    .chart-controls {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      align-items: center;

      .control-group {
        display: flex;
        align-items: center;
        gap: 8px;

        .control-label {
          font-size: 12px;
          color: #8c8c8c;
          white-space: nowrap;
        }
      }
    }
  }

  .chart-container {
    height: 500px;

    .chart {
      width: 100%;
      height: 100%;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .item-comparison-chart {
    .chart-controls {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
    }

    .chart-container {
      height: 400px;
    }
  }
}
</style>
