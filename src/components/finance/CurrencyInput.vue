<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-20 00:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-20 19:17:26
 * @FilePath     : /src/components/finance/CurrencyInput.vue
 * @Description  : 货币输入框组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-20 00:00:00
-->

<template>
  <div class="form-item">
    <label class="form-label">
      <svg
        v-if="icon"
        class="label-icon"
        viewBox="0 0 24 24"
        fill="none"
        :stroke="color"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
        v-html="icon"
      />
      {{ label }}
    </label>
    <div class="input-group">
      <span class="currency-symbol">¥</span>
      <el-input-number
        :model-value="modelValue"
        @update:model-value="$emit('update:modelValue', $event)"
        :min="min"
        :step="step"
        :placeholder="placeholder"
        :controls="false"
        class="currency-input"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  label: string;
  modelValue?: number;
  icon?: string;
  min?: number;
  step?: number;
  placeholder?: string;
  color?: string;
}

withDefaults(defineProps<Props>(), {
  modelValue: 0,
  min: 0,
  step: 10,
  placeholder: "0",
  color: "#4F46E5",
});

defineEmits<{
  "update:modelValue": [value: number];
}>();
</script>

<style lang="scss" scoped>
.form-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  .form-label {
    display: flex;
    align-items: center;
    font-weight: 500;
    color: #374151;

    .label-icon {
      margin-right: 0.5rem;
      width: 1.25rem;
      height: 1.25rem;
      flex-shrink: 0;
      stroke-linecap: round;
      stroke-linejoin: round;
    }
  }

  .input-group {
    display: flex;
    align-items: center;
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    transition: all 0.2s ease;

    &:focus-within {
      border-color: #4f46e5;
      box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
    }

    .currency-symbol {
      padding: 0.5rem 0 0.5rem 0.75rem;
      color: #6b7280;
      font-weight: 500;
      display: flex;
      align-items: center;
      height: 40px;
    }

    :deep(.currency-input) {
      .el-input__wrapper {
        border: none;
        background: transparent;
        box-shadow: none;
        padding: 0.5rem 0.75rem 0.5rem 0;
        min-height: 40px;
        height: 40px;

        .el-input__inner {
          text-align: right;
          font-size: 1rem;
          background: transparent;
          border: none;
          box-shadow: none;
          height: auto;
          line-height: 1.5;
        }
      }
    }
  }
}
</style>
