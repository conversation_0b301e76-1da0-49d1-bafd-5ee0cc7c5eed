<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-20 00:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-20 20:35:43
 * @FilePath     : /src/components/finance/SummaryCard.vue
 * @Description  : 财务总结卡片组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-20 00:00:00
-->

<template>
  <el-card
    :class="[
      'summary-card',
      `${type}-card`,
      type === 'balance' &&
        (value >= 0 ? 'positive-balance' : 'negative-balance'),
    ]"
    shadow="hover"
  >
    <div class="summary-content">
      <div class="summary-icon">
        <el-icon :size="18" :color="iconColor">
          <component :is="iconComponent" />
        </el-icon>
      </div>
      <div class="summary-info">
        <div class="summary-label">{{ label }}</div>
        <div class="summary-main-row">
          <div :class="['summary-value', `${type}-value`]">
            {{ formattedValue }}
          </div>
          <!-- 总支出详细信息 -->
          <div
            v-if="
              type === 'expense' &&
              (creditExpense !== undefined || cashExpense !== undefined)
            "
            class="expense-details"
          >
            <div v-if="creditExpense !== undefined" class="expense-item credit">
              <span class="expense-label">信用:</span>
              <span class="expense-value"
                >¥{{ formatCurrency(creditExpense) }}</span
              >
            </div>
            <div v-if="cashExpense !== undefined" class="expense-item cash">
              <span class="expense-label">现金:</span>
              <span class="expense-value"
                >¥{{ formatCurrency(cashExpense) }}</span
              >
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { computed } from "vue";
import {
  TrendCharts,
  Minus,
  Plus,
  Wallet,
  CreditCard,
  Money,
} from "@element-plus/icons-vue";

interface Props {
  type:
    | "income"
    | "expense"
    | "credit-expense"
    | "cash-expense"
    | "assets"
    | "after-salary"
    | "after-payment"
    | "balance";
  label: string;
  value: number;
  creditExpense?: number;
  cashExpense?: number;
}

const props = defineProps<Props>();

// 格式化货币
const formatCurrency = (value: number): string => {
  if (value % 1 === 0) {
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  }
  return value.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
};

// 格式化显示值
const formattedValue = computed(() => {
  if (props.type === "balance") {
    const prefix = props.value >= 0 ? "+" : "-";
    return `${prefix}¥${formatCurrency(Math.abs(props.value))}`;
  }
  return `¥${formatCurrency(props.value)}`;
});

// 图标组件
const iconComponent = computed(() => {
  switch (props.type) {
    case "income":
      return TrendCharts;
    case "expense":
      return Minus;
    case "assets":
      return Wallet;
    case "after-salary":
      return Money;
    case "after-payment":
      return CreditCard;
    case "balance":
      return props.value >= 0 ? Plus : Minus;
    default:
      return TrendCharts;
  }
});

// 图标颜色
const iconColor = computed(() => {
  switch (props.type) {
    case "income":
      return "#67C23A";
    case "expense":
      return "#F56C6C";
    case "assets":
      return "#1890FF";
    case "after-salary":
      return "#52C41A";
    case "after-payment":
      return "#FA8C16";
    case "balance":
      return props.value >= 0 ? "#67C23A" : "#F56C6C";
    default:
      return "#67C23A";
  }
});
</script>

<style lang="scss" scoped>
@import "@/styles/variables.scss";

.summary-card {
  border-radius: $border-radius-lg;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }

  .summary-content {
    display: flex;
    align-items: center;

    .summary-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      margin-right: 0.5rem;
      background: rgba(255, 255, 255, 0.8);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .summary-info {
      flex: 1;

      .summary-label {
        font-size: 0.875rem;
        color: #6b7280;
        margin-bottom: 2px;
        font-weight: 500;
      }

      .summary-value {
        font-size: 1rem;
        font-weight: 600;
        line-height: 1.2;

        &.income-value {
          color: #67c23a;
        }

        &.expense-value {
          color: #f56c6c;
        }

        &.assets-value {
          color: #1890ff;
        }

        &.after-salary-value {
          color: #52c41a;
        }

        &.after-payment-value {
          color: #fa8c16;
        }

        &.balance-value {
          color: inherit;
        }
      }

      .summary-main-row {
        display: flex;
        align-items: baseline;
        gap: 1rem;
        flex-wrap: wrap;
      }

      .expense-details {
        display: flex;
        gap: 0.75rem;
        margin-left: auto;

        .expense-item {
          display: flex;
          align-items: center;
          font-size: 0.7rem;
          gap: 0.25rem;

          .expense-label {
            color: #8c8c8c;
            font-weight: 500;
            white-space: nowrap;
          }

          .expense-value {
            font-weight: 600;
            white-space: nowrap;
          }

          &.credit .expense-value {
            color: #ff4d4f;
          }

          &.cash .expense-value {
            color: #8c8c8c;
          }
        }
      }
    }
  }

  // 收入卡片样式
  &.income-card {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border: 1px solid #bae6fd;

    .summary-icon {
      background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
      color: white;
    }
  }

  // 支出卡片样式
  &.expense-card {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
    border: 1px solid #fecaca;

    .summary-icon {
      background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%);
      color: white;
    }
  }

  // 信用支出卡片样式
  &.credit-expense-card {
    background: linear-gradient(135deg, #fff1f0 0%, #ffccc7 100%);
    border: 1px solid #ffa39e;

    .summary-icon {
      background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
      color: white;
    }
  }

  // 现金支出卡片样式
  &.cash-expense-card {
    background: linear-gradient(135deg, #f6f6f6 0%, #e8e8e8 100%);
    border: 1px solid #d9d9d9;

    .summary-icon {
      background: linear-gradient(135deg, #8c8c8c 0%, #a6a6a6 100%);
      color: white;
    }
  }

  // 现金资产卡片样式
  &.assets-card {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f7fa 100%);
    border: 1px solid #81d4fa;

    .summary-icon {
      background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
      color: white;
    }
  }

  // 发薪后卡片样式
  &.after-salary-card {
    background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
    border: 1px solid #b7eb8f;

    .summary-icon {
      background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
      color: white;
    }
  }

  // 还款后卡片样式
  &.after-payment-card {
    background: linear-gradient(135deg, #fff7e6 0%, #ffe7ba 100%);
    border: 1px solid #ffd591;

    .summary-icon {
      background: linear-gradient(135deg, #fa8c16 0%, #ffa940 100%);
      color: white;
    }
  }

  // 结余卡片样式
  &.positive-balance {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
    border: 1px solid #bbf7d0;

    .summary-icon {
      background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
      color: white;
    }
  }

  &.negative-balance {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
    border: 1px solid #fecaca;

    .summary-icon {
      background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%);
      color: white;
    }
  }
}
</style>
