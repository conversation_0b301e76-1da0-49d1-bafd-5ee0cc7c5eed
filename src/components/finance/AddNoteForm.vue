<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-20 00:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-20 23:35:00
 * @FilePath     : /src/components/finance/AddNoteForm.vue
 * @Description  : 添加备注表单组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-20 00:00:00
-->

<template>
  <div class="add-note-form">
    <div v-if="!isExpanded" class="add-note-trigger" @click="expand">
      <el-icon class="add-icon">
        <Plus />
      </el-icon>
      <span class="add-text">添加新备注</span>
    </div>

    <div v-else class="add-note-content">
      <!-- 标签选择 -->
      <div class="tag-selection">
        <div class="tag-section">
          <span class="section-label">优先级:</span>
          <div class="tag-options">
            <NoteTag
              v-for="tag in priorityTags"
              :key="tag.id"
              :tag="tag"
              size="small"
              clickable
              :class="{ selected: selectedTags.includes(tag.id) }"
              @click="toggleTag(tag.id)"
            />
          </div>
        </div>

        <div class="tag-section">
          <span class="section-label">分类:</span>
          <div class="tag-options">
            <NoteTag
              v-for="tag in categoryTags"
              :key="tag.id"
              :tag="tag"
              size="small"
              clickable
              :class="{ selected: selectedTags.includes(tag.id) }"
              @click="toggleTag(tag.id)"
            />
          </div>
        </div>

        <!-- 自定义标签输入 -->
        <div class="custom-tag-input">
          <el-input
            v-model="customTagName"
            placeholder="自定义标签"
            size="small"
            @keydown.enter="addCustomTag"
          >
            <template #append>
              <el-button @click="addCustomTag" :disabled="!customTagName.trim()">
                添加
              </el-button>
            </template>
          </el-input>
        </div>
      </div>

      <!-- 内容输入 -->
      <div class="content-input">
        <el-input
          v-model="content"
          type="textarea"
          :rows="3"
          placeholder="请输入备注内容..."
          @keydown.ctrl.enter="handleSave"
        />
      </div>

      <!-- 操作按钮 -->
      <div class="form-actions">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          @click="handleSave"
          :disabled="!content.trim()"
        >
          保存
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import NoteTag from './NoteTag.vue'
import type { NoteItem, TagType } from '../../types/note'
import { PRESET_TAGS } from '../../types/note'

interface Emits {
  (e: 'add', note: Omit<NoteItem, 'id' | 'createdAt' | 'updatedAt'>): void
}

const emit = defineEmits<Emits>()

// 状态
const isExpanded = ref(false)
const selectedTags = ref<string[]>([])
const content = ref('')
const customTagName = ref('')
const customTags = ref<TagType[]>([])

// 计算属性
const priorityTags = computed(() => 
  PRESET_TAGS.filter(tag => tag.type === 'priority')
)

const categoryTags = computed(() => 
  [...PRESET_TAGS.filter(tag => tag.type === 'category'), ...customTags.value]
)

// 展开表单
const expand = () => {
  isExpanded.value = true
}

// 切换标签选择
const toggleTag = (tagId: string) => {
  const index = selectedTags.value.indexOf(tagId)
  if (index > -1) {
    selectedTags.value.splice(index, 1)
  } else {
    selectedTags.value.push(tagId)
  }
}

// 添加自定义标签
const addCustomTag = () => {
  const name = customTagName.value.trim()
  if (!name) return

  const customTag: TagType = {
    id: `custom_${Date.now()}`,
    name,
    color: '#8c8c8c',
    type: 'custom'
  }

  customTags.value.push(customTag)
  selectedTags.value.push(customTag.id)
  customTagName.value = ''
}

// 保存备注
const handleSave = () => {
  if (!content.value.trim()) return

  const note = {
    tags: [...selectedTags.value],
    content: content.value.trim(),
    priority: selectedTags.value.includes('important') ? 1 : 0
  }

  emit('add', note)
  handleCancel()
}

// 取消添加
const handleCancel = () => {
  isExpanded.value = false
  selectedTags.value = []
  content.value = ''
  customTagName.value = ''
}
</script>

<style lang="scss" scoped>
.add-note-form {
  .add-note-trigger {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    border: 2px dashed #d9d9d9;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #8c8c8c;

    &:hover {
      border-color: #1890ff;
      color: #1890ff;
      background: rgba(24, 144, 255, 0.02);
    }

    .add-icon {
      font-size: 1rem;
    }

    .add-text {
      font-size: 0.875rem;
    }
  }

  .add-note-content {
    border: 1px solid #d9d9d9;
    border-radius: 0.5rem;
    padding: 1rem;
    background: white;

    .tag-selection {
      margin-bottom: 1rem;

      .tag-section {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 0.75rem;

        .section-label {
          font-size: 0.875rem;
          color: #595959;
          font-weight: 500;
          min-width: 4rem;
        }

        .tag-options {
          display: flex;
          flex-wrap: wrap;
          gap: 0.5rem;

          .note-tag {
            &.selected {
              box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.3);
            }
          }
        }
      }

      .custom-tag-input {
        margin-top: 0.5rem;

        :deep(.el-input-group__append) {
          .el-button {
            margin: 0;
          }
        }
      }
    }

    .content-input {
      margin-bottom: 1rem;

      :deep(.el-textarea) {
        .el-textarea__inner {
          border-radius: 0.375rem;
          font-size: 0.875rem;
          line-height: 1.5;

          &:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          }
        }
      }
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 0.5rem;
    }
  }
}</style>
