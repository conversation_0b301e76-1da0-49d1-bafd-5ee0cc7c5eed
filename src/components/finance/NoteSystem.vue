<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-20 00:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-20 23:35:00
 * @FilePath     : /src/components/finance/NoteSystem.vue
 * @Description  : 备注系统主组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-20 00:00:00
-->

<template>
  <div class="note-system">
    <!-- 标题和筛选 -->
    <div class="note-header">
      <div class="note-title">
        <el-icon class="title-icon">
          <EditPen />
        </el-icon>
        <span>备注信息</span>
        <span v-if="notes.length > 0" class="note-count">({{ filteredNotes.length }}/{{ notes.length }})</span>
      </div>
      
      <!-- 筛选和搜索 -->
      <div class="note-filters" v-if="notes.length > 0">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索备注..."
          size="small"
          clearable
          style="width: 200px"
        >
          <template #prefix>
            <el-icon>
              <Search />
            </el-icon>
          </template>
        </el-input>
        
        <el-select
          v-model="filterTag"
          placeholder="筛选标签"
          size="small"
          clearable
          style="width: 120px"
        >
          <el-option
            v-for="tag in usedTags"
            :key="tag.id"
            :label="tag.name"
            :value="tag.id"
          >
            <NoteTag :tag="tag" size="small" />
          </el-option>
        </el-select>
      </div>
    </div>

    <!-- 备注列表 -->
    <div class="note-list">
      <template v-if="filteredNotes.length > 0">
        <NoteItem
          v-for="note in filteredNotes"
          :key="note.id"
          :note="note"
          @update="handleUpdateNote"
          @delete="handleDeleteNote"
          @tag-click="handleTagFilter"
        />
      </template>
      
      <div v-else-if="notes.length > 0" class="empty-filtered">
        <el-empty description="没有找到匹配的备注" :image-size="80" />
      </div>
    </div>

    <!-- 添加备注 -->
    <div class="add-note-section">
      <AddNoteForm @add="handleAddNote" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { EditPen, Search } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import NoteItem from './NoteItem.vue'
import NoteTag from './NoteTag.vue'
import AddNoteForm from './AddNoteForm.vue'
import type { NoteItem as NoteItemType, TagType } from '../../types/note'
import { PRESET_TAGS } from '../../types/note'

interface Props {
  modelValue: NoteItemType[]
}

interface Emits {
  (e: 'update:modelValue', notes: NoteItemType[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 状态
const searchKeyword = ref('')
const filterTag = ref('')

// 计算属性
const notes = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 已使用的标签
const usedTags = computed(() => {
  const tagIds = new Set<string>()
  notes.value.forEach(note => {
    note.tags.forEach(tagId => tagIds.add(tagId))
  })
  
  return Array.from(tagIds)
    .map(tagId => getTagById(tagId))
    .filter(Boolean)
})

// 筛选后的备注
const filteredNotes = computed(() => {
  let result = [...notes.value]

  // 搜索筛选
  if (searchKeyword.value.trim()) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(note => 
      note.content.toLowerCase().includes(keyword) ||
      note.tags.some(tagId => {
        const tag = getTagById(tagId)
        return tag?.name.toLowerCase().includes(keyword)
      })
    )
  }

  // 标签筛选
  if (filterTag.value) {
    result = result.filter(note => note.tags.includes(filterTag.value))
  }

  // 按优先级和时间排序
  return result.sort((a, b) => {
    // 优先级排序（重要的在前）
    if (a.priority !== b.priority) {
      return b.priority - a.priority
    }
    // 时间排序（新的在前）
    return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
  })
})

// 根据ID获取标签
const getTagById = (tagId: string): TagType | undefined => {
  return PRESET_TAGS.find(tag => tag.id === tagId)
}

// 添加备注
const handleAddNote = (noteData: Omit<NoteItemType, 'id' | 'createdAt' | 'updatedAt'>) => {
  const newNote: NoteItemType = {
    ...noteData,
    id: `note_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    createdAt: new Date(),
    updatedAt: new Date()
  }

  notes.value = [newNote, ...notes.value]
  ElMessage.success('备注添加成功')
}

// 更新备注
const handleUpdateNote = (updatedNote: NoteItemType) => {
  const index = notes.value.findIndex(note => note.id === updatedNote.id)
  if (index > -1) {
    notes.value[index] = updatedNote
    ElMessage.success('备注更新成功')
  }
}

// 删除备注
const handleDeleteNote = (noteId: string) => {
  notes.value = notes.value.filter(note => note.id !== noteId)
  ElMessage.success('备注删除成功')
}

// 标签筛选
const handleTagFilter = (tag: TagType) => {
  filterTag.value = filterTag.value === tag.id ? '' : tag.id
}

// 监听筛选变化，清空搜索
watch(filterTag, () => {
  if (filterTag.value) {
    searchKeyword.value = ''
  }
})
</script>

<style lang="scss" scoped>
.note-system {
  .note-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;

    .note-title {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 1rem;
      font-weight: 600;
      color: #262626;

      .title-icon {
        color: #1890ff;
      }

      .note-count {
        font-size: 0.875rem;
        color: #8c8c8c;
        font-weight: 400;
      }
    }

    .note-filters {
      display: flex;
      align-items: center;
      gap: 0.75rem;
    }
  }

  .note-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-bottom: 1rem;
    min-height: 100px;

    .empty-filtered {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 150px;
    }
  }

  .add-note-section {
    border-top: 1px solid #f0f0f0;
    padding-top: 1rem;
  }
}
</style>
