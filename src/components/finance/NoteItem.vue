<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-20 00:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-20 23:35:00
 * @FilePath     : /src/components/finance/NoteItem.vue
 * @Description  : 备注条目组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-20 00:00:00
-->

<template>
  <div
    class="note-item"
    @mouseenter="showActions = true"
    @mouseleave="showActions = false"
  >
    <div class="note-content">
      <!-- 标签区域 -->
      <div class="note-tags">
        <NoteTag
          v-for="tagId in note.tags"
          :key="tagId"
          :tag="getTagById(tagId)"
          size="small"
          clickable
          @click="handleTagClick"
        />
      </div>
      
      <!-- 内容区域 -->
      <div class="note-text" @dblclick="startEdit">
        <template v-if="!isEditing">
          {{ note.content }}
        </template>
        <el-input
          v-else
          v-model="editContent"
          type="textarea"
          :rows="2"
          placeholder="请输入备注内容"
          @blur="saveEdit"
          @keydown.enter.ctrl="saveEdit"
          @keydown.esc="cancelEdit"
          ref="editInputRef"
        />
      </div>
    </div>

    <!-- 操作按钮 -->
    <div v-show="showActions" class="note-actions">
      <el-button
        type="text"
        size="small"
        :icon="Edit"
        @click="startEdit"
        title="编辑"
      />
      <el-button
        type="text"
        size="small"
        :icon="Delete"
        @click="handleDelete"
        title="删除"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'
import { Edit, Delete } from '@element-plus/icons-vue'
import { ElMessageBox } from 'element-plus'
import NoteTag from './NoteTag.vue'
import type { NoteItem as NoteItemType, TagType } from '../../types/note'
import { PRESET_TAGS } from '../../types/note'

interface Props {
  note: NoteItemType
}

interface Emits {
  (e: 'update', note: NoteItemType): void
  (e: 'delete', noteId: string): void
  (e: 'tag-click', tag: TagType): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 状态
const showActions = ref(false)
const isEditing = ref(false)
const editContent = ref('')
const editInputRef = ref()

// 根据ID获取标签
const getTagById = (tagId: string): TagType => {
  return PRESET_TAGS.find(tag => tag.id === tagId) || {
    id: tagId,
    name: tagId,
    color: '#8c8c8c',
    type: 'custom'
  }
}

// 开始编辑
const startEdit = async () => {
  isEditing.value = true
  editContent.value = props.note.content
  await nextTick()
  editInputRef.value?.focus()
}

// 保存编辑
const saveEdit = () => {
  if (editContent.value.trim() !== props.note.content) {
    const updatedNote = {
      ...props.note,
      content: editContent.value.trim(),
      updatedAt: new Date()
    }
    emit('update', updatedNote)
  }
  isEditing.value = false
}

// 取消编辑
const cancelEdit = () => {
  isEditing.value = false
  editContent.value = ''
}

// 删除备注
const handleDelete = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条备注吗？',
      '删除确认',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    emit('delete', props.note.id)
  } catch {
    // 用户取消删除
  }
}

// 标签点击
const handleTagClick = (tag: TagType) => {
  emit('tag-click', tag)
}
</script>

<style lang="scss" scoped>
.note-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 0.75rem;
  border: 1px solid #f0f0f0;
  border-radius: 0.5rem;
  background: white;
  transition: all 0.2s ease;

  &:hover {
    border-color: #d9d9d9;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .note-content {
    flex: 1;
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;

    .note-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 0.375rem;
      min-width: fit-content;
    }

    .note-text {
      flex: 1;
      font-size: 0.875rem;
      line-height: 1.5;
      color: #262626;
      cursor: text;

      &:hover {
        background: rgba(0, 0, 0, 0.02);
        border-radius: 0.25rem;
      }

      :deep(.el-textarea) {
        .el-textarea__inner {
          border: 1px solid #d9d9d9;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          line-height: 1.5;

          &:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          }
        }
      }
    }
  }

  .note-actions {
    display: flex;
    gap: 0.25rem;
    opacity: 0.7;

    .el-button {
      padding: 0.25rem;
      min-height: auto;

      &:hover {
        background: rgba(0, 0, 0, 0.06);
      }
    }
  }
}</style>
