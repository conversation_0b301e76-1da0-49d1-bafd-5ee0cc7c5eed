<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-20 00:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-20 23:35:00
 * @FilePath     : /src/components/finance/NoteTag.vue
 * @Description  : 备注标签组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-20 00:00:00
-->

<template>
  <span
    :class="[
      'note-tag',
      size,
      { clickable: clickable }
    ]"
    :style="tagStyle"
    @click="handleClick"
  >
    <span v-if="tag.icon" class="tag-icon">{{ tag.icon }}</span>
    <span class="tag-text">{{ tag.name }}</span>
    <span v-if="closable" class="tag-close" @click.stop="handleClose">
      <el-icon :size="12">
        <Close />
      </el-icon>
    </span>
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Close } from '@element-plus/icons-vue'
import type { TagType } from '../../types/note'

interface Props {
  tag: TagType
  size?: 'small' | 'default' | 'large'
  clickable?: boolean
  closable?: boolean
}

interface Emits {
  (e: 'click', tag: TagType): void
  (e: 'close', tag: TagType): void
}

const props = withDefaults(defineProps<Props>(), {
  size: 'default',
  clickable: false,
  closable: false
})

const emit = defineEmits<Emits>()

// 标签样式
const tagStyle = computed(() => ({
  backgroundColor: props.tag.color,
  color: getTextColor(props.tag.color),
  borderColor: props.tag.color
}))

// 根据背景色计算文字颜色
const getTextColor = (backgroundColor: string): string => {
  // 简单的颜色亮度判断
  const color = backgroundColor.replace('#', '')
  const r = parseInt(color.substr(0, 2), 16)
  const g = parseInt(color.substr(2, 2), 16)
  const b = parseInt(color.substr(4, 2), 16)
  const brightness = (r * 299 + g * 587 + b * 114) / 1000
  return brightness > 128 ? '#000000' : '#ffffff'
}

// 点击事件
const handleClick = () => {
  if (props.clickable) {
    emit('click', props.tag)
  }
}

// 关闭事件
const handleClose = () => {
  emit('close', props.tag)
}
</script>

<style lang="scss" scoped>
.note-tag {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1;
  border: 1px solid;
  transition: all 0.2s ease;
  white-space: nowrap;

  &.small {
    padding: 0.125rem 0.375rem;
    font-size: 0.625rem;
  }

  &.large {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
  }

  &.clickable {
    cursor: pointer;

    &:hover {
      opacity: 0.8;
      transform: translateY(-1px);
    }
  }

  .tag-icon {
    font-size: 0.875em;
  }

  .tag-text {
    flex: 1;
  }

  .tag-close {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-left: 0.25rem;
    opacity: 0.7;

    &:hover {
      opacity: 1;
    }
  }
}</style>
