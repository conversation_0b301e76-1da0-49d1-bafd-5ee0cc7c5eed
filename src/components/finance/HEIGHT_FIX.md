# 输入框高度优化说明

## 🎯 问题描述

1. **CurrencyInput 货币输入框太高** - 由于 padding 设置过大 (0.75rem)
2. **记账周期日期选择器太矮** - 缺少合适的 padding 和高度设置

## 🔧 解决方案

### 1. CurrencyInput 高度优化

**问题**: 输入框过高，视觉上不协调
**原因**: `padding: 0.75rem` 导致垂直空间过大

**修复**:
```scss
// 减少 padding，统一高度
.currency-symbol {
  padding: 0.5rem 0 0.5rem 0.75rem;  // 减少垂直 padding
  height: 36px;                       // 固定高度
  display: flex;
  align-items: center;                // 垂直居中
}

:deep(.currency-input) {
  .el-input__wrapper {
    padding: 0.5rem 0.75rem 0.5rem 0;  // 减少垂直 padding
    min-height: 36px;                   // 最小高度
  }
  
  .el-input__inner {
    height: auto;
    line-height: 1.5;                   // 优化行高
  }
}
```

### 2. 记账周期日期选择器高度优化

**问题**: 日期选择器过矮，与其他输入框不协调
**原因**: 缺少合适的 padding 和高度设置

**修复**:
```scss
:deep(.period-picker) {
  .el-input__wrapper {
    padding: 0.5rem 0.75rem;    // 添加合适的 padding
    min-height: 36px;           // 与货币输入框保持一致
  }
  
  .el-input__inner {
    height: auto;
    line-height: 1.5;           // 优化行高
  }
}
```

## 📏 统一的高度规范

### 设计原则
- **统一高度**: 所有输入框最小高度 36px
- **一致 padding**: 垂直 padding 0.5rem，水平 padding 0.75rem
- **行高优化**: line-height 1.5 确保文字垂直居中

### 高度对比

| 组件 | 优化前 | 优化后 | 改进 |
|------|--------|--------|------|
| 货币输入框 | ~48px (过高) | 36px | ✅ 减少 12px |
| 日期选择器 | ~28px (过矮) | 36px | ✅ 增加 8px |
| 视觉一致性 | 不统一 | 统一 | ✅ 协调美观 |

## 🎨 视觉效果

### 优化后的优势
1. **视觉协调**: 所有输入框高度一致
2. **空间利用**: 减少不必要的垂直空间
3. **用户体验**: 更紧凑、专业的界面
4. **品牌一致性**: 统一的设计规范

### CSS 变量建议
```scss
// 可以考虑定义 CSS 变量统一管理
:root {
  --input-height: 36px;
  --input-padding-vertical: 0.5rem;
  --input-padding-horizontal: 0.75rem;
  --input-line-height: 1.5;
}
```

## 🔄 响应式考虑

- 在移动端可能需要稍微增加高度以便触摸操作
- 保持相对单位 (rem) 确保可缩放性
- 行高设置确保在不同字体大小下都能正确显示

## ✅ 验证清单

- [x] 货币输入框高度减少到合理范围
- [x] 日期选择器高度增加到与其他输入框一致
- [x] 货币符号垂直居中对齐
- [x] 所有输入框视觉高度统一
- [x] 保持良好的可访问性和可读性
