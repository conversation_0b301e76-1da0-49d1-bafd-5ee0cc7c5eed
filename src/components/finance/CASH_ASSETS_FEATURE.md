# 现金资产功能说明

## 🎯 新增功能

为财务表单添加了第三个标签页 - **"现金资产"**，用于管理储蓄卡余额和现金类资产。

## 📋 功能结构

### 1. 标签页导航
```
收入项目 | 支出项目 | 现金资产
```

### 2. 现金资产分类

#### 储蓄卡余额
- **招商银行** (cmBank)
- **工商银行** (icbcBank) 
- **浦发银行** (spdbBank)

#### 现金及其他
- **现金** (cash)
- **支付宝余额** (alipay)
- **微信余额** (wechatPay)

## 🎨 设计特色

### 视觉设计
- **图标颜色**: 蓝色系 (#1890FF) 表示银行卡
- **绿色系**: 现金 (#52C41A)
- **品牌色**: 支付宝蓝 (#1677FF)、微信绿 (#07C160)

### 卡片布局
```vue
<div class="cash-panel">
  <div class="cash-cards">
    <ExpenseCard title="储蓄卡余额" :fields="bankFields" />
    <ExpenseCard title="现金及其他" :fields="cashFields" />
  </div>
</div>
```

## 🔧 技术实现

### 1. 配置文件扩展 (financeConfig.ts)
```typescript
// 新增银行图标
export const ICONS = {
  // ... 现有图标
  bank: '<path d="M3 21h18" /><path d="M5 21V7l8-4v18" />...'
}

// 现金资产分类配置
export const CASH_CATEGORIES = [
  {
    title: '储蓄卡余额',
    fields: [
      {
        name: 'cmBank',
        label: '招商银行',
        icon: ICONS.bank,
        color: '#1890FF',
        step: 100,
        span: 8
      },
      // ... 其他银行
    ]
  },
  // ... 现金及其他分类
]
```

### 2. 数据模型扩展 (useFinanceForm.ts)
```typescript
export interface FinanceFormData {
  // ... 现有字段
  // 现金资产字段
  cmBank: number
  icbcBank: number
  spdbBank: number
  cash: number
  alipay: number
  wechatPay: number
}
```

### 3. 组件集成 (RecordForm.vue)
```vue
<!-- 标签页导航 -->
<button @click="activeTab = 'cash'">现金资产</button>

<!-- 现金资产面板 -->
<div v-show="activeTab === 'cash'" class="cash-panel">
  <ExpenseCard 
    v-for="category in cashCategories"
    :title="category.title"
    :fields="category.fields"
    v-model="formData"
  />
</div>
```

## 📊 数据流程

### 输入 → 存储 → 提交
1. **用户输入**: 在现金资产面板输入各银行卡余额
2. **实时存储**: 数据存储在 `formData` 中
3. **表单提交**: 包含在提交数据中

### 模拟数据示例
```typescript
const mockData = {
  // ... 其他数据
  cmBank: 5000,      // 招商银行 5000元
  icbcBank: 3000,    // 工商银行 3000元
  spdbBank: 2000,    // 浦发银行 2000元
  cash: 500,         // 现金 500元
  alipay: 800,       // 支付宝 800元
  wechatPay: 300     // 微信 300元
}
```

## 🎯 使用场景

### 个人财务管理
- **资产盘点**: 记录各银行卡余额
- **现金流管理**: 跟踪现金和电子钱包余额
- **财务规划**: 了解总体资产分布

### 数据分析
- **银行偏好**: 分析主要使用的银行
- **资金分布**: 现金vs电子支付比例
- **流动性管理**: 各账户资金配置

## 🔄 扩展性

### 易于添加新银行
```typescript
// 在 CASH_CATEGORIES 中添加新银行
{
  name: 'newBank',
  label: '新银行',
  icon: ICONS.bank,
  color: '#1890FF',
  step: 100,
  span: 8
}
```

### 支持新的资产类型
- 投资账户
- 数字货币
- 理财产品
- 基金账户

## ✅ 完成清单

- [x] 添加现金资产标签页
- [x] 配置储蓄卡字段（招商、工商、浦发）
- [x] 配置现金及电子钱包字段
- [x] 集成到表单数据模型
- [x] 添加样式和布局
- [x] 更新提交和加载逻辑
- [x] 添加模拟数据

现在用户可以完整地管理收入、支出和现金资产三个维度的财务数据！🎉
