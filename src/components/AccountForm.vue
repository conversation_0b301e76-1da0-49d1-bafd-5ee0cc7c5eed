<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-28 11:10:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-24 08:30:51
 * @FilePath     : /src/components/AccountForm.vue
 * @Description  : 通用账号表单组件，基于字段配置动态生成表单
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-28 11:10:00
-->

<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    label-position="top"
    @submit.prevent="handleSubmit"
  >
    <!-- 按分组渲染字段 -->
    <template v-for="group in groupedFields" :key="group.key">
      <div
        v-if="group.fields.length > 0 || group.key === 'custom'"
        class="form-group"
      >
        <div class="group-header">
          <h3 v-if="showGroupTitles" class="group-title">{{ group.title }}</h3>
        </div>

        <!-- 动态网格布局 -->
        <el-row :gutter="24">
          <el-col
            v-for="field in group.fields"
            :key="field.key"
            :span="field.gridSpan || 12"
          >
            <el-form-item
              :prop="field.key"
              :label="field.label"
              :required="field.required"
            >
              <!-- 普通输入框 -->
              <el-input
                v-if="field.type === 'input'"
                v-model="formData[field.key]"
                :placeholder="field.placeholder"
                size="large"
              />

              <!-- 密码输入框 -->
              <el-input
                v-else-if="field.type === 'password'"
                v-model="formData[field.key]"
                :placeholder="field.placeholder"
                size="large"
                type="password"
                show-password
              />

              <!-- 邮箱输入框 -->
              <el-input
                v-else-if="field.type === 'email'"
                v-model="formData[field.key]"
                :placeholder="field.placeholder"
                size="large"
                type="email"
              />

              <!-- 手机号输入框 -->
              <el-input
                v-else-if="field.type === 'phone'"
                v-model="formData[field.key]"
                :placeholder="field.placeholder"
                size="large"
                type="tel"
              />

              <!-- URL输入框 -->
              <el-input
                v-else-if="field.type === 'url'"
                v-model="formData[field.key]"
                :placeholder="field.placeholder"
                size="large"
                type="url"
              />

              <!-- 文本域 -->
              <el-input
                v-else-if="field.type === 'textarea'"
                v-model="formData[field.key]"
                :placeholder="field.placeholder"
                :rows="4"
                size="large"
                type="textarea"
              />

              <!-- 分类选择器 -->
              <el-select
                v-else-if="
                  field.type === 'select' && field.key === 'category_id'
                "
                v-model="formData[field.key]"
                :placeholder="field.placeholder"
                size="large"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="category in categories"
                  :key="category.id"
                  :value="category.id"
                  :label="category.name"
                >
                  <div class="category-option">
                    <div
                      class="category-dot"
                      :style="{ backgroundColor: category.color }"
                    ></div>
                    {{ category.name }}
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 自定义字段分组内容 -->
          <template v-if="group.key === 'custom'">
            <!-- 动态自定义字段 -->
            <el-col
              v-for="(customField, index) in customFields"
              :key="`custom_${index}`"
              :span="12"
            >
              <el-form-item :label="customField.label">
                <div class="custom-field-wrapper">
                  <el-input
                    v-model="customField.value"
                    :placeholder="customField.placeholder"
                    size="large"
                  />
                  <el-button
                    text
                    size="small"
                    @click="removeCustomField(index)"
                    class="remove-field-btn"
                  >
                    <el-icon>
                      <Close />
                    </el-icon>
                  </el-button>
                </div>
              </el-form-item>
            </el-col>

            <!-- 新增自定义字段输入框 - 始终单独一行 -->
            <el-col :span="24">
              <el-form-item label=" ">
                <div class="add-field-input">
                  <el-input
                    v-model="newFieldName"
                    placeholder="输入字段名并按回车添加"
                    size="large"
                    @keyup.enter="addCustomFieldDirect"
                    @blur="addCustomFieldDirect"
                    class="new-field-input"
                  >
                    <template #prefix>
                      <el-icon style="color: #409eff">
                        <Plus />
                      </el-icon>
                    </template>
                  </el-input>
                </div>
              </el-form-item>
            </el-col>
          </template>
        </el-row>
      </div>
    </template>

    <!-- 表单操作按钮 -->
    <div v-if="showActions" class="form-actions">
      <el-button @click="handleCancel" size="large">
        {{ cancelText }}
      </el-button>
      <el-button
        type="primary"
        :loading="loading"
        @click="handleSubmit"
        size="large"
      >
        {{ submitText }}
      </el-button>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { Plus, Close } from "@element-plus/icons-vue";
import { showWarning, showSuccess } from "../utils";
import {
  FIELD_GROUPS,
  getFieldsByGroup,
  generateFormRules,
} from "../config/account-fields";

interface CustomField {
  label: string;
  value: string;
  placeholder: string;
}

interface Props {
  modelValue: Record<string, any>;
  loading?: boolean;
  showActions?: boolean;
  showGroupTitles?: boolean;
  submitText?: string;
  cancelText?: string;
  categories?: Array<{ id: string; name: string; color?: string }>;
}

interface Emits {
  (e: "update:modelValue", value: Record<string, any>): void;
  (e: "submit", value: Record<string, any>): void;
  (e: "cancel"): void;
  (e: "validate", valid: boolean): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  showActions: true,
  showGroupTitles: true,
  submitText: "提交",
  cancelText: "取消",
  categories: () => [],
});

const emit = defineEmits<Emits>();

const formRef = ref();
const formData = ref<Record<string, any>>({ ...props.modelValue });

// 自定义字段相关
const customFields = ref<CustomField[]>([]);
const newFieldName = ref("");

// 生成表单验证规则
const formRules = computed(() => generateFormRules());

// 按分组整理字段
const groupedFields = computed(() => {
  const groups = Object.entries(FIELD_GROUPS)
    .sort(([, a], [, b]) => a.order - b.order)
    .map(([key, value]) => ({
      key,
      title: value.title,
      fields: getFieldsByGroup(key),
    }));

  return groups;
});

// 监听表单数据变化，同步到父组件
watch(
  [formData, customFields],
  () => {
    const allData = { ...formData.value };
    // 将自定义字段数据转换为custom_fields对象
    const customFieldsObj: Record<string, string> = {};
    customFields.value.forEach((field) => {
      if (field.label && field.value) {
        customFieldsObj[field.label] = field.value;
      }
    });
    allData.custom_fields = customFieldsObj;
    emit("update:modelValue", allData);
  },
  { deep: true }
);

// 监听父组件数据变化，同步到表单
watch(
  () => props.modelValue,
  (newValue) => {
    formData.value = { ...newValue };
    // 提取自定义字段数据
    const customFieldData: CustomField[] = [];
    if (newValue.custom_fields && typeof newValue.custom_fields === "object") {
      Object.entries(newValue.custom_fields).forEach(([label, value]) => {
        if (label && value) {
          customFieldData.push({
            label,
            value: value as string,
            placeholder: `请输入${label}`,
          });
        }
      });
    }
    customFields.value = customFieldData;
  },
  { deep: true, immediate: true }
);

// 直接添加自定义字段
const addCustomFieldDirect = () => {
  const fieldName = newFieldName.value.trim();

  if (!fieldName) {
    return; // 空字段名不处理
  }

  // 检查是否重复
  const isDuplicate = customFields.value.some(
    (field) => field.label === fieldName
  );
  if (isDuplicate) {
    showWarning("字段名称已存在");
    newFieldName.value = "";
    return;
  }

  customFields.value.push({
    label: fieldName,
    value: "",
    placeholder: `请输入${fieldName}`,
  });

  newFieldName.value = "";
  showSuccess(`已添加字段"${fieldName}"`);
};

// 移除自定义字段
const removeCustomField = (index: number) => {
  const field = customFields.value[index];
  customFields.value.splice(index, 1);
  showSuccess(`已移除字段"${field.label}"`);
};

// 表单提交
const handleSubmit = async () => {
  try {
    const validateResult = await formRef.value?.validate();
    const isValid = validateResult === undefined;

    emit("validate", isValid);

    if (isValid) {
      const submitData = { ...formData.value };
      // 包含自定义字段数据
      const customFieldsObj: Record<string, string> = {};
      customFields.value.forEach((field) => {
        if (field.label && field.value) {
          customFieldsObj[field.label] = field.value;
        }
      });
      submitData.custom_fields = customFieldsObj;
      emit("submit", submitData);
    }
  } catch (error) {
    emit("validate", false);
    console.error("Form validation failed:", error);
  }
};

// 取消操作
const handleCancel = () => {
  emit("cancel");
};

// 暴露验证方法给父组件
const validate = async () => {
  try {
    const result = await formRef.value?.validate();
    return result === undefined;
  } catch (error) {
    return false;
  }
};

// 暴露重置方法给父组件
const resetFields = () => {
  formRef.value?.resetFields();
  customFields.value = [];
};

// 暴露清空验证方法给父组件
const clearValidate = () => {
  formRef.value?.clearValidate();
};

defineExpose({
  validate,
  resetFields,
  clearValidate,
});
</script>

<style lang="scss" scoped>
.form-group {
  margin-bottom: 32px;

  &:last-child {
    margin-bottom: 0;
  }

  .group-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;

    .group-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #262626;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;
      flex: 1;
    }

    .add-field-btn {
      margin-left: 16px;
      color: #1890ff;

      &:hover {
        color: #40a9ff;
      }
    }
  }
}

.category-option {
  display: flex;
  align-items: center;
  gap: 8px;

  .category-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }
}

.custom-field-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;

  .remove-field-btn {
    color: #f5222d;
    flex-shrink: 0;

    &:hover {
      color: #ff4d4f;
      background-color: #fff2f0;
    }
  }
}

.add-field-input {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px dashed #e8e8e8;

  .new-field-input {
    border-style: dashed;
    border-color: #d9d9d9;
    background-color: #fafafa;
    transition: all 0.3s ease;

    &:hover {
      border-color: #1890ff;
      background-color: #f6ffed;
    }

    &:focus {
      border-style: solid;
      background-color: white;
    }

    :deep(.el-input__prefix) {
      color: #409eff;
    }
  }
}

.form-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
  justify-content: flex-end;
}

// 响应式设计
@media (max-width: 768px) {
  .form-group {
    margin-bottom: 24px;

    .group-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;

      .group-title {
        font-size: 14px;
        margin-bottom: 12px;
      }

      .add-field-btn {
        margin-left: 0;
        align-self: flex-end;
      }
    }
  }

  .form-actions {
    flex-direction: column;
    gap: 12px;

    :deep(.el-button) {
      width: 100%;
    }
  }

  .custom-field-wrapper {
    flex-direction: column;
    gap: 8px;

    .remove-field-btn {
      align-self: flex-end;
    }
  }
}
</style>
