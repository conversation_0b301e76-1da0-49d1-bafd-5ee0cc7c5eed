<template>
  <div class="categories">
    <AppLayout>
      <div class="categories-content">
        <div class="categories-header">
          <div class="header-left">
            <h1>分类管理</h1>
            <p>管理账号分类，让您的账号更有序</p>
          </div>
          <div class="header-right">
            <el-button type="primary" @click="showAddModal = true">
              <el-icon><Plus /></el-icon>
              添加分类
            </el-button>
          </div>
        </div>

        <div class="categories-list">
          <div
            v-if="accountsStore.isLoading"
            class="loading-state"
            v-loading="accountsStore.isLoading"
          >
            <p>加载中...</p>
          </div>

          <div v-else class="categories-grid">
            <el-card
              v-for="category in accountsStore.categories"
              :key="category.id"
              class="category-card"
              shadow="hover"
            >
              <template #header>
                <div class="category-title">
                  <div class="category-info-left">
                    <div
                      class="category-icon"
                      :style="{ backgroundColor: category.color }"
                    >
                      {{ category.name.charAt(0) }}
                    </div>
                    <span>{{ category.name }}</span>
                  </div>
                  <div class="card-actions">
                    <el-dropdown
                      @command="(value: string) => onCategoryAction(value, category)"
                    >
                      <el-button type="default" size="small" text>
                        <el-icon><MoreFilled /></el-icon>
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item command="edit">
                            <el-icon><Edit /></el-icon>
                            编辑
                          </el-dropdown-item>
                          <el-dropdown-item command="delete">
                            <el-icon><Delete /></el-icon>
                            删除
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
              </template>

              <div class="category-info">
                <!-- 主要统计信息 -->
                <div class="stats-section">
                  <div class="stat-item primary">
                    <div class="stat-number">
                      {{ getCategoryAccountCount(category.id) }}
                    </div>
                    <div class="stat-label">个账号</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-number">
                      {{ getRecentUsedCount(category.id) }}
                    </div>
                    <div class="stat-label">最近使用</div>
                  </div>
                </div>

                <!-- 分隔线 -->
                <div class="divider"></div>

                <!-- 详细信息 -->
                <div class="details-section">
                  <div class="detail-row">
                    <span class="detail-label">创建时间</span>
                    <span class="detail-value">{{
                      formatDate(category.created_at)
                    }}</span>
                  </div>
                  <div class="detail-row">
                    <span class="detail-label">最后更新</span>
                    <span class="detail-value">{{
                      getLastUsedTime(category.id)
                    }}</span>
                  </div>
                </div>

                <!-- 快速预览账号 -->
                <div
                  class="preview-section"
                  v-if="getCategoryAccounts(category.id).length > 0"
                >
                  <div class="preview-label">包含账号</div>
                  <div class="account-previews">
                    <div
                      v-for="account in getCategoryAccounts(category.id).slice(
                        0,
                        3
                      )"
                      :key="account.id"
                      class="account-preview"
                      :title="account.platform"
                    >
                      {{ account.platform.charAt(0) }}
                    </div>
                    <div
                      v-if="getCategoryAccounts(category.id).length > 3"
                      class="account-preview more"
                    >
                      +{{ getCategoryAccounts(category.id).length - 3 }}
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </div>
        </div>

        <!-- 添加/编辑分类弹窗 -->
        <el-dialog
          v-model="showAddModal"
          :title="editingCategory ? '编辑分类' : '添加分类'"
          width="500px"
          @close="handleCancelEdit"
        >
          <el-form
            ref="categoryFormRef"
            :model="categoryForm"
            :rules="categoryRules"
            label-position="top"
          >
            <el-form-item prop="name" label="分类名称" required>
              <el-input
                v-model="categoryForm.name"
                placeholder="输入分类名称"
                size="large"
              />
            </el-form-item>

            <el-form-item prop="color" label="分类颜色">
              <div class="color-picker">
                <!-- 颜色选择器和输入框组合 -->
                <div class="color-input-group">
                  <el-color-picker
                    v-model="categoryForm.color"
                    :predefine="presetColors"
                    show-alpha
                    size="large"
                    @change="onColorChange"
                  />
                  <el-input
                    v-model="categoryForm.color"
                    placeholder="#1890ff"
                    size="large"
                    style="flex: 1; margin-left: 12px"
                    @change="validateColor"
                  >
                    <template #suffix>
                      <div
                        class="color-preview-suffix"
                        :style="{ backgroundColor: categoryForm.color }"
                      ></div>
                    </template>
                  </el-input>
                </div>

                <!-- 预设颜色快速选择 -->
                <div class="preset-colors-section">
                  <div class="section-title">常用颜色</div>
                  <div class="preset-colors">
                    <div
                      v-for="color in presetColors"
                      :key="color"
                      class="color-option"
                      :class="{
                        active:
                          categoryForm.color.toLowerCase() ===
                          color.toLowerCase(),
                      }"
                      :style="{ backgroundColor: color }"
                      @click="selectPresetColor(color)"
                      :title="color"
                    ></div>
                  </div>
                </div>
              </div>
            </el-form-item>
          </el-form>

          <template #footer>
            <div class="dialog-footer">
              <el-button @click="handleCancelEdit">取消</el-button>
              <el-button
                type="primary"
                :loading="saving"
                @click="handleSaveCategory"
              >
                {{ editingCategory ? "保存" : "添加" }}
              </el-button>
            </div>
          </template>
        </el-dialog>
      </div>
    </AppLayout>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus, MoreFilled, Edit, Delete } from "@element-plus/icons-vue";
import AppLayout from "../components/AppLayout.vue";
import { useAccountsStore } from "../stores/accounts";
import type {
  Category,
  CreateCategoryRequest,
  UpdateCategoryRequest,
} from "../stores/accounts";

const accountsStore = useAccountsStore();

const showAddModal = ref(false);
const editingCategory = ref<Category | null>(null);
const categoryFormRef = ref();
const saving = ref(false);

const categoryForm = reactive({
  name: "",
  color: "#1890ff",
});

const categoryRules = {
  name: [
    { required: true, message: "请输入分类名称" },
    { minLength: 1, maxLength: 20, message: "分类名称长度为1-20个字符" },
    {
      validator: (value: string, callback: (error?: string) => void) => {
        if (!value || value.trim() === "") {
          callback("分类名称不能为空或只包含空格");
          return;
        }

        // 检查是否与现有分类重名（编辑时排除当前分类）
        const existingCategory = accountsStore.categories.find(
          (cat) =>
            cat.name.toLowerCase() === value.trim().toLowerCase() &&
            (!editingCategory.value || cat.id !== editingCategory.value.id)
        );

        if (existingCategory) {
          callback("分类名称已存在");
          return;
        }

        callback();
      },
    },
  ],
};

const presetColors = [
  // 主色调
  "#1890ff", // 蓝色
  "#52c41a", // 绿色
  "#faad14", // 金色
  "#f5222d", // 红色
  "#722ed1", // 紫色
  "#fa8c16", // 橙色

  // 扩展色调
  "#13c2c2", // 青色
  "#eb2f96", // 粉色
  "#2f54eb", // 靛蓝
  "#a0d911", // 青绿
  "#ff7a45", // 橙红
  "#ffc53d", // 柠檬黄

  // 深色调
  "#001529", // 深蓝
  "#002329", // 深青
  "#391085", // 深紫
  "#820014", // 深红
  "#ad4e00", // 深橙
  "#613400", // 深黄

  // 浅色调
  "#e6f7ff", // 浅蓝
  "#f6ffed", // 浅绿
  "#fffbe6", // 浅黄
  "#fff2e8", // 浅橙
  "#f9f0ff", // 浅紫
  "#fff0f6", // 浅粉
];

onMounted(async () => {
  await loadData();
});

const loadData = async () => {
  try {
    await Promise.all([
      accountsStore.loadCategories(),
      accountsStore.loadAccounts(),
    ]);
  } catch (error) {
    ElMessage.error("加载数据失败");
    console.error(error);
  }
};

const getCategoryAccountCount = (categoryId: string) => {
  return accountsStore.accounts.filter(
    (account) => account.category_id === categoryId
  ).length;
};

// 获取分类下的账号列表
const getCategoryAccounts = (categoryId: string) => {
  return accountsStore.accounts.filter(
    (account) => account.category_id === categoryId
  );
};

// 获取最近使用的账号数量（模拟数据，实际应该基于使用时间）
const getRecentUsedCount = (categoryId: string) => {
  const accounts = getCategoryAccounts(categoryId);
  // 这里可以根据实际的使用时间逻辑来计算
  // 暂时返回总数的一半作为示例
  return Math.ceil(accounts.length / 2);
};

// 获取最后使用时间（模拟数据）
const getLastUsedTime = (categoryId: string) => {
  const accounts = getCategoryAccounts(categoryId);
  if (accounts.length === 0) return "从未使用";

  // 这里应该基于实际的使用时间数据
  // 暂时返回一个相对时间作为示例
  const days = Math.floor(Math.random() * 30);
  if (days === 0) return "今天";
  if (days === 1) return "昨天";
  if (days < 7) return `${days}天前`;
  if (days < 30) return `${Math.floor(days / 7)}周前`;
  return "很久以前";
};

const onCategoryAction = (action: string, category: Category) => {
  if (action === "edit") {
    editingCategory.value = category;
    categoryForm.name = category.name;
    categoryForm.color = category.color || "#1890ff";
    showAddModal.value = true;
  } else if (action === "delete") {
    handleDeleteCategory(category);
  }
};

const handleSaveCategory = async () => {
  // 阻止模态框默认关闭行为，只有验证成功才关闭
  return new Promise<void>((resolve, reject) => {
    const doSave = async () => {
      try {
        saving.value = true;

        // 先检查表单引用是否存在
        if (!categoryFormRef.value) {
          ElMessage.error("表单初始化失败，请重试");
          reject(new Error("表单初始化失败"));
          return;
        }

        // 手动检查分类名称是否为空
        if (!categoryForm.name || categoryForm.name.trim() === "") {
          ElMessage.warning("请输入分类名称");
          reject(new Error("分类名称为空"));
          return;
        }

        // 执行表单验证
        const validationResult = await categoryFormRef.value.validate();
        console.log("表单验证结果:", validationResult);

        // Arco Design 的 validate() 方法：
        // - 验证成功时返回 undefined
        // - 验证失败时返回错误对象
        if (validationResult !== undefined) {
          console.log("表单验证失败:", validationResult);
          ElMessage.warning("请检查表单中的必填项");
          reject(new Error("表单验证失败"));
          return;
        }

        if (editingCategory.value) {
          // 编辑分类
          const updateRequest: UpdateCategoryRequest = {
            id: editingCategory.value.id,
            name: categoryForm.name.trim(),
            color: categoryForm.color,
          };
          await accountsStore.updateCategory(updateRequest);
          ElMessage.success("分类更新成功");
        } else {
          // 创建分类
          const createRequest: CreateCategoryRequest = {
            name: categoryForm.name.trim(),
            color: categoryForm.color,
          };
          await accountsStore.createCategory(createRequest);
          ElMessage.success("分类添加成功");
        }

        handleCancelEdit();
        resolve(); // 验证成功，允许关闭模态框
      } catch (error) {
        console.error("保存分类失败:", error);

        // 检查是否是后端验证错误，显示具体错误信息
        const errorMessage = String(error);
        if (errorMessage.includes("分类名称不能为空")) {
          ElMessage.error("分类名称不能为空");
        } else if (errorMessage.includes("分类名称已存在")) {
          ElMessage.error("分类名称已存在，请使用其他名称");
        } else if (errorMessage.includes("分类名称长度")) {
          ElMessage.error("分类名称长度不能超过20个字符");
        } else {
          ElMessage.error(editingCategory.value ? "更新失败" : "添加失败");
        }

        reject(error); // 验证失败，阻止关闭模态框
      } finally {
        saving.value = false;
      }
    };

    doSave();
  });
};

const handleCancelEdit = () => {
  showAddModal.value = false;
  editingCategory.value = null;
  categoryForm.name = "";
  categoryForm.color = "#1890ff";
  saving.value = false; // 重置保存状态
};

const handleDeleteCategory = (category: Category) => {
  const accountCount = getCategoryAccountCount(category.id);

  if (accountCount > 0) {
    ElMessage.warning(`该分类下还有 ${accountCount} 个账号，无法删除`);
    return;
  }

  ElMessageBox.confirm(
    `确定要删除分类 "${category.name}" 吗？此操作不可恢复。`,
    "确认删除",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  )
    .then(async () => {
      try {
        await accountsStore.deleteCategory(category.id);
        ElMessage.success("分类删除成功");
      } catch (error) {
        ElMessage.error("删除失败");
        console.error(error);
      }
    })
    .catch(() => {
      // 用户取消删除
    });
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString("zh-CN");
};

// 颜色相关方法
const onColorChange = (color: string) => {
  categoryForm.color = color;
};

const selectPresetColor = (color: string) => {
  categoryForm.color = color;
};

const validateColor = () => {
  // 验证颜色格式是否正确
  const colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
  if (!colorRegex.test(categoryForm.color)) {
    // 如果格式不正确，重置为默认颜色
    categoryForm.color = "#1890ff";
    ElMessage.warning("颜色格式不正确，已重置为默认颜色");
  }
};
</script>

<style lang="scss" scoped>
.categories {
  height: 100vh;
}

.categories-content {
  padding: $spacing-lg;
}

.categories-header {
  @include flex-center-between;
  align-items: flex-start;
  margin-bottom: $spacing-xl;

  .header-left {
    h1 {
      margin: 0 0 $spacing-xs 0;
      font-size: $font-size-xxxl;
      font-weight: $font-weight-semibold;
      color: $text-color-primary;
      @include smooth-transition(color);
    }

    p {
      margin: 0;
      color: $text-color-secondary;
      font-size: $font-size-base;
    }
  }

  .header-right {
    :deep(.el-button) {
      @include smooth-transition(all);
      font-weight: $font-weight-medium;

      &:hover {
        transform: translateY(-1px);
        box-shadow: $shadow-lg;
      }
    }
  }
}

.loading-state {
  @include flex-column-center;
  padding: 80px 20px;
  text-align: center;

  p {
    margin-top: $spacing-base;
    color: $text-color-secondary;
    font-size: $font-size-base;
  }
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: $spacing-base;

  .category-card {
    @include smooth-transition(all);
    @include card-shadow;
    border-radius: $border-radius-lg;
    min-height: 280px;
    display: flex;
    flex-direction: column;

    &:hover {
      @include card-hover;
      transform: translateY(-4px);
      box-shadow: $shadow-xl;
    }

    // 自定义卡片样式
    :deep(.el-card__header) {
      border-bottom: 1px solid $border-color-light;
      padding: $spacing-base $spacing-lg;
      flex-shrink: 0;
    }

    :deep(.el-card__body) {
      padding: $spacing-lg;
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }
}

.category-title {
  @include flex-center-between;
  width: 100%;

  .category-info-left {
    @include flex-center;
    gap: $spacing-sm;
  }

  .category-icon {
    width: 32px;
    height: 32px;
    border-radius: $border-radius-sm;
    @include flex-center;
    color: $text-color-white;
    font-weight: $font-weight-semibold;
    font-size: $font-size-sm;
    @include smooth-transition(transform);

    &:hover {
      transform: scale(1.05);
    }
  }

  span {
    font-size: $font-size-base;
    font-weight: $font-weight-medium;
    color: $text-color-primary;
  }

  .card-actions {
    @include flex-center;
  }
}

.category-info {
  @include flex-column;
  gap: $spacing-base;
  padding: 0;

  // 统计信息区域
  .stats-section {
    @include flex-center;
    gap: $spacing-lg;
    margin-bottom: $spacing-xs;

    .stat-item {
      @include flex-column;
      align-items: center;
      gap: 4px;
      flex: 1;
      padding: $spacing-sm;
      border-radius: $border-radius-sm;
      background: $background-color-secondary;
      @include smooth-transition(all);

      &:hover {
        background: $background-color-dark;
        transform: translateY(-1px);
      }

      &.primary {
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        border: 1px solid #bae6fd;

        .stat-number {
          color: #0369a1;
        }
      }

      .stat-number {
        font-size: 24px;
        font-weight: $font-weight-bold;
        color: $text-color-primary;
        line-height: 1;
      }

      .stat-label {
        font-size: $font-size-xs;
        color: $text-color-secondary;
        font-weight: $font-weight-medium;
      }
    }
  }

  // 分隔线
  .divider {
    height: 1px;
    background: linear-gradient(
      90deg,
      transparent 0%,
      $border-color-light 50%,
      transparent 100%
    );
    margin: $spacing-xs 0;
  }

  // 详细信息区域
  .details-section {
    @include flex-column;
    gap: $spacing-xs;

    .detail-row {
      @include flex-center-between;
      font-size: $font-size-sm;

      .detail-label {
        color: $text-color-secondary;
        font-weight: $font-weight-medium;
      }

      .detail-value {
        color: $text-color-primary;
        font-weight: $font-weight-medium;
      }
    }
  }

  // 账号预览区域
  .preview-section {
    margin-top: $spacing-sm;

    .preview-label {
      font-size: $font-size-xs;
      color: $text-color-secondary;
      margin-bottom: $spacing-xs;
      font-weight: $font-weight-medium;
    }

    .account-previews {
      @include flex-center;
      gap: $spacing-xs;
      flex-wrap: wrap;

      .account-preview {
        width: 28px;
        height: 28px;
        border-radius: 50%;
        @include flex-center;
        font-size: $font-size-xs;
        font-weight: $font-weight-semibold;
        color: white;
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        @include smooth-transition(all);
        cursor: pointer;

        &:hover {
          transform: scale(1.1);
          box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }

        &.more {
          background: $background-color-tertiary;
          color: $text-color-secondary;
          border: 1px solid $border-color-light;
          font-size: 10px;

          &:hover {
            background: $background-color-quaternary;
            color: $text-color-primary;
          }
        }
      }
    }
  }
}

// 模态框样式
:deep(.arco-modal) {
  .arco-modal-header {
    border-bottom: 1px solid $border-color-light;

    .arco-modal-title {
      font-weight: $font-weight-semibold;
      color: $text-color-primary;
    }
  }

  .arco-modal-body {
    padding: $spacing-lg;
  }
}

.color-picker {
  width: 100%;

  .color-input-group {
    display: flex;
    gap: $spacing-sm;
    align-items: center;
    margin-bottom: $spacing-base;

    :deep(.arco-color-picker) {
      flex-shrink: 0;

      .arco-color-picker-trigger {
        @include smooth-transition(all);
        border-radius: $border-radius-sm;

        &:hover {
          transform: scale(1.05);
          box-shadow: $shadow-base;
        }
      }
    }

    .color-preview-suffix {
      width: 16px;
      height: 16px;
      border-radius: $border-radius-xs;
      border: 1px solid $border-color-light;
      @include smooth-transition(transform);

      &:hover {
        transform: scale(1.2);
      }
    }
  }

  .preset-colors-section {
    .section-title {
      font-size: $font-size-sm;
      font-weight: $font-weight-medium;
      color: $text-color-secondary;
      margin-bottom: $spacing-xs;
    }

    .preset-colors {
      display: grid;
      grid-template-columns: repeat(8, 1fr);
      gap: $spacing-xs;

      .color-option {
        width: 36px;
        height: 36px;
        border-radius: $border-radius-sm;
        cursor: pointer;
        @include smooth-transition(all);
        border: 2px solid transparent;
        position: relative;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

        &:hover {
          transform: scale(1.1);
          box-shadow: $shadow-base;
          border-color: rgba($primary-color, 0.3);
        }

        &.active {
          border-color: $primary-color;
          transform: scale(1.1);
          box-shadow: $shadow-base;

          &::after {
            content: "✓";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: $text-color-white;
            font-weight: $font-weight-bold;
            font-size: $font-size-sm;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
            background: rgba(0, 0, 0, 0.2);
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
  }
}

// 表单样式增强
:deep(.arco-form) {
  .arco-form-item {
    margin-bottom: $spacing-lg;

    .arco-form-item-label {
      font-weight: $font-weight-medium;
      color: $text-color-primary;
    }
  }

  .arco-input-wrapper {
    @include smooth-transition(border-color, box-shadow);

    &:focus-within {
      box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
    }
  }
}

// 响应式设计
@include mobile {
  .categories-content {
    padding: $spacing-base;
  }

  .categories-header {
    @include flex-column;
    gap: $spacing-base;
    align-items: stretch;

    .header-left {
      h1 {
        font-size: $font-size-xxl;
      }
    }

    .header-right {
      align-self: flex-start;
    }
  }

  .categories-grid {
    grid-template-columns: 1fr;

    .category-card {
      :deep(.arco-card-body) {
        padding: $spacing-base;
      }
    }
  }

  .color-picker {
    .color-input-group {
      flex-direction: column;
      align-items: stretch;
      gap: $spacing-xs;

      :deep(.arco-color-picker) {
        align-self: flex-start;
      }
    }

    .preset-colors-section {
      .preset-colors {
        grid-template-columns: repeat(6, 1fr);

        .color-option {
          width: 32px;
          height: 32px;
        }
      }
    }
  }
}

@include tablet {
  .categories-content {
    padding: $spacing-lg;
  }

  .categories-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
