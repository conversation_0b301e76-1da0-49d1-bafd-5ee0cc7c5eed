<template>
  <div class="categories">
    <AppLayout>
      <div class="categories-content">
        <div class="categories-header">
          <div class="header-left">
            <h1>分类管理</h1>
            <p>管理账号分类，让您的账号更有序</p>
          </div>
          <div class="header-right">
            <a-button type="primary" @click="showAddModal = true">
              <template #icon><icon-plus /></template>
              添加分类
            </a-button>
          </div>
        </div>

        <div class="categories-list">
          <div v-if="accountsStore.isLoading" class="loading-state">
            <a-spin size="large" />
            <p>加载中...</p>
          </div>

          <div v-else class="categories-grid">
            <a-card
              v-for="category in accountsStore.categories"
              :key="category.id"
              class="category-card"
              hoverable
            >
              <template #title>
                <div class="category-title">
                  <div
                    class="category-icon"
                    :style="{ backgroundColor: category.color }"
                  >
                    {{ category.name.charAt(0) }}
                  </div>
                  <span>{{ category.name }}</span>
                </div>
              </template>

              <template #extra>
                <a-dropdown
                  @select="(value: string) => onCategoryAction(value, category)"
                >
                  <a-button type="text" size="small">
                    <template #icon><icon-more /></template>
                  </a-button>
                  <template #content>
                    <a-doption value="edit">
                      <template #icon><icon-edit /></template>
                      编辑
                    </a-doption>
                    <a-doption value="delete">
                      <template #icon><icon-delete /></template>
                      删除
                    </a-doption>
                  </template>
                </a-dropdown>
              </template>

              <div class="category-info">
                <div class="account-count">
                  <icon-user />
                  <span>{{ getCategoryAccountCount(category.id) }} 个账号</span>
                </div>

                <div class="category-color">
                  <span>颜色：</span>
                  <div
                    class="color-preview"
                    :style="{ backgroundColor: category.color }"
                  ></div>
                  <span>{{ category.color }}</span>
                </div>

                <div class="create-time">
                  <span>创建于 {{ formatDate(category.created_at) }}</span>
                </div>
              </div>
            </a-card>
          </div>
        </div>

        <!-- 添加/编辑分类弹窗 -->
        <a-modal
          v-model:visible="showAddModal"
          :title="editingCategory ? '编辑分类' : '添加分类'"
          :ok-loading="saving"
          @ok="handleSaveCategory"
          @cancel="handleCancelEdit"
        >
          <a-form
            ref="categoryFormRef"
            :model="categoryForm"
            :rules="categoryRules"
            layout="vertical"
          >
            <a-form-item field="name" label="分类名称" required>
              <a-input
                v-model="categoryForm.name"
                placeholder="输入分类名称"
                size="large"
              />
            </a-form-item>

            <a-form-item field="color" label="分类颜色">
              <div class="color-picker">
                <!-- 颜色选择器和输入框组合 -->
                <div class="color-input-group">
                  <a-color-picker
                    v-model="categoryForm.color"
                    :preset-colors="presetColors"
                    show-text
                    show-history
                    size="large"
                    @change="onColorChange"
                  />
                  <a-input
                    v-model="categoryForm.color"
                    placeholder="#1890ff"
                    size="large"
                    style="flex: 1"
                    @change="validateColor"
                  >
                    <template #suffix>
                      <div
                        class="color-preview-suffix"
                        :style="{ backgroundColor: categoryForm.color }"
                      ></div>
                    </template>
                  </a-input>
                </div>

                <!-- 预设颜色快速选择 -->
                <div class="preset-colors-section">
                  <div class="section-title">常用颜色</div>
                  <div class="preset-colors">
                    <div
                      v-for="color in presetColors"
                      :key="color"
                      class="color-option"
                      :class="{
                        active:
                          categoryForm.color.toLowerCase() ===
                          color.toLowerCase(),
                      }"
                      :style="{ backgroundColor: color }"
                      @click="selectPresetColor(color)"
                      :title="color"
                    ></div>
                  </div>
                </div>
              </div>
            </a-form-item>
          </a-form>
        </a-modal>
      </div>
    </AppLayout>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { Message, Modal } from "@arco-design/web-vue";
import {
  IconPlus,
  IconMore,
  IconEdit,
  IconDelete,
  IconUser,
} from "@arco-design/web-vue/es/icon";
import AppLayout from "../components/AppLayout.vue";
import { useAccountsStore } from "../stores/accounts";
import type {
  Category,
  CreateCategoryRequest,
  UpdateCategoryRequest,
} from "../stores/accounts";

const accountsStore = useAccountsStore();

const showAddModal = ref(false);
const editingCategory = ref<Category | null>(null);
const categoryFormRef = ref();
const saving = ref(false);

const categoryForm = reactive({
  name: "",
  color: "#1890ff",
});

const categoryRules = {
  name: [
    { required: true, message: "请输入分类名称" },
    { minLength: 1, maxLength: 20, message: "分类名称长度为1-20个字符" },
    {
      validator: (value: string, callback: (error?: string) => void) => {
        if (!value || value.trim() === "") {
          callback("分类名称不能为空或只包含空格");
          return;
        }

        // 检查是否与现有分类重名（编辑时排除当前分类）
        const existingCategory = accountsStore.categories.find(
          (cat) =>
            cat.name.toLowerCase() === value.trim().toLowerCase() &&
            (!editingCategory.value || cat.id !== editingCategory.value.id)
        );

        if (existingCategory) {
          callback("分类名称已存在");
          return;
        }

        callback();
      },
    },
  ],
};

const presetColors = [
  // 主色调
  "#1890ff", // 蓝色
  "#52c41a", // 绿色
  "#faad14", // 金色
  "#f5222d", // 红色
  "#722ed1", // 紫色
  "#fa8c16", // 橙色

  // 扩展色调
  "#13c2c2", // 青色
  "#eb2f96", // 粉色
  "#2f54eb", // 靛蓝
  "#a0d911", // 青绿
  "#ff7a45", // 橙红
  "#ffc53d", // 柠檬黄

  // 深色调
  "#001529", // 深蓝
  "#002329", // 深青
  "#391085", // 深紫
  "#820014", // 深红
  "#ad4e00", // 深橙
  "#613400", // 深黄

  // 浅色调
  "#e6f7ff", // 浅蓝
  "#f6ffed", // 浅绿
  "#fffbe6", // 浅黄
  "#fff2e8", // 浅橙
  "#f9f0ff", // 浅紫
  "#fff0f6", // 浅粉
];

onMounted(async () => {
  await loadData();
});

const loadData = async () => {
  try {
    await Promise.all([
      accountsStore.loadCategories(),
      accountsStore.loadAccounts(),
    ]);
  } catch (error) {
    Message.error("加载数据失败");
    console.error(error);
  }
};

const getCategoryAccountCount = (categoryId: string) => {
  return accountsStore.accounts.filter(
    (account) => account.category_id === categoryId
  ).length;
};

const onCategoryAction = (action: string, category: Category) => {
  if (action === "edit") {
    editingCategory.value = category;
    categoryForm.name = category.name;
    categoryForm.color = category.color || "#1890ff";
    showAddModal.value = true;
  } else if (action === "delete") {
    handleDeleteCategory(category);
  }
};

const handleSaveCategory = async () => {
  // 阻止模态框默认关闭行为，只有验证成功才关闭
  return new Promise<void>((resolve, reject) => {
    const doSave = async () => {
      try {
        saving.value = true;

        // 先检查表单引用是否存在
        if (!categoryFormRef.value) {
          Message.error("表单初始化失败，请重试");
          reject(new Error("表单初始化失败"));
          return;
        }

        // 手动检查分类名称是否为空
        if (!categoryForm.name || categoryForm.name.trim() === "") {
          Message.warning("请输入分类名称");
          reject(new Error("分类名称为空"));
          return;
        }

        // 执行表单验证
        const validationResult = await categoryFormRef.value.validate();
        console.log("表单验证结果:", validationResult);

        // Arco Design 的 validate() 方法：
        // - 验证成功时返回 undefined
        // - 验证失败时返回错误对象
        if (validationResult !== undefined) {
          console.log("表单验证失败:", validationResult);
          Message.warning("请检查表单中的必填项");
          reject(new Error("表单验证失败"));
          return;
        }

        if (editingCategory.value) {
          // 编辑分类
          const updateRequest: UpdateCategoryRequest = {
            id: editingCategory.value.id,
            name: categoryForm.name.trim(),
            color: categoryForm.color,
          };
          await accountsStore.updateCategory(updateRequest);
          Message.success("分类更新成功");
        } else {
          // 创建分类
          const createRequest: CreateCategoryRequest = {
            name: categoryForm.name.trim(),
            color: categoryForm.color,
          };
          await accountsStore.createCategory(createRequest);
          Message.success("分类添加成功");
        }

        handleCancelEdit();
        resolve(); // 验证成功，允许关闭模态框
      } catch (error) {
        console.error("保存分类失败:", error);

        // 检查是否是后端验证错误，显示具体错误信息
        const errorMessage = String(error);
        if (errorMessage.includes("分类名称不能为空")) {
          Message.error("分类名称不能为空");
        } else if (errorMessage.includes("分类名称已存在")) {
          Message.error("分类名称已存在，请使用其他名称");
        } else if (errorMessage.includes("分类名称长度")) {
          Message.error("分类名称长度不能超过20个字符");
        } else {
          Message.error(editingCategory.value ? "更新失败" : "添加失败");
        }

        reject(error); // 验证失败，阻止关闭模态框
      } finally {
        saving.value = false;
      }
    };

    doSave();
  });
};

const handleCancelEdit = () => {
  showAddModal.value = false;
  editingCategory.value = null;
  categoryForm.name = "";
  categoryForm.color = "#1890ff";
  saving.value = false; // 重置保存状态
};

const handleDeleteCategory = (category: Category) => {
  const accountCount = getCategoryAccountCount(category.id);

  if (accountCount > 0) {
    Message.warning(`该分类下还有 ${accountCount} 个账号，无法删除`);
    return;
  }

  Modal.confirm({
    title: "确认删除",
    content: `确定要删除分类 "${category.name}" 吗？此操作不可恢复。`,
    onOk: async () => {
      try {
        await accountsStore.deleteCategory(category.id);
        Message.success("分类删除成功");
      } catch (error) {
        Message.error("删除失败");
        console.error(error);
      }
    },
  });
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString("zh-CN");
};

// 颜色相关方法
const onColorChange = (color: string) => {
  categoryForm.color = color;
};

const selectPresetColor = (color: string) => {
  categoryForm.color = color;
};

const validateColor = () => {
  // 验证颜色格式是否正确
  const colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
  if (!colorRegex.test(categoryForm.color)) {
    // 如果格式不正确，重置为默认颜色
    categoryForm.color = "#1890ff";
    Message.warning("颜色格式不正确，已重置为默认颜色");
  }
};
</script>

<style lang="scss" scoped>
.categories {
  height: 100vh;
}

.categories-content {
  padding: $spacing-lg;
}

.categories-header {
  @include flex-center-between;
  align-items: flex-start;
  margin-bottom: $spacing-xl;

  .header-left {
    h1 {
      margin: 0 0 $spacing-xs 0;
      font-size: $font-size-xxxl;
      font-weight: $font-weight-semibold;
      color: $text-color-primary;
      @include smooth-transition(color);
    }

    p {
      margin: 0;
      color: $text-color-secondary;
      font-size: $font-size-base;
    }
  }

  .header-right {
    :deep(.arco-btn) {
      @include smooth-transition(all);
      font-weight: $font-weight-medium;

      &:hover {
        transform: translateY(-1px);
        box-shadow: $shadow-lg;
      }
    }
  }
}

.loading-state {
  @include flex-column-center;
  padding: 80px 20px;
  text-align: center;

  p {
    margin-top: $spacing-base;
    color: $text-color-secondary;
    font-size: $font-size-base;
  }
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: $spacing-base;

  .category-card {
    @include smooth-transition(all);
    @include card-shadow;
    border-radius: $border-radius-lg;

    &:hover {
      @include card-hover;
      transform: translateY(-4px);
      box-shadow: $shadow-xl;
    }

    // 自定义卡片样式
    :deep(.arco-card-header) {
      border-bottom: 1px solid $border-color-light;

      .arco-card-header-title {
        font-weight: $font-weight-semibold;
      }
    }

    :deep(.arco-card-body) {
      padding: $spacing-lg;
    }
  }
}

.category-title {
  @include flex-center;
  gap: $spacing-sm;

  .category-icon {
    width: 32px;
    height: 32px;
    border-radius: $border-radius-sm;
    @include flex-center;
    color: $text-color-white;
    font-weight: $font-weight-semibold;
    font-size: $font-size-sm;
    @include smooth-transition(transform);

    &:hover {
      transform: scale(1.05);
    }
  }

  span {
    font-size: $font-size-base;
    font-weight: $font-weight-medium;
    color: $text-color-primary;
  }
}

.category-info {
  @include flex-column;
  gap: $spacing-sm;

  .account-count {
    @include flex-center;
    gap: $spacing-xs;
    color: $text-color-secondary;
    font-size: $font-size-sm;
    @include smooth-transition(color);

    &:hover {
      color: $primary-color;
    }

    span {
      font-weight: $font-weight-medium;
    }
  }

  .category-color {
    @include flex-center;
    gap: $spacing-xs;
    font-size: $font-size-sm;
    color: $text-color-secondary;

    .color-preview {
      width: 16px;
      height: 16px;
      border-radius: $border-radius-xs;
      border: 1px solid $border-color-light;
      @include smooth-transition(transform);

      &:hover {
        transform: scale(1.2);
      }
    }

    span:last-child {
      font-family: $font-family-code;
      background: $background-color-secondary;
      padding: 2px 6px;
      border-radius: $border-radius-xs;
      font-size: $font-size-xs;
    }
  }

  .create-time {
    font-size: $font-size-xs;
    color: $text-color-disabled;
  }
}

// 模态框样式
:deep(.arco-modal) {
  .arco-modal-header {
    border-bottom: 1px solid $border-color-light;

    .arco-modal-title {
      font-weight: $font-weight-semibold;
      color: $text-color-primary;
    }
  }

  .arco-modal-body {
    padding: $spacing-lg;
  }
}

.color-picker {
  width: 100%;

  .color-input-group {
    display: flex;
    gap: $spacing-sm;
    align-items: center;
    margin-bottom: $spacing-base;

    :deep(.arco-color-picker) {
      flex-shrink: 0;

      .arco-color-picker-trigger {
        @include smooth-transition(all);
        border-radius: $border-radius-sm;

        &:hover {
          transform: scale(1.05);
          box-shadow: $shadow-base;
        }
      }
    }

    .color-preview-suffix {
      width: 16px;
      height: 16px;
      border-radius: $border-radius-xs;
      border: 1px solid $border-color-light;
      @include smooth-transition(transform);

      &:hover {
        transform: scale(1.2);
      }
    }
  }

  .preset-colors-section {
    .section-title {
      font-size: $font-size-sm;
      font-weight: $font-weight-medium;
      color: $text-color-secondary;
      margin-bottom: $spacing-xs;
    }

    .preset-colors {
      display: grid;
      grid-template-columns: repeat(8, 1fr);
      gap: $spacing-xs;

      .color-option {
        width: 36px;
        height: 36px;
        border-radius: $border-radius-sm;
        cursor: pointer;
        @include smooth-transition(all);
        border: 2px solid transparent;
        position: relative;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

        &:hover {
          transform: scale(1.1);
          box-shadow: $shadow-base;
          border-color: rgba($primary-color, 0.3);
        }

        &.active {
          border-color: $primary-color;
          transform: scale(1.1);
          box-shadow: $shadow-base;

          &::after {
            content: "✓";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: $text-color-white;
            font-weight: $font-weight-bold;
            font-size: $font-size-sm;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
            background: rgba(0, 0, 0, 0.2);
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
  }
}

// 表单样式增强
:deep(.arco-form) {
  .arco-form-item {
    margin-bottom: $spacing-lg;

    .arco-form-item-label {
      font-weight: $font-weight-medium;
      color: $text-color-primary;
    }
  }

  .arco-input-wrapper {
    @include smooth-transition(border-color, box-shadow);

    &:focus-within {
      box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
    }
  }
}

// 响应式设计
@include mobile {
  .categories-content {
    padding: $spacing-base;
  }

  .categories-header {
    @include flex-column;
    gap: $spacing-base;
    align-items: stretch;

    .header-left {
      h1 {
        font-size: $font-size-xxl;
      }
    }

    .header-right {
      align-self: flex-start;
    }
  }

  .categories-grid {
    grid-template-columns: 1fr;

    .category-card {
      :deep(.arco-card-body) {
        padding: $spacing-base;
      }
    }
  }

  .color-picker {
    .color-input-group {
      flex-direction: column;
      align-items: stretch;
      gap: $spacing-xs;

      :deep(.arco-color-picker) {
        align-self: flex-start;
      }
    }

    .preset-colors-section {
      .preset-colors {
        grid-template-columns: repeat(6, 1fr);

        .color-option {
          width: 32px;
          height: 32px;
        }
      }
    }
  }
}

@include tablet {
  .categories-content {
    padding: $spacing-lg;
  }

  .categories-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
