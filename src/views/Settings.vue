<template>
  <div class="settings">
    <AppLayout>
      <div class="settings-content">
        <div class="settings-header">
          <h1>设置</h1>
          <p>应用设置和安全配置</p>
        </div>

        <div class="settings-sections">
          <a-card title="安全设置" class="settings-card">
            <div class="setting-item">
              <div class="setting-info">
                <h3>修改主密码</h3>
                <p>更改用于解锁应用的主密码</p>
              </div>
              <div class="setting-action">
                <a-button @click="showChangePasswordModal = true">
                  修改密码
                </a-button>
              </div>
            </div>

            <a-divider />

            <div class="setting-item">
              <div class="setting-info">
                <h3>自动锁定</h3>
                <p>设置应用闲置多长时间后自动锁定</p>
              </div>
              <div class="setting-action">
                <a-select v-model="autoLockTime" style="width: 120px">
                  <a-option :value="0">从不</a-option>
                  <a-option :value="5">5分钟</a-option>
                  <a-option :value="10">10分钟</a-option>
                  <a-option :value="30">30分钟</a-option>
                  <a-option :value="60">1小时</a-option>
                </a-select>
              </div>
            </div>
          </a-card>

          <a-card title="数据管理" class="settings-card">
            <div class="setting-item">
              <div class="setting-info">
                <h3>数据备份</h3>
                <p>导出账号数据到本地文件</p>
              </div>
              <div class="setting-action">
                <a-button @click="exportData"> 导出数据 </a-button>
              </div>
            </div>

            <a-divider />

            <div class="setting-item">
              <div class="setting-info">
                <h3>数据导入</h3>
                <p>从备份文件(.json)或Excel文件(.xls/.xlsx)导入账号数据</p>
              </div>
              <div class="setting-action">
                <a-space>
                  <a-button @click="downloadTemplate" type="outline">
                    下载模板
                  </a-button>
                  <a-button @click="importData" type="primary">
                    导入数据
                  </a-button>
                </a-space>
              </div>
            </div>

            <a-divider />

            <div class="setting-item">
              <div class="setting-info">
                <h3>清空数据</h3>
                <p>删除所有账号数据，此操作不可恢复</p>
              </div>
              <div class="setting-action">
                <a-button status="danger" @click="clearAllData">
                  清空数据
                </a-button>
              </div>
            </div>
          </a-card>

          <a-card title="应用信息" class="settings-card">
            <div class="setting-item">
              <div class="setting-info">
                <h3>版本信息</h3>
                <p>当前版本：v1.0.0</p>
              </div>
            </div>

            <a-divider />

            <div class="setting-item">
              <div class="setting-info">
                <h3>数据存储位置</h3>
                <p>{{ dataPath }}</p>
              </div>
              <div class="setting-action">
                <a-button @click="openDataFolder"> 打开文件夹 </a-button>
              </div>
            </div>

            <a-divider />

            <div class="setting-item">
              <div class="setting-info">
                <h3>关于应用</h3>
                <p>本地账号管理系统 - 安全、简单、高效</p>
              </div>
            </div>
          </a-card>
        </div>

        <!-- 修改密码弹窗 -->
        <a-modal
          v-model:visible="showChangePasswordModal"
          title="修改主密码"
          @ok="handleChangePassword"
          @cancel="cancelChangePassword"
        >
          <a-form
            ref="passwordFormRef"
            :model="passwordForm"
            :rules="passwordRules"
            layout="vertical"
          >
            <a-form-item field="currentPassword" label="当前密码" required>
              <a-input-password
                v-model="passwordForm.currentPassword"
                placeholder="输入当前密码"
                size="large"
              />
            </a-form-item>

            <a-form-item field="newPassword" label="新密码" required>
              <a-input-password
                v-model="passwordForm.newPassword"
                placeholder="输入新密码"
                size="large"
              />
            </a-form-item>

            <a-form-item field="confirmPassword" label="确认新密码" required>
              <a-input-password
                v-model="passwordForm.confirmPassword"
                placeholder="再次输入新密码"
                size="large"
              />
            </a-form-item>
          </a-form>
        </a-modal>
      </div>
    </AppLayout>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { Modal, Message } from "@arco-design/web-vue";
import AppLayout from "../components/AppLayout.vue";
import { useAccountsStore } from "../stores/accounts";
import { showInfo, showError, showSuccess, showLoading } from "../utils";
import * as XLSX from "xlsx";

const accountsStore = useAccountsStore();

const showChangePasswordModal = ref(false);
const passwordFormRef = ref();
const autoLockTime = ref(10);
const dataPath = ref("加载中...");

const passwordForm = reactive({
  currentPassword: "",
  newPassword: "",
  confirmPassword: "",
});

const passwordRules = {
  currentPassword: [{ required: true, message: "请输入当前密码" }],
  newPassword: [
    { required: true, message: "请输入新密码" },
    { minLength: 6, message: "密码长度至少6位" },
  ],
  confirmPassword: [
    { required: true, message: "请确认新密码" },
    {
      validator: (value: string, callback: Function) => {
        if (value !== passwordForm.newPassword) {
          callback("两次输入的密码不一致");
        } else {
          callback();
        }
      },
    },
  ],
};

onMounted(async () => {
  console.log("⚙️ [Settings] Page mounted, loading settings...");

  // 加载设置和数据路径
  try {
    const path = await accountsStore.getDatabasePath();
    dataPath.value = path;
    console.log("⚙️ [Settings] Settings loaded successfully");
  } catch (error) {
    console.error("Failed to get database path:", error);
    dataPath.value = "无法获取路径";
  }
});

const handleChangePassword = async () => {
  try {
    const valid = await passwordFormRef.value?.validate();
    if (!valid) return;

    // TODO: 实现修改密码功能
    showInfo("修改密码功能暂未实现");

    cancelChangePassword();
  } catch (error) {
    showError("修改密码失败");
    console.error(error);
  }
};

const cancelChangePassword = () => {
  showChangePasswordModal.value = false;
  passwordForm.currentPassword = "";
  passwordForm.newPassword = "";
  passwordForm.confirmPassword = "";
};

// Excel 文件转换为 JSON 的函数
const convertExcelToJson = async (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: "array" });

        // 获取第一个工作表
        const firstSheetName = workbook.SheetNames[0];
        if (!firstSheetName) {
          reject(new Error("Excel 文件中没有找到工作表"));
          return;
        }

        const worksheet = workbook.Sheets[firstSheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

        if (jsonData.length === 0) {
          reject(new Error("Excel 文件中没有数据"));
          return;
        }

        // 转换 Excel 数据为应用所需的格式
        const convertedData = convertExcelDataToAppFormat(jsonData as any[][]);

        resolve(JSON.stringify(convertedData));
      } catch (error) {
        reject(new Error(`解析 Excel 文件失败: ${error}`));
      }
    };

    reader.onerror = () => {
      reject(new Error("读取文件失败"));
    };

    reader.readAsArrayBuffer(file);
  });
};

// 将 Excel 数据转换为应用格式
const convertExcelDataToAppFormat = (excelData: any[][]): any => {
  if (excelData.length < 2) {
    throw new Error("Excel 文件至少需要包含标题行和一行数据");
  }

  const headers = excelData[0] as string[];
  const rows = excelData.slice(1);

  // 创建列名映射（支持中英文列名）
  const columnMap: { [key: string]: string } = {
    平台: "platform",
    平台名称: "platform",
    platform: "platform",
    用户名: "username",
    账号: "username",
    username: "username",
    account: "username",
    密码: "password",
    password: "password",
    邮箱: "email",
    电子邮箱: "email",
    email: "email",
    手机: "phone",
    手机号: "phone",
    电话: "phone",
    phone: "phone",
    mobile: "phone",
    网址: "website_url",
    网站: "website_url",
    网站地址: "website_url",
    平台地址: "website_url",
    url: "website_url",
    website: "website_url",
    website_url: "website_url",
    分类: "category",
    category: "category",
    备注: "notes",
    说明: "notes",
    notes: "notes",
    remark: "notes",
    remarks: "notes",
    关联: "notes",
  };

  // 映射列索引
  const columnIndexMap: { [key: string]: number } = {};
  headers.forEach((header, index) => {
    const headerStr = header?.toString().trim();
    if (!headerStr) return;

    // 尝试原始列名和小写列名
    const mappedField =
      columnMap[headerStr] || columnMap[headerStr.toLowerCase()];
    if (mappedField) {
      columnIndexMap[mappedField] = index;
    }
  });

  // 调试信息
  console.log("Excel headers:", headers);
  console.log("Column index map:", columnIndexMap);

  // 检查必需字段
  if (columnIndexMap.platform === undefined) {
    const availableColumns = headers
      .filter((h) => h?.toString().trim())
      .join(", ");
    throw new Error(
      `Excel 文件必须包含"平台"列。当前列名：${availableColumns}`
    );
  }

  if (columnIndexMap.username === undefined) {
    const availableColumns = headers
      .filter((h) => h?.toString().trim())
      .join(", ");
    throw new Error(
      `Excel 文件必须包含"用户名"或"账号"列。当前列名：${availableColumns}`
    );
  }

  // 转换数据
  console.log("Total rows to process:", rows.length);
  console.log("Sample rows:", rows.slice(0, 3));

  const filteredRows = rows.filter(
    (row) =>
      row &&
      row.length > 0 &&
      row[columnIndexMap.platform] &&
      row[columnIndexMap.username]
  );

  console.log("Filtered rows count:", filteredRows.length);
  console.log("Platform column index:", columnIndexMap.platform);
  console.log("Username column index:", columnIndexMap.username);

  const accounts = filteredRows.map((row) => {
    const account: any = {
      platform: row[columnIndexMap.platform]?.toString().trim() || "",
      username: row[columnIndexMap.username]?.toString().trim() || "",
      password: row[columnIndexMap.password]?.toString().trim() || "",
      email: row[columnIndexMap.email]?.toString().trim() || null,
      phone: row[columnIndexMap.phone]?.toString().trim() || null,
      website_url: row[columnIndexMap.website_url]?.toString().trim() || null,
      notes: row[columnIndexMap.notes]?.toString().trim() || null,
    };

    // 清理空值
    Object.keys(account).forEach((key) => {
      if (
        account[key] === "" ||
        account[key] === null ||
        account[key] === undefined
      ) {
        account[key] = null;
      }
    });

    return account;
  });

  if (accounts.length === 0) {
    throw new Error("没有找到有效的账号数据");
  }

  // 返回应用期望的格式
  return {
    version: "1.0",
    export_time: new Date().toISOString(),
    accounts: accounts,
    categories: [], // Excel 导入时不包含分类
  };
};

const exportData = async () => {
  console.log("=== 开始导出数据 ===");
  try {
    // 显示加载状态
    const loadingMessage = showLoading("正在导出数据...");

    console.log("调用 accountsStore.exportDataToFile()...");
    // 使用新的导出到文件方法
    const result = await accountsStore.exportDataToFile();
    console.log("导出结果:", result);

    // 关闭加载提示并显示成功消息
    loadingMessage.close();
    showSuccess(result);
    console.log("=== 导出数据完成 ===");
  } catch (error) {
    console.error("=== 导出数据失败 ===");
    console.error("错误详情:", error);
    console.error("错误类型:", typeof error);
    console.error("错误堆栈:", (error as Error)?.stack);
    console.error("错误消息:", (error as any)?.message || String(error));

    showError("导出数据失败：" + (error as any)?.message || String(error));
  }
};

const importData = () => {
  console.log("📥 [Settings] Starting data import process...");

  // 创建文件输入元素
  const input = document.createElement("input");
  input.type = "file";
  input.accept = ".json,.xls,.xlsx";

  input.onchange = async (event) => {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (!file) {
      console.log("📥 [Settings] No file selected, cancelling import");
      return;
    }

    console.log("📥 [Settings] File selected:", {
      name: file.name,
      size: file.size,
      type: file.type,
      lastModified: new Date(file.lastModified).toISOString(),
    });

    try {
      // 显示加载状态
      console.log("📥 [Settings] Showing loading message...");
      const loadingMessage = Message.loading("正在导入数据...");

      let fileContent: string;

      // 根据文件类型处理
      if (file.name.endsWith(".json")) {
        console.log("📥 [Settings] Processing JSON file...");
        // JSON 文件直接读取文本内容
        fileContent = await file.text();
        console.log(
          "📥 [Settings] JSON file read successfully, length:",
          fileContent.length
        );
      } else if (file.name.endsWith(".xls") || file.name.endsWith(".xlsx")) {
        console.log("📥 [Settings] Processing Excel file...");
        // Excel 文件需要转换为 JSON
        fileContent = await convertExcelToJson(file);
        console.log(
          "📥 [Settings] Excel file converted to JSON successfully, length:",
          fileContent.length
        );
      } else {
        throw new Error("不支持的文件格式，请选择 .json、.xls 或 .xlsx 文件");
      }

      console.log("📥 [Settings] Calling accountsStore.importData()...");
      console.log(
        "📥 [Settings] File content preview:",
        fileContent.substring(0, 200) + "..."
      );

      // 调用导入方法
      const result = await accountsStore.importData(fileContent);

      console.log("📥 [Settings] Import completed successfully:", result);

      // 关闭加载提示并显示成功消息
      loadingMessage.close();
      Message.success(
        `导入成功：成功导入 ${result.success} 个账号，失败 ${result.failed} 个`
      );

      console.log("📥 [Settings] Import process completed successfully");
    } catch (error) {
      console.error("📥 [Settings] Import failed:");
      console.error("  Error type:", typeof error);
      console.error(
        "  Error message:",
        (error as any)?.message || String(error)
      );
      console.error("  Error stack:", (error as Error)?.stack);
      console.error("  Full error object:", error);

      Message.error(
        "导入数据失败：" + (error as any)?.message || String(error)
      );
    }
  };

  // 触发文件选择
  console.log("📥 [Settings] Triggering file selection dialog...");
  input.click();
};

const clearAllData = () => {
  Modal.confirm({
    title: "确认清空数据",
    content: "此操作将删除所有账号数据和自定义分类，且不可恢复。确定要继续吗？",
    okText: "确认清空",
    cancelText: "取消",
    okButtonProps: {
      status: "danger",
    },
    onOk: async () => {
      try {
        await accountsStore.clearAllData();
        Message.success("数据清空成功");
      } catch (error) {
        console.error("Failed to clear data:", error);
        Message.error(
          "清空数据失败：" + (error as any)?.message || String(error)
        );
      }
    },
  });
};

const downloadTemplate = async () => {
  console.log("=== 开始下载模板 ===");
  try {
    // 显示加载状态
    const loadingMessage = Message.loading("正在生成模板文件...");

    console.log("调用 accountsStore.downloadTemplate()...");
    // 调用后端下载模板方法
    const result = await accountsStore.downloadTemplate();
    console.log("下载结果:", result);

    // 关闭加载提示并显示成功消息
    loadingMessage.close();
    Message.success(result);
    console.log("=== 下载模板完成 ===");
  } catch (error) {
    console.error("=== 下载模板失败 ===");
    console.error("错误详情:", error);
    console.error("错误类型:", typeof error);
    console.error("错误堆栈:", (error as Error)?.stack);
    console.error("错误消息:", (error as any)?.message || String(error));

    Message.error("下载模板失败：" + (error as any)?.message || String(error));
  }
};

const openDataFolder = async () => {
  try {
    await accountsStore.openDataFolder();
    Message.success("已打开数据文件夹");
  } catch (error) {
    console.error("Failed to open data folder:", error);
    Message.error("打开文件夹失败");
  }
};
</script>

<style lang="scss" scoped>
.settings {
  height: 100vh;
}

.settings-content {
  padding: $spacing-lg;
  max-width: 800px;
  margin: 0 auto;
}

.settings-header {
  margin-bottom: $spacing-xl;
  text-align: center;

  h1 {
    margin: 0 0 $spacing-xs 0;
    font-size: $font-size-xxxl;
    font-weight: $font-weight-semibold;
    color: $text-color-primary;
    @include smooth-transition(color);
  }

  p {
    margin: 0;
    color: $text-color-secondary;
    font-size: $font-size-base;
  }
}

.settings-sections {
  @include flex-column;
  gap: $spacing-lg;

  .settings-card {
    @include card-shadow;
    background: $background-color-primary;
    border-radius: $border-radius-lg;
    @include smooth-transition(transform, box-shadow);

    &:hover {
      transform: translateY(-2px);
      box-shadow: $shadow-lg;
    }

    // 自定义卡片样式
    :deep(.arco-card-header) {
      border-bottom: 1px solid $border-color-light;
      padding-left: $spacing-lg; // 与卡片内容左边距保持一致

      .arco-card-header-title {
        font-weight: $font-weight-semibold;
        color: $text-color-primary;
        font-size: $font-size-lg;
      }
    }

    :deep(.arco-card-body) {
      padding: $spacing-lg;
    }

    // 分割线样式
    :deep(.arco-divider) {
      margin: $spacing-base 0;
      border-color: $border-color-light;
    }
  }
}

.setting-item {
  @include flex-center-between;
  padding: $spacing-base 0;
  transition: background-color 0.4s cubic-bezier(0.4, 0, 0.2, 1),
    padding 0.4s cubic-bezier(0.4, 0, 0.2, 1); // 与蓝色条保持相同的动画时间

  &:hover {
    background: $background-color-secondary;
    border-radius: $border-radius-base;
    padding-left: $spacing-lg; // 增加左侧内边距，为蓝色条留出更多空间
    padding-right: $spacing-sm;
  }

  .setting-info {
    flex: 1;

    h3 {
      margin: 0 0 $spacing-xs 0;
      font-size: $font-size-base;
      font-weight: $font-weight-semibold;
      color: $text-color-primary;
      @include smooth-transition(color);
    }

    p {
      margin: 0;
      font-size: $font-size-sm;
      color: $text-color-secondary;
      line-height: $line-height-base;

      // 特殊处理路径文本
      &:contains("~/") {
        font-family: $font-family-code;
        background: $background-color-secondary;
        padding: 2px 6px;
        border-radius: $border-radius-xs;
        font-size: $font-size-xs;
      }
    }
  }

  .setting-action {
    flex-shrink: 0;
    margin-left: $spacing-base;

    // 按钮样式增强
    :deep(.arco-btn) {
      @include smooth-transition(all);
      font-weight: $font-weight-medium;

      &:hover {
        transform: translateY(-1px);
      }

      &.arco-btn-primary {
        &:hover {
          box-shadow: $shadow-base;
        }
      }

      &.arco-btn-status-danger {
        &:hover {
          box-shadow: 0 2px 8px rgba($error-color, 0.3);
        }
      }
    }

    // 选择器样式
    :deep(.arco-select) {
      .arco-select-view {
        @include smooth-transition(border-color, box-shadow);

        &:focus-within {
          box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
        }
      }
    }
  }
}

// 模态框样式增强
:deep(.arco-modal) {
  .arco-modal-header {
    border-bottom: 1px solid $border-color-light;

    .arco-modal-title {
      font-weight: $font-weight-semibold;
      color: $text-color-primary;
    }
  }

  .arco-modal-body {
    padding: $spacing-lg;
  }

  .arco-modal-footer {
    border-top: 1px solid $border-color-light;
    padding: $spacing-base $spacing-lg;

    .arco-btn {
      @include smooth-transition(all);
      font-weight: $font-weight-medium;

      &:hover {
        transform: translateY(-1px);
      }
    }
  }
}

// 表单样式增强
:deep(.arco-form) {
  .arco-form-item {
    margin-bottom: $spacing-lg;

    &:last-child {
      margin-bottom: 0;
    }

    .arco-form-item-label {
      font-weight: $font-weight-medium;
      color: $text-color-primary;
    }
  }

  .arco-input-wrapper {
    @include smooth-transition(border-color, box-shadow);

    &:focus-within {
      box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
    }
  }
}

// 响应式设计
@include mobile {
  .settings-content {
    padding: $spacing-base;
  }

  .settings-header {
    h1 {
      font-size: $font-size-xxl;
    }
  }

  .settings-sections {
    gap: $spacing-base;

    .settings-card {
      :deep(.arco-card-body) {
        padding: $spacing-base;
      }
    }
  }

  .setting-item {
    @include flex-column;
    align-items: flex-start;
    gap: $spacing-sm;

    &:hover {
      padding: $spacing-sm;
    }

    .setting-info {
      width: 100%;

      h3 {
        font-size: $font-size-sm;
      }

      p {
        font-size: $font-size-xs;
      }
    }

    .setting-action {
      width: 100%;
      margin-left: 0;

      :deep(.arco-btn) {
        width: 100%;
      }

      :deep(.arco-select) {
        width: 100%;
      }
    }
  }
}

@include tablet {
  .settings-content {
    padding: $spacing-lg;
  }
}

// 特殊动画效果
.setting-item {
  position: relative;

  &::before {
    content: "";
    position: absolute;
    left: $spacing-xs; // 调整蓝色条位置，与内容保持合适间距
    top: 0;
    bottom: 0;
    width: 3px;
    background: $primary-color;
    border-radius: $border-radius-xs;
    transform: scaleY(0);
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1); // 使用更慢的动画和缓动函数
  }

  &:hover::before {
    transform: scaleY(1);
  }
}

// 版本信息特殊样式
.setting-item:has(.setting-info h3:contains("版本信息")) {
  .setting-info p {
    font-family: $font-family-code;
    background: $primary-color-light;
    color: $primary-color-dark;
    padding: 4px 8px;
    border-radius: $border-radius-base;
    font-weight: $font-weight-medium;
    display: inline-block;
  }
}
</style>
