<template>
  <div class="accounts">
    <AppLayout>
      <div class="accounts-content">
        <div class="accounts-header">
          <div class="header-left">
            <h1>账号管理</h1>
          </div>
          <div class="header-right">
            <el-button type="primary" @click="handleAddAccount">
              <el-icon><Plus /></el-icon>
              添加账号
            </el-button>
          </div>
        </div>

        <div class="accounts-toolbar">
          <div class="toolbar-card">
            <div class="toolbar-left">
              <div class="search-input-wrapper">
                <el-input
                  v-model="searchKeyword"
                  placeholder="搜索平台名称、用户名或邮箱..."
                  size="large"
                  @keyup.enter="handleSearch"
                  @clear="handleClearSearch"
                  clearable
                  class="search-input"
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </div>

              <div class="filter-wrapper">
                <el-select
                  v-model="selectedCategory"
                  placeholder="全部分类"
                  size="large"
                  clearable
                  @change="handleCategoryFilter"
                  class="filter-select"
                >
                  <el-option value="" label="全部分类" />
                  <el-option
                    v-for="category in accountsStore.categories"
                    :key="category.id"
                    :value="category.id"
                    :label="category.name"
                  >
                    <div class="category-option">
                      <div
                        class="category-dot"
                        :style="{ backgroundColor: category.color }"
                      ></div>
                      {{ category.name }}
                    </div>
                  </el-option>
                </el-select>
              </div>

              <el-button
                type="primary"
                size="large"
                @click="handleSearch"
                class="search-btn"
              >
                <el-icon><Search /></el-icon>
                查询
              </el-button>

              <el-button size="large" @click="handleReset" class="reset-btn">
                <el-icon><Refresh /></el-icon>
                重置
              </el-button>
            </div>

            <div class="toolbar-right">
              <div class="stats-info">
                <span class="stats-number">{{ filteredAccounts.length }}</span>
                <span class="stats-label">个账号</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 账号表格组件 -->
        <AccountTable
          :accounts="filteredAccounts"
          :loading="accountsStore.isLoading"
          :search-keyword="searchKeyword"
          :categories="accountsStore.categories"
          :show-pagination="true"
          :current-page="currentPage"
          :page-size="pageSize"
          :total="filteredAccounts.length"
          @view-detail="handleViewDetail"
          @delete="handleDelete"
          @batch-delete="handleBatchDelete"
          @copy="handleCopy"
          @add-account="handleAddAccount"
          @page-change="handlePageChange"
          @page-size-change="handlePageSizeChange"
          @pagination-change="handlePaginationChange"
        />
      </div>
    </AppLayout>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import { ElMessageBox } from "element-plus";
import { Plus, Refresh, Search } from "@element-plus/icons-vue";
import AppLayout from "../components/AppLayout.vue";
import AccountTable from "../components/AccountTable.vue";
import {
  copyToClipboard,
  showSuccess,
  showError,
  showInfo,
  sleep,
} from "../utils";
import { useAccountsStore } from "../stores/accounts";
import type { Account } from "../stores/accounts";

const router = useRouter();
const accountsStore = useAccountsStore();

const searchKeyword = ref("");
const selectedCategory = ref("");

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);

const filteredAccounts = computed(() => {
  let accounts = accountsStore.accounts;

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    accounts = accounts.filter(
      (account) =>
        account.platform.toLowerCase().includes(keyword) ||
        account.username.toLowerCase().includes(keyword) ||
        (account.email && account.email.toLowerCase().includes(keyword))
    );
  }

  // 按分类筛选
  if (selectedCategory.value) {
    accounts = accounts.filter(
      (account) => account.category_id === selectedCategory.value
    );
  }

  return accounts;
});

onMounted(async () => {
  await loadData();
});

const loadData = async () => {
  try {
    await Promise.all([
      accountsStore.loadAccounts(),
      accountsStore.loadCategories(),
    ]);
  } catch (error) {
    showError("加载数据失败");
    console.error(error);
  }
};

// 移除未使用的 refreshAccounts 函数

const handleSearch = () => {
  // 查询功能由计算属性 filteredAccounts 自动处理
  // 这里可以添加一些查询提示
  if (searchKeyword.value || selectedCategory.value) {
    showSuccess(`查询完成，找到 ${filteredAccounts.value.length} 个账号`);
  } else {
    showInfo("请输入搜索关键词或选择分类");
  }
  currentPage.value = 1; // 重置到第一页
};

const handleReset = () => {
  searchKeyword.value = "";
  selectedCategory.value = "";
  currentPage.value = 1;
  showSuccess("已重置筛选条件");
};

const handleClearSearch = () => {
  searchKeyword.value = "";
};

const handleCategoryFilter = () => {
  // 分类筛选由计算属性自动处理
  currentPage.value = 1; // 重置到第一页
};

// 分页处理函数
const handlePageChange = (page: number) => {
  currentPage.value = page;
};

const handlePageSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1; // 重置到第一页
};

const handlePaginationChange = (data: { page: number; pageSize: number }) => {
  currentPage.value = data.page;
  pageSize.value = data.pageSize;
};

const handleViewDetail = async (account: Account) => {
  try {
    // 添加短暂延迟，避免快速点击导致的问题
    await sleep(100);
    router.push(`/account/detail/${account.id}`);
  } catch (error) {
    console.error("Navigation error:", error);
    showError("页面跳转失败，请重试");
  }
};

const handleDelete = (account: Account) => {
  ElMessageBox.confirm(
    `确定要删除账号 "${account.platform} - ${account.username}" 吗？此操作不可恢复。`,
    "确认删除",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  )
    .then(async () => {
      try {
        await accountsStore.deleteAccount(account.id);
        showSuccess("删除成功");
      } catch (error) {
        showError("删除失败");
        console.error(error);
      }
    })
    .catch(() => {
      // 用户取消删除
    });
};

const handleBatchDelete = (accountIds: string[]) => {
  ElMessageBox.confirm(
    `确定要删除选中的 ${accountIds.length} 个账号吗？此操作不可恢复。`,
    "确认批量删除",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  )
    .then(async () => {
      try {
        const deletedCount = await accountsStore.batchDeleteAccounts(
          accountIds
        );
        showSuccess(`成功删除 ${deletedCount} 个账号`);
      } catch (error) {
        showError("批量删除失败");
        console.error(error);
      }
    })
    .catch(() => {
      // 用户取消删除
    });
};

const handleCopy = async (text: string) => {
  await copyToClipboard(text);
};

// 工具函数已移动到 AccountTable 组件中

const handleAddAccount = async () => {
  try {
    // 添加短暂延迟，避免快速点击导致的问题
    await sleep(100);
    router.push("/account/new");
  } catch (error) {
    console.error("Navigation error:", error);
    showError("页面跳转失败，请重试");
  }
};
</script>

<style lang="scss" scoped>
.accounts {
  height: 100vh;
}

.accounts-content {
  padding: $spacing-lg;
}

.accounts-header {
  @include flex-center-between;
  align-items: flex-start;
  margin-bottom: $spacing-lg;

  .header-left {
    h1 {
      margin: 0 0 $spacing-xs 0;
      font-size: $font-size-xxxl;
      font-weight: $font-weight-semibold;
      color: $text-color-primary;
      @include smooth-transition(color);
    }

    p {
      margin: 0;
      color: $text-color-secondary;
      font-size: $font-size-base;
    }
  }
}

.accounts-toolbar {
  margin-bottom: $spacing-lg;
}

.toolbar-card {
  @include flex-center-between;
  padding: $spacing-lg $spacing-xl;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(0, 0, 0, 0.06);
  border-radius: $border-radius-lg;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  @include smooth-transition(all);

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }
}

.toolbar-left {
  @include flex-center;
  gap: $spacing-base;
  flex: 1;
}

.toolbar-right {
  .stats-info {
    @include flex-center;
    gap: $spacing-xs;
    color: $text-color-secondary;
    font-size: $font-size-sm;

    .stats-number {
      font-size: $font-size-lg;
      font-weight: $font-weight-semibold;
      color: $color-primary;
    }
  }
}

.search-input-wrapper {
  position: relative;
  width: 350px;

  .search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: $text-color-tertiary;
    font-size: 16px;
    z-index: 1;
    @include smooth-transition(color);
  }

  .search-input {
    :deep(.arco-input) {
      padding-left: 40px;
      border-radius: $border-radius-lg;
      @include smooth-transition(all);

      &:focus {
        border-color: $color-primary;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
      }
    }
  }

  &:focus-within .search-icon {
    color: $color-primary;
  }
}

.filter-wrapper {
  width: 200px;

  .filter-select {
    :deep(.arco-select-view) {
      border-radius: $border-radius-lg;
      @include smooth-transition(all);

      &.arco-select-view-focus {
        border-color: $color-primary;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
      }
    }
  }
}

.search-btn {
  border-radius: $border-radius-lg;
  @include smooth-transition(all);

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
  }
}

.reset-btn {
  border-radius: $border-radius-lg;
  @include smooth-transition(all);

  &:hover {
    border-color: $color-primary;
    color: $color-primary;
  }
}

.category-option {
  @include flex-center;
  gap: $spacing-xs;

  .category-dot {
    width: 8px;
    height: 8px;
    border-radius: $border-radius-circle;
  }
}

// 表格相关样式已移动到 AccountTable 组件中

// 响应式设计
@include mobile {
  .accounts-content {
    padding: $spacing-base;
  }

  .accounts-header {
    @include flex-column;
    gap: $spacing-base;
  }

  .toolbar-card {
    @include flex-column;
    gap: $spacing-base;

    .toolbar-left {
      width: 100%;
      @include flex-column;
      gap: $spacing-sm;

      .search-input-wrapper,
      .filter-wrapper {
        width: 100%;
      }
    }

    .toolbar-right {
      width: 100%;
      text-align: center;
    }
  }

  // 表格组件的移动端样式已在 AccountTable 组件中处理
}

@include tablet {
  .accounts-content {
    padding: $spacing-lg;
  }
}
</style>
