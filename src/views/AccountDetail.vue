<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-22 23:02:33
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-06-29 00:01:23
 * @FilePath     : /src/views/AccountDetail.vue
 * @Description  : 账号详情页面，使用通用组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-22 23:02:33
-->

<template>
  <div class="account-detail">
    <AppLayout>
      <div class="detail-content">
        <!-- 页面头部 -->
        <div class="detail-header">
          <div class="header-left">
            <a-button type="text" @click="goBack" class="back-btn">
              <template #icon><IconLeft /></template>
              返回
            </a-button>
            <div class="title-section">
              <h1>{{ isEditMode ? "编辑账号" : "账号详情" }}</h1>
              <p v-if="account">
                {{ account.platform }} - {{ account.username }}
              </p>
            </div>
          </div>
          <div class="header-right">
            <a-button
              v-if="!isEditMode"
              type="primary"
              @click="enterEditMode"
              :loading="loading"
            >
              <template #icon><IconEdit /></template>
              编辑账号
            </a-button>
            <div v-else class="edit-actions">
              <a-button @click="cancelEdit"> 取消 </a-button>
              <a-button
                type="primary"
                @click="handleFormSubmit(editFormData)"
                :loading="saving"
              >
                保存
              </a-button>
            </div>
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="detail-body">
          <div v-if="loading" class="loading-state">
            <a-spin size="large" />
            <p>加载账号信息...</p>
          </div>

          <div v-else-if="!account" class="error-state">
            <a-result
              status="404"
              title="账号不存在"
              subtitle="可能已被删除或您没有访问权限"
            >
              <template #extra>
                <a-button type="primary" @click="goBack">
                  返回账号列表
                </a-button>
              </template>
            </a-result>
          </div>

          <a-card v-else class="info-card">
            <!-- 查看模式 -->
            <AccountInfo
              v-if="!isEditMode"
              :account-data="account"
              :categories="categories"
              :on-decrypt-password="decryptPassword"
              @copy="handleCopy"
              @toggle-password="handleTogglePassword"
            />

            <!-- 编辑模式 -->
            <AccountForm
              v-else
              v-model="editFormData"
              :loading="saving"
              :categories="categories"
              :show-actions="false"
              submit-text="保存"
              cancel-text="取消"
              @submit="handleFormSubmit"
              @cancel="cancelEdit"
            />
          </a-card>
        </div>
      </div>
    </AppLayout>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { IconLeft, IconEdit } from "@arco-design/web-vue/es/icon";
import AppLayout from "../components/AppLayout.vue";
import AccountInfo from "../components/AccountInfo.vue";
import AccountForm from "../components/AccountForm.vue";
import { useAccountsStore } from "../stores/accounts";
import { showError, showSuccess } from "../utils";
import type { Account } from "../stores/accounts";

const route = useRoute();
const router = useRouter();
const accountsStore = useAccountsStore();

const loading = ref(true);
const saving = ref(false);
const isEditMode = ref(false);
const account = ref<Account | null>(null);
const categories = ref<Array<{ id: string; name: string; color?: string }>>([]);

// 编辑表单数据
const editFormData = reactive({
  platform: "",
  username: "",
  display_name: "",
  password: "",
  email: "",
  phone: "",
  website_url: "",
  category_id: "",
  notes: "",
  custom_fields: {} as Record<string, string>,
});

onMounted(async () => {
  await loadAccount();
});

const loadAccount = async () => {
  try {
    loading.value = true;
    const accountId = route.params.id as string;

    // 并行加载账号信息和分类列表
    const [accountData, categoriesData] = await Promise.all([
      accountsStore.getAccountById(accountId),
      accountsStore.loadCategories().then(() => accountsStore.categories),
    ]);

    if (accountData) {
      account.value = accountData;
      categories.value = categoriesData;
    } else {
      showError("账号不存在");
    }
  } catch (error) {
    console.error("Failed to load account:", error);
    showError("加载账号信息失败");
  } finally {
    loading.value = false;
  }
};

const goBack = () => {
  router.push("/accounts");
};

const enterEditMode = async () => {
  if (!account.value) return;

  // 复制当前账号信息到编辑表单
  editFormData.platform = account.value.platform;
  editFormData.username = account.value.username;
  editFormData.display_name = account.value.display_name || "";
  editFormData.email = account.value.email || "";
  editFormData.phone = account.value.phone || "";
  editFormData.website_url = account.value.website_url || "";
  editFormData.category_id = account.value.category_id || "";
  editFormData.notes = account.value.notes || "";

  // 复制自定义字段数据
  editFormData.custom_fields = account.value.custom_fields || {};

  // 解密密码用于编辑
  await loadPasswordForEdit();

  isEditMode.value = true;
};

const cancelEdit = () => {
  isEditMode.value = false;
};

const loadPasswordForEdit = async () => {
  if (!account.value) return;

  try {
    const password = await accountsStore.decryptPassword(
      account.value.encrypted_password
    );
    editFormData.password = password;
  } catch (error) {
    console.error("Failed to decrypt password:", error);
    showError("密码解密失败，请重新输入密码");
    editFormData.password = "";
  }
};

const handleFormSubmit = async (formValues: Record<string, any>) => {
  if (!account.value) return;

  try {
    saving.value = true;

    const updateData = {
      id: account.value.id,
      platform: formValues.platform?.trim() || "",
      username: formValues.username?.trim() || "",
      display_name: formValues.display_name?.trim() || undefined,
      password: formValues.password || "",
      email: formValues.email?.trim() || undefined,
      phone: formValues.phone?.trim() || undefined,
      website_url: formValues.website_url?.trim() || undefined,
      category_id:
        formValues.category_id && formValues.category_id.trim() !== ""
          ? formValues.category_id.trim()
          : null,
      notes: formValues.notes?.trim() || undefined,
      custom_fields: formValues.custom_fields || {},
    };

    const updatedAccount = await accountsStore.updateAccount(updateData);
    account.value = updatedAccount;

    isEditMode.value = false;
    showSuccess("账号更新成功");
  } catch (error) {
    console.error("Failed to update account:", error);
    showError("账号更新失败");
  } finally {
    saving.value = false;
  }
};

// 密码解密方法
const decryptPassword = async (encryptedPassword: string): Promise<string> => {
  return await accountsStore.decryptPassword(encryptedPassword);
};

// 处理复制事件
const handleCopy = (value: string) => {
  console.log("Copied:", value);
};

// 处理密码切换事件
const handleTogglePassword = () => {
  console.log("Password toggled");
};
</script>

<style lang="scss" scoped>
@import "@/styles/variables.scss";
@import "@/styles/mixins.scss";

.account-detail {
  min-height: 100vh;
}

.detail-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;

  @media (min-width: 1600px) {
    max-width: 1600px;
  }

  @media (min-width: 1920px) {
    max-width: 1800px;
  }
}

.detail-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;

    .back-btn {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .title-section {
      h1 {
        margin: 0;
        font-size: 24px;
      }

      p {
        margin: 4px 0 0 0;
        color: #595959;
      }
    }
  }

  .edit-actions {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.detail-body {
  .loading-state {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding: 48px;
    gap: 16px;
  }

  .info-card {
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    width: 100%;

    // 在大屏幕上让卡片更宽
    @media (min-width: 1200px) {
      min-height: 400px;
    }

    :deep(.el-card__body) {
      padding: 32px;

      @media (min-width: 1200px) {
        padding: 40px;
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .detail-content {
      padding: 16px;
    }

    .detail-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-left {
        width: 100%;
      }

      .header-right {
        width: 100%;

        .edit-actions {
          width: 100%;
          justify-content: flex-end;
        }
      }
    }
  }
}
</style>
