<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-22 23:06:38
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-06-28 11:31:28
 * @FilePath     : /src/views/AccountAdd.vue
 * @Description  : 添加账号页面，使用通用表单组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-22 23:06:38
-->
<template>
  <div class="account-add">
    <AppLayout>
      <div class="form-content">
        <div class="form-header">
          <div class="header-left">
            <a-button type="text" @click="goBack" class="back-btn">
              <template #icon><IconLeft /></template>
              返回
            </a-button>
            <div class="title-section">
              <h1>添加账号</h1>
            </div>
          </div>
        </div>

        <a-card class="form-card">
          <AccountForm
            v-model="formData"
            :loading="loading"
            :categories="categories"
            submit-text="添加账号"
            cancel-text="取消"
            @submit="handleSubmit"
            @cancel="handleCancel"
          />
        </a-card>
      </div>
    </AppLayout>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { Message } from "@arco-design/web-vue";
import { IconLeft } from "@arco-design/web-vue/es/icon";
import AppLayout from "../components/AppLayout.vue";
import AccountForm from "../components/AccountForm.vue";
import { useAccountsStore } from "../stores/accounts";
import type { CreateAccountRequest } from "../stores/accounts";

const router = useRouter();
const accountsStore = useAccountsStore();

const loading = ref(false);
const categories = ref<Array<{ id: string; name: string; color?: string }>>([]);

// 表单数据
const formData = reactive({
  platform: "",
  username: "",
  display_name: "",
  password: "",
  email: "",
  phone: "",
  website_url: "",
  category_id: "",
  notes: "",
});

onMounted(async () => {
  try {
    await accountsStore.loadCategories();
    categories.value = accountsStore.categories;
  } catch (error) {
    console.error("Failed to load categories:", error);
  }
});

const goBack = () => {
  router.push("/accounts");
};

const handleCancel = () => {
  goBack();
};

const handleSubmit = async (formValues: Record<string, any>) => {
  console.log("=== 开始提交添加账号表单 ===");
  try {
    console.log("步骤1: 设置加载状态...");
    loading.value = true;

    console.log("步骤2: 构建请求数据...");
    const requestData: CreateAccountRequest = {
      platform: formValues.platform?.trim() || "",
      username: formValues.username?.trim() || "",
      display_name: formValues.display_name?.trim() || undefined,
      password: formValues.password || "",
      email: formValues.email?.trim() || undefined,
      phone: formValues.phone?.trim() || undefined,
      website_url: formValues.website_url?.trim() || undefined,
      category_id: formValues.category_id || undefined,
      notes: formValues.notes?.trim() || undefined,
    };

    console.log("请求数据:", { ...requestData, password: "***" });

    console.log("步骤3: 调用 Store 创建账号...");
    const newAccount = await accountsStore.createAccount(requestData);
    console.log("账号创建成功:", newAccount);

    console.log("步骤4: 显示成功消息并跳转...");
    Message.success("账号添加成功！");
    router.push("/accounts");

    console.log("=== 添加账号流程完成 ===");
  } catch (error) {
    console.error("=== 添加账号失败 ===");
    console.error("错误详情:", error);

    const errorMessage = String(error);
    console.log("错误消息:", errorMessage);

    if (errorMessage.includes("认证状态失效")) {
      // 认证拦截器已经处理了跳转，这里只需要显示消息
      console.log("认证失效，已由拦截器处理跳转");
    } else {
      Message.error(`添加失败：${errorMessage}`);
    }
  } finally {
    loading.value = false;
  }
};
</script>

<style lang="scss" scoped>
@import "@/styles/variables.scss";
@import "@/styles/mixins.scss";

.account-add {
  min-height: 100vh;
}

.form-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: $spacing-lg;

  @media (max-width: 1024px) {
    max-width: 900px;
  }

  @media (max-width: 768px) {
    max-width: 100%;
    padding: $spacing-base;
  }
}

.form-header {
  margin-bottom: $spacing-lg;
  padding-bottom: $spacing-lg;
  border-bottom: 1px solid $border-color-light;

  .header-left {
    @include flex-center;
    gap: $spacing-base;

    .back-btn {
      @include flex-center;
      gap: $spacing-xs;
    }

    .title-section {
      h1 {
        margin: 0;
        font-size: $font-size-xxl;
      }

      p {
        margin: $spacing-xs 0 0 0;
        color: $text-color-secondary;
      }
    }
  }
}

.form-card {
  border-radius: $border-radius-lg;
  box-shadow: $shadow-sm;
}
</style>
