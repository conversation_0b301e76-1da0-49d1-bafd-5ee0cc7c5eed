<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-22 23:06:38
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-29 00:00:19
 * @FilePath     : /src/views/AccountAdd.vue
 * @Description  : 添加账号页面，使用通用表单组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-22 23:06:38
-->
<template>
  <div class="account-add">
    <AppLayout>
      <div class="detail-content">
        <!-- 页面头部 -->
        <div class="detail-header">
          <div class="header-left">
            <el-button type="default" @click="goBack" class="back-btn">
              <el-icon><ArrowLeft /></el-icon>
              返回
            </el-button>
            <div class="title-section">
              <h1>添加账号</h1>
            </div>
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="detail-body">
          <el-card class="info-card">
            <AccountForm
              v-model="formData"
              :loading="loading"
              :categories="categories"
              submit-text="添加账号"
              cancel-text="取消"
              @submit="handleSubmit"
              @cancel="handleCancel"
            />
          </el-card>
        </div>
      </div>
    </AppLayout>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { ArrowLeft } from "@element-plus/icons-vue";
import AppLayout from "../components/AppLayout.vue";
import AccountForm from "../components/AccountForm.vue";
import { useAccountsStore } from "../stores/accounts";
import type { CreateAccountRequest } from "../stores/accounts";

const router = useRouter();
const accountsStore = useAccountsStore();

const loading = ref(false);
const categories = ref<Array<{ id: string; name: string; color?: string }>>([]);

// 表单数据
const formData = reactive({
  platform: "",
  username: "",
  display_name: "",
  password: "",
  email: "",
  phone: "",
  website_url: "",
  category_id: "",
  notes: "",
});

onMounted(async () => {
  try {
    await accountsStore.loadCategories();
    categories.value = accountsStore.categories;
  } catch (error) {
    console.error("Failed to load categories:", error);
  }
});

const goBack = () => {
  router.push("/accounts");
};

const handleCancel = () => {
  goBack();
};

const handleSubmit = async (formValues: Record<string, any>) => {
  console.log("=== 开始提交添加账号表单 ===");
  try {
    console.log("步骤1: 设置加载状态...");
    loading.value = true;

    console.log("步骤2: 构建请求数据...");
    const requestData: CreateAccountRequest = {
      platform: formValues.platform?.trim() || "",
      username: formValues.username?.trim() || "",
      display_name: formValues.display_name?.trim() || undefined,
      password: formValues.password || "",
      email: formValues.email?.trim() || undefined,
      phone: formValues.phone?.trim() || undefined,
      website_url: formValues.website_url?.trim() || undefined,
      category_id: formValues.category_id || undefined,
      notes: formValues.notes?.trim() || undefined,
    };

    console.log("请求数据:", { ...requestData, password: "***" });

    console.log("步骤3: 调用 Store 创建账号...");
    const newAccount = await accountsStore.createAccount(requestData);
    console.log("账号创建成功:", newAccount);

    console.log("步骤4: 显示成功消息并跳转...");
    ElMessage.success("账号添加成功！");
    router.push("/accounts");

    console.log("=== 添加账号流程完成 ===");
  } catch (error) {
    console.error("=== 添加账号失败 ===");
    console.error("错误详情:", error);

    const errorMessage = String(error);
    console.log("错误消息:", errorMessage);

    if (errorMessage.includes("认证状态失效")) {
      // 认证拦截器已经处理了跳转，这里只需要显示消息
      console.log("认证失效，已由拦截器处理跳转");
    } else {
      ElMessage.error(`添加失败：${errorMessage}`);
    }
  } finally {
    loading.value = false;
  }
};
</script>

<style lang="scss" scoped>
@import "@/styles/variables.scss";
@import "@/styles/mixins.scss";

.account-add {
  min-height: 100vh;
}

.detail-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;

  @media (min-width: 1600px) {
    max-width: 1600px;
  }

  @media (min-width: 1920px) {
    max-width: 1800px;
  }
}

.detail-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;

    .back-btn {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .title-section {
      h1 {
        margin: 0;
        font-size: 24px;
      }

      p {
        margin: 4px 0 0 0;
        color: #595959;
      }
    }
  }
}

.detail-body {
  .info-card {
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    width: 100%;

    // 在大屏幕上让卡片更宽
    @media (min-width: 1200px) {
      min-height: 400px;
    }

    :deep(.el-card__body) {
      padding: 32px;

      @media (min-width: 1200px) {
        padding: 40px;
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .detail-content {
      padding: 16px;
    }

    .detail-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-left {
        width: 100%;
      }
    }
  }
}
</style>
