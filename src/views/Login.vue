<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <h1>账号管理系统</h1>
        <p>{{ isFirstTime ? "设置主密码" : "请输入主密码解锁" }}</p>
      </div>

      <a-form
        :model="form"
        @submit="handleSubmit"
        layout="vertical"
        class="login-form"
      >
        <a-form-item
          field="password"
          :label="isFirstTime ? '设置主密码' : '主密码'"
          :rules="[{ required: true, message: '请输入密码' }]"
        >
          <a-input-password
            v-model="form.password"
            placeholder="请输入密码"
            size="large"
            @press-enter="handleSubmit"
          />
        </a-form-item>

        <a-form-item
          v-if="isFirstTime"
          field="confirmPassword"
          label="确认密码"
        >
          <a-input-password
            v-model="form.confirmPassword"
            placeholder="请再次输入密码"
            size="large"
            @press-enter="handleSubmit"
          />
        </a-form-item>

        <a-form-item>
          <a-button
            type="primary"
            html-type="submit"
            size="large"
            long
            :loading="authStore.isLoading"
          >
            {{ isFirstTime ? "设置密码" : "解锁" }}
          </a-button>
        </a-form-item>
      </a-form>

      <div class="login-footer">
        <p>数据完全本地存储，安全可靠</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useAuthStore } from "../stores/auth";
import { showError, showWarning, showSuccess } from "../utils";

const router = useRouter();
const authStore = useAuthStore();

const isFirstTime = ref(false);
const form = ref({
  password: "Zp!23456",
  confirmPassword: "",
});

onMounted(async () => {
  try {
    // 只检查是否已设置主密码，数据库已在 App.vue 中初始化
    isFirstTime.value = !(await authStore.checkMasterPasswordSet());
  } catch (error) {
    console.error("Failed to check master password:", error);
    showError("检查主密码状态失败");
  }
});

const handleSubmit = async () => {
  if (!form.value.password) {
    showWarning("请输入密码");
    return;
  }

  if (isFirstTime.value) {
    if (form.value.password !== form.value.confirmPassword) {
      showWarning("两次输入的密码不一致");
      return;
    }

    if (form.value.password.length < 6) {
      showWarning("密码长度至少6位");
      return;
    }
  }

  try {
    const success = await authStore.verifyMasterPassword(form.value.password);
    if (success) {
      showSuccess(isFirstTime.value ? "密码设置成功" : "解锁成功");
      router.push("/dashboard");
    } else {
      showError("密码错误");
    }
  } catch (error) {
    showError("操作失败，请重试");
    console.error(error);
  }
};
</script>

<style lang="scss" scoped>
.login-container {
  height: 100vh;
  @include flex-center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-card {
  width: 400px;
  padding: $spacing-xl;
  background: $background-color-primary;
  border-radius: $border-radius-lg;
  @include card-shadow;
  box-shadow: $shadow-xxl;
  @include smooth-transition(transform);

  &:hover {
    transform: translateY(-2px);
  }

  // 响应式设计
  @include mobile {
    width: 90%;
    margin: $spacing-base;
    padding: $spacing-lg;
  }
}

.login-header {
  text-align: center;
  margin-bottom: $spacing-xl;

  h1 {
    margin: 0 0 $spacing-xs 0;
    font-size: $font-size-xxxl;
    font-weight: $font-weight-semibold;
    color: $text-color-primary;
    @include smooth-transition(color);
  }

  p {
    margin: 0;
    color: $text-color-secondary;
    font-size: $font-size-sm;
  }
}

.login-form {
  margin-bottom: $spacing-lg;

  // 自定义表单项间距
  :deep(.arco-form-item) {
    margin-bottom: $spacing-lg;

    &:last-child {
      margin-bottom: 0;
    }
  }

  // 自定义输入框样式
  :deep(.arco-input-wrapper) {
    @include smooth-transition(border-color, box-shadow);

    &:focus-within {
      box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
    }
  }

  // 按钮样式增强
  :deep(.arco-btn-primary) {
    height: 44px;
    font-size: $font-size-base;
    font-weight: $font-weight-medium;
    @include smooth-transition(all);

    &:hover {
      transform: translateY(-1px);
      box-shadow: $shadow-lg;
    }
  }
}

.login-footer {
  text-align: center;

  p {
    margin: 0;
    color: $text-color-disabled;
    font-size: $font-size-xs;
  }
}
</style>
