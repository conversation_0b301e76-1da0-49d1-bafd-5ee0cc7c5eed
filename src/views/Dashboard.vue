<template>
  <div class="dashboard">
    <AppLayout>
      <div class="dashboard-content">
        <div class="dashboard-header">
          <h1 class="page-title">仪表板</h1>
          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-number">{{ totalAccounts }}</div>
              <div class="stat-label">总账号数</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">{{ categoriesCount }}</div>
              <div class="stat-label">分类数</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">{{ recentlyAdded }}</div>
              <div class="stat-label">本月新增</div>
            </div>
          </div>
        </div>

        <div class="dashboard-sections">
          <div class="section quick-actions-section">
            <a-card title="快速操作" class="quick-actions-card">
              <div class="quick-actions">
                <a-button
                  type="primary"
                  size="large"
                  @click="$router.push('/account/new')"
                >
                  <template #icon><icon-plus /></template>
                  添加账号
                </a-button>
                <a-button size="large" @click="$router.push('/accounts')">
                  <template #icon><icon-list /></template>
                  查看所有账号
                </a-button>
                <a-button size="large" @click="$router.push('/categories')">
                  <template #icon><icon-folder /></template>
                  管理分类
                </a-button>
              </div>
            </a-card>
          </div>

          <div class="section recent-accounts-section">
            <a-card title="最近添加的账号" class="recent-accounts-card">
              <div v-if="recentAccounts.length === 0" class="empty-state">
                <icon-user style="font-size: 48px; color: #c9cdd4" />
                <p>暂无账号</p>
                <a-button type="primary" @click="$router.push('/account/new')">
                  添加第一个账号
                </a-button>
              </div>
              <div v-else class="recent-accounts">
                <div
                  v-for="account in recentAccounts"
                  :key="account.id"
                  class="account-item"
                  @click="$router.push(`/account/detail/${account.id}`)"
                >
                  <div class="account-info">
                    <div class="platform">{{ account.platform }}</div>
                    <div class="username">{{ account.username }}</div>
                  </div>
                  <div class="account-date">
                    {{ formatDate(account.created_at) }}
                  </div>
                </div>
              </div>
            </a-card>
          </div>
        </div>

        <div class="recent-activity">
          <h2 class="section-title">最近活动</h2>
          <div class="activity-list">
            <div class="activity-item">
              <div class="activity-icon">
                <icon-plus />
              </div>
              <div class="activity-content">
                <div class="activity-title">添加了新账号</div>
                <div class="activity-time">2 小时前</div>
              </div>
            </div>
            <!-- 更多活动项... -->
          </div>
        </div>
      </div>
    </AppLayout>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import {
  IconUser,
  IconFolder,
  IconPlus,
  IconList,
} from "@arco-design/web-vue/es/icon";
import AppLayout from "../components/AppLayout.vue";
import { useAccountsStore } from "../stores/accounts";

const accountsStore = useAccountsStore();

const stats = ref({
  totalAccounts: 0,
  totalCategories: 0,
  weeklyAdded: 0,
  recentlyUpdated: 0,
});

const recentAccounts = computed(() => {
  return accountsStore.accounts.slice(0, 5);
});

const totalAccounts = computed(() => accountsStore.accounts.length);
const categoriesCount = computed(() => accountsStore.categories.length);
const recentlyAdded = computed(() => {
  const thisMonth = new Date().getMonth();
  return accountsStore.accounts.filter((account) => {
    const accountMonth = new Date(account.created_at).getMonth();
    return accountMonth === thisMonth;
  }).length;
});

onMounted(async () => {
  await loadData();
});

const loadData = async () => {
  try {
    await Promise.all([
      accountsStore.loadAccounts(),
      accountsStore.loadCategories(),
    ]);

    calculateStats();
  } catch (error) {
    console.error("Failed to load dashboard data:", error);
  }
};

const calculateStats = () => {
  const now = new Date();
  const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

  stats.value = {
    totalAccounts: accountsStore.accounts.length,
    totalCategories: accountsStore.categories.length,
    weeklyAdded: accountsStore.accounts.filter(
      (account) => new Date(account.created_at) > weekAgo
    ).length,
    recentlyUpdated: accountsStore.accounts.filter(
      (account) => new Date(account.updated_at) > weekAgo
    ).length,
  };
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - date.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 1) return "今天";
  if (diffDays === 2) return "昨天";
  if (diffDays <= 7) return `${diffDays - 1}天前`;

  return date.toLocaleDateString("zh-CN");
};
</script>

<style lang="scss" scoped>
.dashboard {
  // 移除 padding，让内容完全填充空间
}

.dashboard-content {
  padding: $spacing-lg;
}

.dashboard-header {
  margin-bottom: $spacing-xl;
}

.page-title {
  font-size: $font-size-xxl;
  font-weight: $font-weight-bold;
  color: $text-color-primary;
  margin-bottom: $spacing-lg;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: $spacing-base;
  margin-bottom: $spacing-xl;
}

.stat-card {
  @include card-shadow;
  padding: $spacing-lg;
  text-align: center;
  background: $background-color-primary;
  border-radius: $border-radius-base;
  @include smooth-transition(transform);

  &:hover {
    @include card-hover;
    transform: translateY(-2px);
  }

  .stat-number {
    font-size: $font-size-xxl;
    font-weight: $font-weight-bold;
    color: $primary-color;
    margin-bottom: $spacing-xs;
  }

  .stat-label {
    font-size: $font-size-sm;
    color: $text-color-secondary;
    font-weight: $font-weight-medium;
  }
}

.dashboard-sections {
  display: grid;
  grid-template-columns: 400px 1fr;
  gap: 24px;
  max-width: 1200px; // 限制整体最大宽度
}

.section {
  min-height: 300px;
}

.quick-actions-section {
  // 快速操作区域固定宽度，更紧凑
}

.recent-accounts-section {
  // 最近账号区域占用剩余空间，但有最大宽度限制
  max-width: 600px;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
}

.empty-state p {
  margin: 16px 0;
  color: #86909c;
  font-size: 16px;
}

.recent-accounts {
  max-height: 300px;
  overflow-y: auto;
}

.account-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f2f3f5;
  cursor: pointer;
  transition: background-color 0.2s;
}

.account-item:hover {
  background-color: #f7f8fa;
  margin: 0 -16px;
  padding: 12px 16px;
  border-radius: 6px;
}

.account-item:last-child {
  border-bottom: none;
}

.account-info .platform {
  font-weight: 500;
  color: #1d2129;
  margin-bottom: 4px;
}

.account-info .username {
  font-size: 14px;
  color: #86909c;
}

.account-date {
  font-size: 12px;
  color: #c9cdd4;
}

.recent-activity {
  @include card-shadow;
  background: $background-color-primary;
  padding: $spacing-lg;
  border-radius: $border-radius-base;
  margin-top: $spacing-xl; // 增加上边距，与上面的内容保持更大间距
}

.activity-list {
  @include flex-column;
  gap: $spacing-base;
}

.activity-item {
  @include flex-center;
  gap: $spacing-base;
  padding: $spacing-base;
  border-radius: $border-radius-base;
  @include smooth-transition(background-color);

  &:hover {
    background: $background-color-secondary;
  }

  .activity-icon {
    @include flex-center;
    width: 32px;
    height: 32px;
    background: $primary-color-light;
    color: $primary-color;
    border-radius: $border-radius-base;
  }

  .activity-content {
    flex: 1;

    .activity-title {
      font-size: $font-size-sm;
      font-weight: $font-weight-medium;
      color: $text-color-primary;
      margin-bottom: $spacing-xs;
    }

    .activity-time {
      font-size: $font-size-xs;
      color: $text-color-secondary;
    }
  }
}

// 响应式设计
@include mobile {
  .dashboard-content {
    padding: $spacing-base;
  }

  .dashboard-sections {
    grid-template-columns: 1fr;
    max-width: none;
  }

  .quick-actions-section,
  .recent-accounts-section {
    max-width: none;
  }

  .quick-actions {
    gap: $spacing-sm;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: $spacing-sm;
  }

  .stat-card {
    padding: $spacing-base;
  }
}

@include tablet {
  .dashboard-content {
    padding: $spacing-lg;
  }

  .dashboard-sections {
    grid-template-columns: 350px 1fr;
  }
}
</style>
