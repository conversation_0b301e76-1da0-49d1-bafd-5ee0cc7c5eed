<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-20 00:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-20 23:42:14
 * @FilePath     : /src/views/finance/RecordForm.vue
 * @Description  : 财务记录表单页面
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-20 00:00:00
-->

<template>
  <AppLayout>
    <div class="finance-record-form">
      <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <el-button type="text" @click="goBack" class="back-button">
              <el-icon><ArrowLeft /></el-icon>
              返回
            </el-button>
            <span class="title">{{
              isEdit ? "编辑财务记录" : "新增财务记录"
            }}</span>
          </div>
        </template>

        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-position="top"
          @submit="handleSubmit"
          class="form-container"
        >
          <!-- 记账周期 -->
          <div class="period-section">
            <div class="period-card">
              <div class="period-row">
                <div class="period-title">
                  <svg
                    class="title-icon"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="#3B82F6"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <rect width="18" height="18" x="3" y="4" rx="2" ry="2" />
                    <line x1="16" x2="16" y1="2" y2="6" />
                    <line x1="8" x2="8" y1="2" y2="6" />
                    <line x1="3" x2="21" y1="10" y2="10" />
                  </svg>
                  记账周期
                </div>
                <el-form-item
                  prop="periodRange"
                  required
                  class="period-form-item"
                >
                  <el-date-picker
                    v-model="formData.periodRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    class="period-picker"
                    @change="handlePeriodChange"
                  />
                </el-form-item>
              </div>
            </div>
          </div>

          <!-- 财务总结 -->
          <div class="summary-section sticky-summary">
            <div class="summary-container">
              <el-row :gutter="12" class="summary-cards">
                <el-col :span="3">
                  <SummaryCard
                    type="income"
                    label="总收入"
                    :value="totalIncome"
                  />
                </el-col>
                <el-col :span="6">
                  <SummaryCard
                    type="expense"
                    label="总支出"
                    :value="totalExpense"
                    :credit-expense="creditExpense"
                    :cash-expense="cashExpense"
                  />
                </el-col>
                <el-col :span="3">
                  <SummaryCard
                    type="assets"
                    label="总现金资产"
                    :value="totalCashAssets"
                  />
                </el-col>
                <el-col :span="3">
                  <SummaryCard
                    type="after-salary"
                    label="发薪后"
                    :value="afterSalary"
                  />
                </el-col>
                <el-col :span="3">
                  <SummaryCard
                    type="after-payment"
                    label="还款后"
                    :value="afterPayment"
                  />
                </el-col>
                <el-col :span="6">
                  <SummaryCard
                    type="balance"
                    label="当期结余"
                    :value="currentBalance"
                  />
                </el-col>
              </el-row>
            </div>
          </div>

          <!-- 标签页导航 -->
          <div class="tabs-container">
            <div class="tabs-nav">
              <button
                :class="['tab-btn', { 'tab-active': activeTab === 'income' }]"
                @click="activeTab = 'income'"
                type="button"
              >
                收入项目
              </button>
              <button
                :class="['tab-btn', { 'tab-active': activeTab === 'expenses' }]"
                @click="activeTab = 'expenses'"
                type="button"
              >
                支出项目
              </button>
              <button
                :class="['tab-btn', { 'tab-active': activeTab === 'cash' }]"
                @click="activeTab = 'cash'"
                type="button"
              >
                现金资产
              </button>
              <button
                :class="['tab-btn', { 'tab-active': activeTab === 'actual' }]"
                @click="activeTab = 'actual'"
                type="button"
              >
                余
              </button>
            </div>
          </div>

          <!-- 标签页内容 -->
          <div class="tab-content-container">
            <!-- 收入面板 -->
            <div
              v-show="activeTab === 'income'"
              class="tab-content income-panel"
            >
              <div class="income-cards">
                <div class="income-card">
                  <h3 class="card-title">
                    <svg
                      class="title-icon"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="#10B981"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    >
                      <path
                        d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"
                      />
                    </svg>
                    收入来源
                  </h3>
                  <el-row :gutter="24">
                    <el-col
                      v-for="field in incomeFields"
                      :key="field.name"
                      :span="field.span"
                    >
                      <CurrencyInput
                        :label="field.label"
                        :icon="field.icon"
                        :color="field.color"
                        :step="field.step"
                        :model-value="formData[field.name]"
                        @update:model-value="(value: number) => formData[field.name] = value"
                      />
                    </el-col>
                  </el-row>
                </div>
              </div>
            </div>

            <!-- 支出面板 -->
            <div
              v-show="activeTab === 'expenses'"
              class="tab-content expenses-panel"
            >
              <div class="expense-cards">
                <ExpenseCard
                  v-for="category in expenseCategories"
                  :key="category.title"
                  :title="category.title"
                  :fields="category.fields"
                  :model-value="formData"
                  @update:model-value="(value: any) => Object.assign(formData, value)"
                />
              </div>
            </div>

            <!-- 现金资产面板 -->
            <div v-show="activeTab === 'cash'" class="tab-content cash-panel">
              <div class="cash-cards">
                <ExpenseCard
                  v-for="category in cashCategories"
                  :key="category.title"
                  :title="category.title"
                  :fields="category.fields"
                  :model-value="formData"
                  @update:model-value="(value: any) => Object.assign(formData, value)"
                />
              </div>
            </div>

            <!-- 实际结余面板 -->
            <div
              v-show="activeTab === 'actual'"
              class="tab-content actual-panel"
            >
              <div class="actual-balance-card">
                <div class="balance-card">
                  <h3 class="card-title">
                    <svg
                      class="title-icon"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="#52C41A"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    >
                      <path
                        d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"
                      />
                    </svg>
                    实际结余
                  </h3>
                  <div class="balance-input-container">
                    <CurrencyInput
                      label=""
                      :icon="actualBalanceField.icon"
                      :color="actualBalanceField.color"
                      :step="actualBalanceField.step"
                      :model-value="formData.actualBalance"
                      @update:model-value="(value: number) => formData.actualBalance = value"
                    />
                  </div>

                  <!-- 对比分析 -->
                  <div class="balance-comparison">
                    <el-row :gutter="16">
                      <el-col :span="12">
                        <div class="comparison-item">
                          <div class="comparison-label">预期还款后</div>
                          <div class="comparison-value expected">
                            ¥{{ formatCurrency(afterPayment) }}
                          </div>
                        </div>
                      </el-col>
                      <el-col :span="12">
                        <div class="comparison-item">
                          <div class="comparison-label">差额</div>
                          <div
                            :class="[
                              'comparison-value',
                              'difference',
                              balanceDifference >= 0 ? 'positive' : 'negative',
                            ]"
                          >
                            {{ balanceDifference >= 0 ? "+" : "" }}¥{{
                              formatCurrency(Math.abs(balanceDifference))
                            }}
                          </div>
                        </div>
                      </el-col>
                    </el-row>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 备注 -->
          <div class="remark-section">
            <div class="remark-card">
              <NoteSystem v-model="formData.notes" />
            </div>
          </div>

          <!-- 操作按钮 -->
          <el-row :gutter="16">
            <el-col :span="24">
              <div class="form-actions">
                <el-button @click="goBack" size="large">取消</el-button>
                <el-button
                  type="primary"
                  native-type="submit"
                  size="large"
                  :loading="loading"
                >
                  {{ isEdit ? "更新" : "保存" }}
                </el-button>
              </div>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { ArrowLeft } from "@element-plus/icons-vue";
import AppLayout from "../../components/AppLayout.vue";
import CurrencyInput from "../../components/finance/CurrencyInput.vue";
import ExpenseCard from "../../components/finance/ExpenseCard.vue";
import SummaryCard from "../../components/finance/SummaryCard.vue";
import NoteSystem from "../../components/finance/NoteSystem.vue";
import { useFinanceForm } from "../../composables/useFinanceForm";
import {
  INCOME_FIELDS,
  EXPENSE_CATEGORIES,
  CASH_CATEGORIES,
  ACTUAL_BALANCE_FIELD,
} from "../../config/financeConfig";

// 标签页控制
const activeTab = ref("income");

// 使用财务表单逻辑
const {
  formRef,
  loading,
  isEdit,
  formData,
  formRules,
  totalIncome,
  totalExpense,
  creditExpense,
  cashExpense,
  totalCashAssets,
  currentBalance,
  afterSalary,
  afterPayment,
  balanceDifference,
  handlePeriodChange,
  goBack,
  handleSubmit,
  loadRecordData,
} = useFinanceForm();

// 配置数据
const incomeFields = INCOME_FIELDS;
const expenseCategories = EXPENSE_CATEGORIES;
const cashCategories = CASH_CATEGORIES;
const actualBalanceField = ACTUAL_BALANCE_FIELD;

// 格式化货币函数
const formatCurrency = (value: number): string => {
  if (value % 1 === 0) {
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  }
  return value.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
};

// 页面加载时执行
onMounted(() => {
  loadRecordData();
});
</script>

<style lang="scss" scoped>
@import "@/styles/variables.scss";
@import "@/styles/mixins.scss";

// 记账周期样式
.period-section {
  .period-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .period-row {
      display: flex;
      align-items: center;
      gap: 2rem;

      .period-title {
        display: flex;
        align-items: center;
        font-size: 1.125rem;
        font-weight: 600;
        color: #1f2937;
        white-space: nowrap;
        min-width: fit-content;

        .title-icon {
          width: 1.25rem;
          height: 1.25rem;
          margin-right: 0.5rem;
        }
      }

      .period-form-item {
        flex: 1;
        margin-bottom: 0;
      }

      // 响应式调整
      @include mobile {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;

        .period-title {
          width: 100%;
        }

        .period-form-item {
          width: 100%;
        }
      }
    }

    :deep(.period-picker) {
      width: 100%;
      max-width: 400px;

      // 日期范围选择器的外层容器
      .el-date-editor {
        width: 100%;

        .el-input__wrapper {
          border-radius: 0.375rem;
          border: 1px solid #d1d5db;
          transition: border-color 0.2s ease;
          padding: 0.5rem 0.75rem;
          min-height: 40px;
          height: 40px;

          &:hover {
            border-color: #9ca3af;
          }

          &.is-focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
          }
        }

        .el-input__inner {
          color: #374151;
          height: 24px;
          line-height: 24px;

          &::placeholder {
            color: #9ca3af;
          }
        }

        .el-range-separator {
          color: #6b7280;
          font-weight: 500;
          line-height: 24px;
        }

        .el-range-input {
          height: 24px;
          line-height: 24px;
        }
      }

      // 直接作用于 el-date-editor 类
      &.el-date-editor {
        .el-input__wrapper {
          border-radius: 0.375rem;
          border: 1px solid #d1d5db;
          transition: border-color 0.2s ease;
          padding: 0.5rem 0.75rem;
          min-height: 40px;
          height: 40px;

          &:hover {
            border-color: #9ca3af;
          }

          &.is-focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
          }
        }
      }
    }
  }
}

// 全局样式覆盖，确保日期选择器高度生效
:deep(.el-date-editor.el-input) {
  .el-input__wrapper {
    min-height: 40px !important;
    height: 40px !important;
    padding: 0.5rem 0.75rem !important;
  }
}

// 针对 period-picker 的强制样式
:deep(.period-picker.el-date-editor) {
  .el-input__wrapper {
    min-height: 40px !important;
    height: 40px !important;
    padding: 0.5rem 0.75rem !important;
    border-radius: 0.375rem !important;
    border: 1px solid #d1d5db !important;
  }
}

// 标签页样式
.tabs-container {
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 2rem;

  .tabs-nav {
    display: flex;
    gap: 2rem;

    .tab-btn {
      padding: 1rem 0.25rem;
      border: none;
      background: none;
      font-size: 1.125rem;
      font-weight: 500;
      color: #6b7280;
      border-bottom: 2px solid transparent;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        color: #374151;
        border-bottom-color: #d1d5db;
      }

      &.tab-active {
        color: #4f46e5;
        border-bottom-color: #4f46e5;
        font-weight: 600;
      }
    }
  }
}

// 标签页内容
.tab-content-container {
  .tab-content {
    &.income-panel {
      .income-cards {
        display: flex;
        flex-direction: column;
        gap: 2rem;
        width: 50%;
      }

      .income-card {
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;
        padding: 1.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

        .card-title {
          display: flex;
          align-items: center;
          font-size: 1.125rem;
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 1rem;

          .title-icon {
            width: 1.25rem;
            height: 1.25rem;
            margin-right: 0.5rem;
          }
        }
      }
    }

    &.expenses-panel {
      .expense-cards {
        display: flex;
        flex-direction: column;
        gap: 2rem;
        width: 50%;
      }
    }

    &.cash-panel {
      .cash-cards {
        display: flex;
        flex-direction: column;
        gap: 2rem;
        width: 50%;
      }
    }

    &.actual-panel {
      .actual-balance-card {
        display: flex;
        justify-content: center;
        padding: 2rem 0;

        .balance-card {
          background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
          border: 1px solid #b7eb8f;
          border-radius: 1rem;
          padding: 2rem;
          width: 100%;
          max-width: 600px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

          .card-title {
            display: flex;
            align-items: center;
            font-size: 1.5rem;
            font-weight: 600;
            color: #52c41a;
            margin-bottom: 2rem;
            justify-content: center;

            .title-icon {
              width: 2rem;
              height: 2rem;
              margin-right: 0.75rem;
            }
          }

          .balance-input-container {
            margin-bottom: 2rem;
          }

          .balance-comparison {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 0.75rem;
            padding: 1.5rem;
            border: 1px solid #d9f7be;

            .comparison-item {
              text-align: center;

              .comparison-label {
                font-size: 0.875rem;
                color: #6b7280;
                margin-bottom: 0.5rem;
                font-weight: 500;
              }

              .comparison-value {
                font-size: 1.25rem;
                font-weight: 600;

                &.expected {
                  color: #fa8c16;
                }

                &.difference {
                  &.positive {
                    color: #52c41a;
                  }

                  &.negative {
                    color: #f5222d;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

.finance-record-form {
  // 防止水平滚动
  * {
    box-sizing: border-box;
  }

  padding: $spacing-lg;
  height: calc(100vh - 60px); // 减去头部导航的高度
  display: flex;
  flex-direction: column;
  overflow: hidden; // 防止整个页面滚动
  box-sizing: border-box; // 确保 padding 包含在总宽度内

  .form-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; // 重要：允许 flex 子项收缩

    :deep(.el-card__header) {
      padding: $spacing-base $spacing-lg;
      flex-shrink: 0; // 头部不收缩
    }

    :deep(.el-card__body) {
      padding: $spacing-base $spacing-lg;
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden; // 防止卡片内容溢出
      min-height: 0; // 重要：允许 flex 子项收缩
    }

    .card-header {
      display: flex;
      align-items: center;
      gap: $spacing-base;

      .back-button {
        padding: $spacing-xs $spacing-sm;
        color: $text-color-secondary;

        &:hover {
          color: $primary-color;
          background-color: rgba($primary-color, 0.1);
        }
      }

      .title {
        font-size: $font-size-lg;
        font-weight: $font-weight-semibold;
        color: $text-color-primary;
      }
    }
  }

  // 备注区域样式
  .remark-section {
    margin: $spacing-lg 0;

    .remark-card {
      background: white;
      border: 1px solid #e5e7eb;
      border-radius: 0.5rem;
      padding: 1.5rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
  }

  // 财务总结区域样式 - 粘性定位
  .summary-section {
    margin: $spacing-lg 0;

    &.sticky-summary {
      position: sticky;
      top: 0;
      z-index: 100;
      margin: $spacing-lg 0 $spacing-xl 0;

      .summary-container {
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;
        padding: 1rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
          0 2px 4px -1px rgba(0, 0, 0, 0.06);
        backdrop-filter: blur(8px);

        .summary-title {
          display: flex;
          align-items: center;
          font-size: 1.25rem;
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 1rem;
          padding-bottom: 0.75rem;
          border-bottom: 1px solid #f3f4f6;

          .title-icon {
            width: 1.5rem;
            height: 1.5rem;
            margin-right: 0.5rem;
          }
        }

        .summary-cards {
          .el-col {
            margin-bottom: 0;
          }
        }
      }

      // 响应式调整
      @include mobile {
        position: relative;
        top: auto;

        .summary-container {
          padding: 1rem;

          .summary-title {
            font-size: 1.125rem;
          }

          .summary-cards {
            .el-col {
              margin-bottom: 1rem;

              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }
      }
    }
  }

  .balance-card {
    background: linear-gradient(135deg, #f6f9fc 0%, #e9f4ff 100%);
    border: 1px solid #e1f0ff;
    width: 100%;
    max-width: 100%;

    .balance-info {
      display: flex;
      justify-content: space-around;
      align-items: center;
      padding: $spacing-base 0;

      @include mobile {
        flex-direction: column;
        gap: $spacing-sm;
      }

      .balance-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: $spacing-xs;

        .label {
          font-size: $font-size-sm;
          color: $text-color-secondary;
          font-weight: $font-weight-medium;
        }

        .value {
          font-size: $font-size-lg;
          font-weight: $font-weight-bold;

          &.income {
            color: $success-color;
          }

          &.expense {
            color: $error-color;
          }
        }
      }
    }
  }

  .form-actions {
    display: flex;
    justify-content: center;
    gap: $spacing-base;
    padding-top: $spacing-lg;
    border-top: 1px solid $border-color-light;
    background-color: $background-color-primary;
  }

  // 表单容器样式
  .form-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    overflow-x: hidden; // 防止水平滚动
    min-height: 0; // 重要：允许 flex 子项收缩
    width: 100%; // 确保不超出容器宽度
    scroll-behavior: smooth; // 平滑滚动

    // 表单内容区域
    > .el-row {
      width: 100%;
      max-width: 100%;

      &:not(:last-child) {
        margin-bottom: $spacing-base;
      }

      // 最后一行（操作按钮）不参与滚动
      &:last-child {
        margin-top: auto;
        flex-shrink: 0;
      }
    }
  }

  // 表单项样式优化
  :deep(.el-form-item) {
    margin-bottom: $spacing-base;

    .el-form-item__label {
      font-weight: $font-weight-medium;
      color: $text-color-primary;
    }
  }

  :deep(.el-input-number) {
    width: 100% !important;
    max-width: 100%;
  }

  :deep(.el-date-editor) {
    width: 100% !important;
    max-width: 100%;
  }

  :deep(.el-textarea) {
    width: 100% !important;
    max-width: 100%;
  }

  :deep(.el-divider) {
    margin: $spacing-lg 0 $spacing-base 0;

    .el-divider__text {
      font-weight: $font-weight-semibold;
      color: $text-color-primary;
      background-color: $background-color-primary;
    }
  }

  // 响应式设计
  @include mobile {
    padding: $spacing-sm;
    height: calc(100vh - 60px);
    overflow: hidden;

    .form-card {
      height: 100%;
      min-height: 0;

      :deep(.el-card__header) {
        padding: $spacing-sm $spacing-base;
      }

      :deep(.el-card__body) {
        padding: $spacing-sm $spacing-base;
      }
    }

    :deep(.el-col) {
      &.el-col-12 {
        flex: 0 0 100%;
        max-width: 100%;
      }
    }

    .balance-card {
      .balance-info {
        .balance-item {
          .label {
            font-size: $font-size-xs;
          }
          .value {
            font-size: $font-size-base;
          }
        }
      }
    }

    .form-actions {
      flex-direction: column;
      padding: $spacing-base;

      .el-button {
        width: 100%;
      }
    }
  }
}
</style>
