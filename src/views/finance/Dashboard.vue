<template>
  <AppLayout>
    <div class="finance-dashboard">
      <!-- 页面头部 -->
      <div class="dashboard-header">
        <div class="header-left">
          <h1 class="dashboard-title">
            <el-icon class="title-icon">
              <DataBoard />
            </el-icon>
            财务仪表板
          </h1>
          <p class="dashboard-subtitle">
            {{ currentPeriodData?.period || "暂无数据" }} 财务数据概览
          </p>
        </div>

        <div class="header-controls">
          <!-- 周期选择器 -->
          <div class="period-selector">
            <span class="selector-label">选择周期:</span>
            <el-select
              v-model="selectedPeriod"
              @change="handlePeriodChange"
              style="width: 250px"
              placeholder="请选择周期"
            >
              <el-option
                v-for="record in financeRecords"
                :key="record.id"
                :label="record.period"
                :value="record.id"
              />
            </el-select>
          </div>

          <el-button type="primary" @click="refreshData" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>

      <!-- 仪表板内容 -->
      <div class="dashboard-content" v-loading="loading">
        <!-- 统计卡片 -->
        <div class="stats-row">
          <el-card class="stat-card income">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">¥{{ formatCurrency(totalIncome) }}</div>
                <div class="stat-label">总收入</div>
                <div
                  class="stat-trend"
                  :class="incomeChange >= 0 ? 'positive' : 'negative'"
                >
                  <el-icon>
                    <ArrowUp v-if="incomeChange >= 0" />
                    <ArrowDown v-else />
                  </el-icon>
                  <span>{{ Math.abs(incomeChange).toFixed(1) }}%</span>
                </div>
              </div>
            </div>
          </el-card>

          <el-card class="stat-card expense">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Wallet /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">
                  ¥{{ formatCurrency(totalExpense) }}
                </div>
                <div class="stat-label">总支出</div>
                <div
                  class="stat-trend"
                  :class="expenseChange >= 0 ? 'negative' : 'positive'"
                >
                  <el-icon>
                    <ArrowUp v-if="expenseChange >= 0" />
                    <ArrowDown v-else />
                  </el-icon>
                  <span>{{ Math.abs(expenseChange).toFixed(1) }}%</span>
                </div>
              </div>
            </div>
          </el-card>

          <el-card class="stat-card balance">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Money /></el-icon>
              </div>
              <div class="stat-info">
                <div
                  class="stat-value"
                  :class="totalBalance >= 0 ? 'positive' : 'negative'"
                >
                  ¥{{ formatCurrency(totalBalance) }}
                </div>
                <div class="stat-label">结余</div>
                <div
                  class="stat-trend"
                  :class="balanceChange >= 0 ? 'positive' : 'negative'"
                >
                  <el-icon>
                    <ArrowUp v-if="balanceChange >= 0" />
                    <ArrowDown v-else />
                  </el-icon>
                  <span>{{ Math.abs(balanceChange).toFixed(1) }}%</span>
                </div>
              </div>
            </div>
          </el-card>

          <el-card class="stat-card savings-rate">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><PieChart /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value" :class="savingsRateClass">
                  {{ savingsRate.toFixed(1) }}%
                </div>
                <div class="stat-label">储蓄率</div>
                <div
                  class="stat-trend"
                  :class="savingsRateChange >= 0 ? 'positive' : 'negative'"
                >
                  <el-icon>
                    <ArrowUp v-if="savingsRateChange >= 0" />
                    <ArrowDown v-else />
                  </el-icon>
                  <span>{{ Math.abs(savingsRateChange).toFixed(1) }}%</span>
                </div>
              </div>
            </div>
          </el-card>

          <el-card class="stat-card assets">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><CreditCard /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">¥{{ formatCurrency(totalAssets) }}</div>
                <div class="stat-label">总资产</div>
                <div
                  class="stat-trend"
                  :class="assetsChange >= 0 ? 'positive' : 'negative'"
                >
                  <el-icon>
                    <ArrowUp v-if="assetsChange >= 0" />
                    <ArrowDown v-else />
                  </el-icon>
                  <span>{{ Math.abs(assetsChange).toFixed(1) }}%</span>
                </div>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 图表区域 -->
        <div class="chart-row">
          <div class="chart-card-wrapper">
            <IncomeExpenseChart
              v-if="currentPeriodData"
              :data="[currentPeriodData]"
              :loading="loading"
            />
          </div>

          <div class="chart-card-wrapper">
            <ExpenseCategoryChart
              v-if="currentPeriodData"
              :data="[currentPeriodData]"
              :loading="loading"
              :show-period-selector="false"
              :selected-period-prop="currentPeriodData.period"
            />
          </div>
        </div>

        <!-- 月度现金流图表 - 占满整行 -->
        <div class="chart-row full-width">
          <div class="chart-card-wrapper full-width">
            <MonthlyCashFlowChart :data="financeRecords" :loading="loading" />
          </div>
        </div>

        <!-- 详细数据表格 -->
        <el-card class="data-table-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">
                <el-icon><Grid /></el-icon>
                当前周期详细数据
              </span>
            </div>
          </template>

          <el-table
            v-if="currentPeriodData"
            :data="tableData"
            border
            stripe
            style="width: 100%"
            :header-cell-style="{
              backgroundColor: '#fafafa',
              color: '#262626',
              fontWeight: '600',
              textAlign: 'center',
            }"
          >
            <el-table-column
              prop="category"
              label="项目类别"
              width="120"
              align="center"
            />
            <el-table-column
              prop="item"
              label="具体项目"
              width="150"
              align="center"
            />
            <el-table-column
              prop="amount"
              label="金额"
              width="120"
              align="right"
            >
              <template #default="{ row }">
                <span
                  :class="
                    row.type === 'income' ? 'income-text' : 'expense-text'
                  "
                >
                  {{ row.type === "income" ? "+" : "-" }}¥{{
                    formatCurrency(row.amount)
                  }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              prop="type"
              label="类型"
              width="100"
              align="center"
            >
              <template #default="{ row }">
                <el-tag
                  :type="row.type === 'income' ? 'success' : 'danger'"
                  size="small"
                >
                  {{ row.type === "income" ? "收入" : "支出" }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              prop="percentage"
              label="占比"
              width="100"
              align="center"
            >
              <template #default="{ row }">
                <span class="percentage">{{ row.percentage }}%</span>
              </template>
            </el-table-column>
          </el-table>

          <div v-else class="no-data">
            <el-empty description="请选择一个周期查看详细数据" />
          </div>
        </el-card>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import {
  DataBoard,
  Refresh,
  TrendCharts,
  Wallet,
  Money,
  CreditCard,
  ArrowUp,
  ArrowDown,
  Grid,
  PieChart,
} from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import AppLayout from "../../components/AppLayout.vue";
import IncomeExpenseChart from "../../components/finance/charts/IncomeExpenseChart.vue";
import ExpenseCategoryChart from "../../components/finance/charts/ExpenseCategoryChart.vue";
import MonthlyCashFlowChart from "../../components/finance/charts/MonthlyCashFlowChart.vue";
import {
  getFinanceRecordList,
  type FinanceRecord,
  type PaginatedResponse,
} from "../../utils/mock/finance";

// 状态管理
const loading = ref(false);
const financeRecords = ref<FinanceRecord[]>([]);
const selectedPeriod = ref<string>("");

// 当前周期数据
const currentPeriodData = computed(() => {
  if (!selectedPeriod.value) return null;
  return financeRecords.value.find(
    (record) => record.id === selectedPeriod.value
  );
});

// 计算统计数据
const totalIncome = computed(() => {
  if (!currentPeriodData.value) return 0;
  return (
    (currentPeriodData.value.salary || 0) +
    (currentPeriodData.value.otherIncome || 0)
  );
});

const totalExpense = computed(() => {
  if (!currentPeriodData.value) return 0;
  const data = currentPeriodData.value;
  return (
    (data.juan || 0) +
    (data.wechat || 0) +
    (data.huabei || 0) +
    (data.spdbCreditCard || 0) +
    (data.commCreditCard || 0) +
    (data.cmCreditCard || 0) +
    (data.jdBaitiao || 0) +
    (data.jdJintiao || 0) +
    (data.rent || 0) +
    (data.mortgage || 0)
  );
});

const totalBalance = computed(() => totalIncome.value - totalExpense.value);

const totalAssets = computed(() => {
  if (!currentPeriodData.value) return 0;
  return (
    (currentPeriodData.value.alipay || 0) +
    (currentPeriodData.value.wechatPay || 0)
  );
});

// 储蓄率计算（结余/收入 * 100%）
const savingsRate = computed(() => {
  if (totalIncome.value === 0) return 0;
  return (totalBalance.value / totalIncome.value) * 100;
});

// 储蓄率样式类
const savingsRateClass = computed(() => {
  if (savingsRate.value >= 30) return "excellent";
  if (savingsRate.value >= 20) return "positive";
  if (savingsRate.value >= 10) return "warning";
  return "negative";
});

// 计算变化率（相对于上一个周期）
const previousPeriodData = computed(() => {
  if (!selectedPeriod.value || financeRecords.value.length < 2) return null;
  const currentIndex = financeRecords.value.findIndex(
    (record) => record.id === selectedPeriod.value
  );
  if (currentIndex <= 0) return null;
  return financeRecords.value[currentIndex - 1];
});

const incomeChange = computed(() => {
  if (!previousPeriodData.value) return 0;
  const prevIncome =
    (previousPeriodData.value.salary || 0) +
    (previousPeriodData.value.otherIncome || 0);
  if (prevIncome === 0) return 0;
  return ((totalIncome.value - prevIncome) / prevIncome) * 100;
});

const expenseChange = computed(() => {
  if (!previousPeriodData.value) return 0;
  const prevExpense = calculateTotalExpense(previousPeriodData.value);
  if (prevExpense === 0) return 0;
  return ((totalExpense.value - prevExpense) / prevExpense) * 100;
});

const balanceChange = computed(() => {
  if (!previousPeriodData.value) return 0;
  const prevBalance =
    calculateTotalIncome(previousPeriodData.value) -
    calculateTotalExpense(previousPeriodData.value);
  if (prevBalance === 0) return totalBalance.value > 0 ? 100 : -100;
  return ((totalBalance.value - prevBalance) / Math.abs(prevBalance)) * 100;
});

const assetsChange = computed(() => {
  if (!previousPeriodData.value) return 0;
  const prevAssets =
    (previousPeriodData.value.alipay || 0) +
    (previousPeriodData.value.wechatPay || 0);
  if (prevAssets === 0) return 0;
  return ((totalAssets.value - prevAssets) / prevAssets) * 100;
});

const savingsRateChange = computed(() => {
  if (!previousPeriodData.value) return 0;
  const prevIncome = calculateTotalIncome(previousPeriodData.value);
  const prevExpense = calculateTotalExpense(previousPeriodData.value);
  const prevBalance = prevIncome - prevExpense;
  const prevSavingsRate =
    prevIncome === 0 ? 0 : (prevBalance / prevIncome) * 100;

  return savingsRate.value - prevSavingsRate;
});

// 表格数据
const tableData = computed(() => {
  if (!currentPeriodData.value) return [];

  const data = currentPeriodData.value;
  const items = [];

  // 收入项目
  if (data.salary) {
    items.push({
      category: "收入",
      item: "工资",
      amount: data.salary,
      type: "income",
      percentage: ((data.salary / totalIncome.value) * 100).toFixed(1),
    });
  }

  if (data.otherIncome) {
    items.push({
      category: "收入",
      item: "其他收入",
      amount: data.otherIncome,
      type: "income",
      percentage: ((data.otherIncome / totalIncome.value) * 100).toFixed(1),
    });
  }

  // 支出项目
  const expenseItems = [
    { key: "juan", name: "娟" },
    { key: "wechat", name: "微信" },
    { key: "huabei", name: "花呗" },
    { key: "spdbCreditCard", name: "浦发信用卡" },
    { key: "commCreditCard", name: "交通信用卡" },
    { key: "cmCreditCard", name: "招商信用卡" },
    { key: "jdBaitiao", name: "京东白条" },
    { key: "jdJintiao", name: "京东金条" },
    { key: "rent", name: "房租" },
    { key: "mortgage", name: "房贷" },
  ];

  expenseItems.forEach((item) => {
    const value = (data as any)[item.key];
    if (value && value > 0) {
      items.push({
        category: "支出",
        item: item.name,
        amount: value,
        type: "expense",
        percentage: ((value / totalExpense.value) * 100).toFixed(1),
      });
    }
  });

  return items.sort((a, b) => b.amount - a.amount);
});

// 辅助函数
const formatCurrency = (value: number): string => {
  if (value % 1 === 0) {
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  }
  return value.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
};

const calculateTotalIncome = (record: FinanceRecord): number => {
  return (record.salary || 0) + (record.otherIncome || 0);
};

const calculateTotalExpense = (record: FinanceRecord): number => {
  return (
    (record.juan || 0) +
    (record.wechat || 0) +
    (record.huabei || 0) +
    (record.spdbCreditCard || 0) +
    (record.commCreditCard || 0) +
    (record.cmCreditCard || 0) +
    (record.jdBaitiao || 0) +
    (record.jdJintiao || 0) +
    (record.rent || 0) +
    (record.mortgage || 0)
  );
};

// 事件处理
const handlePeriodChange = (value: string) => {
  selectedPeriod.value = value;
  ElMessage.success("周期切换成功");
};

const refreshData = async () => {
  try {
    loading.value = true;
    await loadFinanceData();
    ElMessage.success("数据刷新成功");
  } catch (error) {
    console.error("刷新数据失败:", error);
    ElMessage.error("数据刷新失败");
  } finally {
    loading.value = false;
  }
};

// 加载财务数据
const loadFinanceData = async () => {
  try {
    const response = await getFinanceRecordList({ pageSize: 100 }); // 获取所有数据
    const records = response.records || [];
    financeRecords.value = records.sort((a, b) =>
      b.period.localeCompare(a.period)
    );

    // 默认选择最新的周期
    if (records.length > 0 && !selectedPeriod.value) {
      selectedPeriod.value = records[0].id;
    }
  } catch (error) {
    console.error("加载财务数据失败:", error);
    ElMessage.error("加载数据失败");
  }
};

// 页面加载时获取数据
onMounted(() => {
  loadFinanceData();
});
</script>

<style lang="scss" scoped>
.finance-dashboard {
  padding: 20px;
  min-height: 100vh;
  background: #f5f5f5;

  .dashboard-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: white;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-left {
      .dashboard-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 24px;
        font-weight: 600;
        color: #262626;
        margin: 0 0 8px 0;

        .title-icon {
          color: #1890ff;
        }
      }

      .dashboard-subtitle {
        color: #8c8c8c;
        font-size: 14px;
        margin: 0;
      }
    }

    .header-controls {
      display: flex;
      align-items: center;
      gap: 16px;

      .period-selector {
        display: flex;
        align-items: center;
        gap: 8px;

        .selector-label {
          font-size: 14px;
          color: #8c8c8c;
          white-space: nowrap;
        }
      }
    }
  }

  .dashboard-content {
    display: flex;
    flex-direction: column;
    gap: 20px;

    .stats-row {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: 20px;

      .stat-card {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        overflow: hidden;

        &.income {
          border-left: 4px solid #52c41a;
        }

        &.expense {
          border-left: 4px solid #ff4d4f;
        }

        &.balance {
          border-left: 4px solid #1890ff;
        }

        &.savings-rate {
          border-left: 4px solid #fa8c16;
        }

        &.assets {
          border-left: 4px solid #722ed1;
        }

        .stat-content {
          display: flex;
          align-items: center;
          gap: 16px;

          .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
          }
        }

        &.income .stat-content .stat-icon {
          background: #52c41a;
        }

        &.expense .stat-content .stat-icon {
          background: #ff4d4f;
        }

        &.balance .stat-content .stat-icon {
          background: #1890ff;
        }

        &.savings-rate .stat-content .stat-icon {
          background: #fa8c16;
        }

        &.assets .stat-content .stat-icon {
          background: #722ed1;

          .stat-info {
            flex: 1;

            .stat-value {
              font-size: 24px;
              font-weight: 600;
              margin-bottom: 4px;
              color: #262626;

              &.positive {
                color: #52c41a;
              }

              &.negative {
                color: #ff4d4f;
              }

              &.warning {
                color: #fa8c16;
              }

              &.excellent {
                color: #52c41a;
                font-weight: 700;
              }
            }

            .stat-label {
              color: #8c8c8c;
              font-size: 14px;
              margin-bottom: 8px;
            }

            .stat-trend {
              display: flex;
              align-items: center;
              gap: 4px;
              font-size: 12px;
              font-weight: 500;

              &.positive {
                color: #52c41a;
              }

              &.negative {
                color: #ff4d4f;
              }
            }
          }
        }
      }
    }

    .chart-row {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 20px;

      &.full-width {
        grid-template-columns: 1fr;
      }

      .chart-card {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border-radius: 8px;

        &.full-width {
          .chart-container {
            height: 450px;
          }
        }

        .card-header {
          .card-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 16px;
            font-weight: 600;
            color: #262626;
          }
        }

        .chart-container {
          height: 400px;
          padding: 0;
        }
      }

      .chart-card-wrapper {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        overflow: hidden;

        &.full-width {
          :deep(.monthly-cash-flow-chart) {
            .chart-container {
              height: 450px;
            }
          }
        }

        :deep(.expense-category-chart),
        :deep(.income-expense-chart) {
          .chart-container {
            height: 400px;
          }
        }
      }
    }

    .data-table-card {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border-radius: 8px;

      .card-header {
        .card-title {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 16px;
          font-weight: 600;
          color: #262626;
        }
      }

      .no-data {
        padding: 40px;
        text-align: center;
      }

      :deep(.el-table) {
        .income-text {
          color: #52c41a;
          font-weight: 600;
        }

        .expense-text {
          color: #ff4d4f;
          font-weight: 600;
        }

        .percentage {
          color: #8c8c8c;
          font-size: 12px;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1400px) {
  .finance-dashboard {
    .dashboard-content {
      .stats-row {
        grid-template-columns: repeat(3, 1fr);
      }

      .chart-row {
        grid-template-columns: 1fr;
      }
    }
  }
}

@media (max-width: 1000px) {
  .finance-dashboard {
    .dashboard-content {
      .stats-row {
        grid-template-columns: repeat(2, 1fr);
      }
    }
  }
}

@media (max-width: 768px) {
  .finance-dashboard {
    padding: 16px;

    .dashboard-header {
      flex-direction: column;
      gap: 16px;
      align-items: flex-start;

      .header-controls {
        width: 100%;
        justify-content: space-between;
      }
    }

    .dashboard-content {
      .stats-row {
        grid-template-columns: 1fr;
      }
    }
  }
}
</style>
