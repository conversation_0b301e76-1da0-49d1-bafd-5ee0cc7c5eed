<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-20 00:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-20 23:50:00
 * @FilePath     : /src/views/finance/ComparisonView.vue
 * @Description  : 财务数据对比分析页面
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-20 00:00:00
-->

<template>
  <AppLayout>
    <div class="comparison-view">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-content">
          <div class="title-section">
            <el-button type="text" @click="goBack" class="back-button">
              <el-icon><ArrowLeft /></el-icon>
              返回列表
            </el-button>
            <h1 class="page-title">
              <el-icon class="title-icon">
                <TrendCharts />
              </el-icon>
              财务数据对比分析
            </h1>
          </div>

          <div class="selected-periods">
            <span class="periods-label">对比周期:</span>
            <div class="periods-list">
              <el-tag
                v-for="record in comparisonData"
                :key="record.id"
                type="primary"
                class="period-tag"
              >
                {{ record.period }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 图表网格 -->
      <div class="charts-grid" v-loading="loading">
        <div class="chart-row">
          <div class="chart-item">
            <IncomeExpenseChart :data="comparisonData" :loading="loading" />
          </div>
          <div class="chart-item">
            <AssetTrendChart :data="comparisonData" :loading="loading" />
          </div>
        </div>

        <div class="chart-row">
          <div class="chart-item">
            <ExpenseCategoryChart :data="comparisonData" :loading="loading" />
          </div>
          <div class="chart-item">
            <ComprehensiveRadar :data="comparisonData" :loading="loading" />
          </div>
        </div>

        <!-- 项目逐项对比图表 - 占满整行 -->
        <div class="chart-row full-width">
          <div class="chart-item full-width">
            <ItemComparisonChart :data="comparisonData" :loading="loading" />
          </div>
        </div>
      </div>

      <!-- 详细数据表格 -->
      <div class="data-table-section">
        <el-card class="table-card">
          <template #header>
            <div class="table-header">
              <h3 class="table-title">
                <el-icon class="title-icon">
                  <Grid />
                </el-icon>
                详细数据对比
              </h3>
              <div class="table-actions">
                <el-button @click="exportData">
                  <el-icon><Download /></el-icon>
                  导出数据
                </el-button>
              </div>
            </div>
          </template>

          <el-table
            :data="comparisonData"
            border
            stripe
            style="width: 100%"
            :header-cell-style="{
              backgroundColor: '#fafafa',
              color: '#262626',
              fontWeight: '600',
              textAlign: 'center',
            }"
          >
            <el-table-column
              prop="period"
              label="记账周期"
              width="150"
              align="center"
              fixed="left"
            />

            <el-table-column label="收入" align="center">
              <el-table-column
                prop="salary"
                label="工资"
                width="120"
                align="right"
              >
                <template #default="{ row }">
                  <span class="income-text"
                    >+{{ formatCurrency(row.salary || 0) }}</span
                  >
                </template>
              </el-table-column>
              <el-table-column
                prop="otherIncome"
                label="其他收入"
                width="120"
                align="right"
              >
                <template #default="{ row }">
                  <span class="income-text"
                    >+{{ formatCurrency(row.otherIncome || 0) }}</span
                  >
                </template>
              </el-table-column>
            </el-table-column>

            <el-table-column label="支出" align="center">
              <el-table-column
                prop="totalExpense"
                label="总支出"
                width="120"
                align="right"
              >
                <template #default="{ row }">
                  <span class="expense-text"
                    >-{{ formatCurrency(calculateTotalExpense(row)) }}</span
                  >
                </template>
              </el-table-column>
            </el-table-column>

            <el-table-column label="资产" align="center">
              <el-table-column
                prop="alipay"
                label="支付宝"
                width="120"
                align="right"
              >
                <template #default="{ row }">
                  {{ formatCurrency(row.alipay || 0) }}
                </template>
              </el-table-column>
              <el-table-column
                prop="wechatPay"
                label="微信支付"
                width="120"
                align="right"
              >
                <template #default="{ row }">
                  {{ formatCurrency(row.wechatPay || 0) }}
                </template>
              </el-table-column>
              <el-table-column
                prop="actualBalance"
                label="实际结余"
                width="120"
                align="right"
              >
                <template #default="{ row }">
                  <span
                    :class="
                      row.actualBalance >= 0 ? 'income-text' : 'expense-text'
                    "
                  >
                    {{ formatCurrency(row.actualBalance || 0) }}
                  </span>
                </template>
              </el-table-column>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import {
  ArrowLeft,
  TrendCharts,
  Grid,
  Download,
} from "@element-plus/icons-vue";
import AppLayout from "../../components/AppLayout.vue";
import IncomeExpenseChart from "../../components/finance/charts/IncomeExpenseChart.vue";
import AssetTrendChart from "../../components/finance/charts/AssetTrendChart.vue";
import ExpenseCategoryChart from "../../components/finance/charts/ExpenseCategoryChart.vue";
import ComprehensiveRadar from "../../components/finance/charts/ComprehensiveRadar.vue";
import ItemComparisonChart from "../../components/finance/charts/ItemComparisonChart.vue";
import {
  getFinanceRecordById,
  type FinanceRecord,
} from "../../utils/mock/finance";

const route = useRoute();
const router = useRouter();

// 状态
const loading = ref(true);
const comparisonData = ref<FinanceRecord[]>([]);

// 格式化货币
const formatCurrency = (value: number): string => {
  if (value % 1 === 0) {
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  }
  return value.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
};

// 计算总支出
const calculateTotalExpense = (record: FinanceRecord): number => {
  return (
    (record.juan || 0) +
    (record.wechat || 0) +
    (record.huabei || 0) +
    (record.spdbCreditCard || 0) +
    (record.commCreditCard || 0) +
    (record.cmCreditCard || 0) +
    (record.jdBaitiao || 0) +
    (record.jdJintiao || 0) +
    (record.rent || 0) +
    (record.mortgage || 0)
  );
};

// 返回列表页
const goBack = () => {
  router.push("/finance/list");
};

// 导出数据
const exportData = () => {
  ElMessage.success("数据导出功能将在后续版本中实现");
};

// 加载对比数据
const loadComparisonData = async () => {
  try {
    loading.value = true;
    const ids = (route.query.ids as string)?.split(",") || [];

    if (ids.length < 2) {
      ElMessage.error("请至少选择2个记录进行对比");
      goBack();
      return;
    }

    const records: FinanceRecord[] = [];
    for (const id of ids) {
      try {
        const record = await getFinanceRecordById(id);
        if (record) {
          records.push(record);
        }
      } catch (error) {
        console.warn(`获取记录 ${id} 失败:`, error);
      }
    }

    if (records.length < 2) {
      ElMessage.error("获取对比数据失败，请重新选择");
      goBack();
      return;
    }

    // 按周期排序
    comparisonData.value = records.sort((a, b) =>
      a.period.localeCompare(b.period)
    );
  } catch (error) {
    console.error("加载对比数据失败:", error);
    ElMessage.error("加载数据失败，请重试");
    goBack();
  } finally {
    loading.value = false;
  }
};

// 页面加载时获取数据
onMounted(() => {
  loadComparisonData();
});
</script>

<style lang="scss" scoped>
.comparison-view {
  padding: 20px;
  min-height: 100vh;
  background: #f5f5f5;

  .page-header {
    background: white;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-content {
      .title-section {
        display: flex;
        align-items: center;
        gap: 16px;
        margin-bottom: 16px;

        .back-button {
          display: flex;
          align-items: center;
          gap: 4px;
          color: #1890ff;

          &:hover {
            color: #40a9ff;
          }
        }

        .page-title {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 24px;
          font-weight: 600;
          color: #262626;
          margin: 0;

          .title-icon {
            color: #1890ff;
          }
        }
      }

      .selected-periods {
        display: flex;
        align-items: center;
        gap: 12px;

        .periods-label {
          font-size: 14px;
          color: #8c8c8c;
          font-weight: 500;
        }

        .periods-list {
          display: flex;
          gap: 8px;
          flex-wrap: wrap;

          .period-tag {
            font-weight: 500;
          }
        }
      }
    }
  }

  .charts-grid {
    margin-bottom: 20px;

    .chart-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      &.full-width {
        grid-template-columns: 1fr;
      }

      .chart-item {
        min-height: 480px;

        &.full-width {
          min-height: 520px;
        }
      }
    }
  }

  .data-table-section {
    .table-card {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .table-header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .table-title {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 16px;
          font-weight: 600;
          color: #262626;
          margin: 0;

          .title-icon {
            color: #1890ff;
          }
        }

        .table-actions {
          display: flex;
          gap: 8px;
        }
      }
    }

    :deep(.el-table) {
      .income-text {
        color: #52c41a;
        font-weight: 600;
      }

      .expense-text {
        color: #ff4d4f;
        font-weight: 600;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .comparison-view {
    .charts-grid {
      .chart-row {
        grid-template-columns: 1fr;
      }
    }
  }
}
</style>
