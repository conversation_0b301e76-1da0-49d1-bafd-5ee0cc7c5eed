<template>
  <AppLayout>
    <div class="finance-list">
      <el-card class="list-card">
        <template #header>
          <div class="card-header">
            <span class="title">资金流转记录表</span>
            <div class="operations">
              <el-button type="primary" @click="handleAddRecord">
                <el-icon><Plus /></el-icon>新增记录
              </el-button>
              <el-button
                type="success"
                @click="handleComparison"
                :disabled="selectedRecords.length < 2"
              >
                <el-icon><TrendCharts /></el-icon>对比分析 ({{
                  selectedRecords.length
                }})
              </el-button>
              <el-button @click="handleExport">
                <el-icon><Download /></el-icon>导出
              </el-button>
            </div>
          </div>
        </template>

        <div class="filter-bar">
          <el-form :model="filterForm" inline>
            <el-form-item label="记账周期">
              <el-select
                v-model="filterForm.cycle"
                placeholder="选择周期"
                clearable
              >
                <el-option
                  v-for="item in cycleOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="日期范围">
              <el-date-picker
                v-model="filterForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
            <el-form-item label="账户类型">
              <el-select
                v-model="filterForm.accountType"
                placeholder="选择账户类型"
                clearable
              >
                <el-option label="银行卡" value="bank" />
                <el-option label="信用卡" value="credit" />
                <el-option label="其他信用账户" value="other_credit" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">
                <el-icon><Search /></el-icon>搜索
              </el-button>
              <el-button @click="resetFilters">
                <el-icon><Refresh /></el-icon>重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 资金流转记录表格 -->
        <el-table
          :data="tableData"
          style="width: 100%"
          border
          :header-cell-style="{
            color: '#1D2129',
            fontWeight: '600',
            textAlign: 'center',
            borderBottom: '2px solid #DCDFE6',
          }"
          row-key="id"
          v-loading="loading"
          :header-cell-class-name="getHeaderCellClass"
          :scroll-x="true"
          @selection-change="handleSelectionChange"
        >
          <!-- 多选列 -->
          <el-table-column
            type="selection"
            width="55"
            align="center"
            fixed="left"
          />
          <el-table-column
            prop="period"
            label="记账周期"
            width="200"
            align="center"
            fixed="left"
          >
            <template #default="scope">
              <div v-if="scope && scope.row" class="period-cell">
                <span>{{ scope.row.period }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column
            label="收入"
            align="center"
            header-cell-class-name="income-header"
          >
            <el-table-column
              prop="salary"
              label="工资"
              min-width="100"
              align="right"
              header-cell-class-name="income-header"
            >
              <template #default="scope">
                <span
                  v-if="scope && scope.row && scope.row.salary"
                  class="income-text"
                  >+{{ formatCurrency(scope.row.salary) }}</span
                >
              </template>
            </el-table-column>

            <el-table-column
              prop="otherIncome"
              label="其他"
              min-width="100"
              align="right"
              header-cell-class-name="income-header"
            >
              <template #default="scope">
                <span
                  v-if="scope && scope.row && scope.row.otherIncome"
                  class="income-text"
                  >+{{ formatCurrency(scope.row.otherIncome) }}</span
                >
              </template>
            </el-table-column>
          </el-table-column>

          <el-table-column
            label="支出"
            align="center"
            header-cell-class-name="expense-header"
          >
            <el-table-column
              prop="juan"
              label="娟"
              min-width="80"
              align="right"
              header-cell-class-name="expense-header"
            >
              <template #default="scope">
                <span
                  v-if="scope && scope.row && scope.row.juan"
                  class="expense-text"
                  >-{{ formatCurrency(scope.row.juan) }}</span
                >
              </template>
            </el-table-column>

            <el-table-column
              prop="wechat"
              label="微信"
              min-width="80"
              align="right"
              header-cell-class-name="expense-header"
            >
              <template #default="scope">
                <span
                  v-if="scope && scope.row && scope.row.wechat"
                  class="expense-text"
                  >-{{ formatCurrency(scope.row.wechat) }}</span
                >
              </template>
            </el-table-column>

            <el-table-column
              prop="huabei"
              label="花呗"
              min-width="80"
              align="right"
              header-cell-class-name="expense-header"
            >
              <template #default="scope">
                <span
                  v-if="scope && scope.row && scope.row.huabei"
                  class="expense-text"
                  >-{{ formatCurrency(scope.row.huabei) }}</span
                >
              </template>
            </el-table-column>

            <el-table-column
              prop="spdbCreditCard"
              label="浦发信用卡"
              min-width="120"
              align="right"
              header-cell-class-name="expense-header"
            >
              <template #default="scope">
                <span
                  v-if="scope && scope.row && scope.row.spdbCreditCard"
                  class="expense-text"
                  >-{{ formatCurrency(scope.row.spdbCreditCard) }}</span
                >
              </template>
            </el-table-column>

            <el-table-column
              prop="commCreditCard"
              label="交行信用卡"
              min-width="120"
              align="right"
              header-cell-class-name="expense-header"
            >
              <template #default="scope">
                <span
                  v-if="scope && scope.row && scope.row.commCreditCard"
                  class="expense-text"
                  >-{{ formatCurrency(scope.row.commCreditCard) }}</span
                >
              </template>
            </el-table-column>

            <el-table-column
              prop="cmCreditCard"
              label="招商信用卡"
              min-width="120"
              align="right"
              header-cell-class-name="expense-header"
            >
              <template #default="scope">
                <span
                  v-if="scope && scope.row && scope.row.cmCreditCard"
                  class="expense-text"
                  >-{{ formatCurrency(scope.row.cmCreditCard) }}</span
                >
              </template>
            </el-table-column>

            <el-table-column
              prop="jdBaitiao"
              label="京东白条"
              min-width="100"
              align="right"
              header-cell-class-name="expense-header"
            >
              <template #default="scope">
                <span
                  v-if="scope && scope.row && scope.row.jdBaitiao"
                  class="expense-text"
                  >-{{ formatCurrency(scope.row.jdBaitiao) }}</span
                >
              </template>
            </el-table-column>

            <el-table-column
              prop="jdJintiao"
              label="京东金条"
              min-width="100"
              align="right"
              header-cell-class-name="expense-header"
            >
              <template #default="scope">
                <span
                  v-if="scope && scope.row && scope.row.jdJintiao"
                  class="expense-text"
                  >-{{ formatCurrency(scope.row.jdJintiao) }}</span
                >
              </template>
            </el-table-column>

            <el-table-column
              prop="rent"
              label="房租"
              min-width="80"
              align="right"
              header-cell-class-name="expense-header"
            >
              <template #default="scope">
                <span
                  v-if="scope && scope.row && scope.row.rent"
                  class="expense-text"
                  >-{{ formatCurrency(scope.row.rent) }}</span
                >
              </template>
            </el-table-column>

            <el-table-column
              prop="mortgage"
              label="房贷"
              min-width="80"
              align="right"
              header-cell-class-name="expense-header"
            >
              <template #default="scope">
                <span
                  v-if="scope && scope.row && scope.row.mortgage"
                  class="expense-text"
                  >-{{ formatCurrency(scope.row.mortgage) }}</span
                >
              </template>
            </el-table-column>

            <el-table-column
              label="总支出"
              min-width="120"
              align="right"
              header-cell-class-name="expense-header"
            >
              <template #default="scope">
                <span
                  v-if="scope && scope.row"
                  class="expense-text total-expense"
                  >-{{ formatCurrency(calculateTotalExpense(scope.row)) }}</span
                >
              </template>
            </el-table-column>
          </el-table-column>

          <el-table-column
            label="结余"
            align="center"
            header-cell-class-name="balance-header"
          >
            <el-table-column
              label="发薪后"
              min-width="100"
              align="right"
              header-cell-class-name="balance-header"
            >
              <template #default="scope">
                <span
                  v-if="scope && scope.row"
                  :class="
                    calculateBalanceAfterSalary(scope.row) >= 0
                      ? 'income-text'
                      : 'expense-text'
                  "
                  >{{ calculateBalanceAfterSalary(scope.row) >= 0 ? "+" : ""
                  }}{{
                    formatCurrency(calculateBalanceAfterSalary(scope.row))
                  }}</span
                >
              </template>
            </el-table-column>

            <el-table-column
              label="还款后"
              min-width="100"
              align="right"
              header-cell-class-name="balance-header"
            >
              <template #default="scope">
                <span
                  v-if="scope && scope.row"
                  :class="
                    calculateBalanceAfterPayment(scope.row) >= 0
                      ? 'income-text'
                      : 'expense-text'
                  "
                  >{{ calculateBalanceAfterPayment(scope.row) >= 0 ? "+" : ""
                  }}{{
                    formatCurrency(calculateBalanceAfterPayment(scope.row))
                  }}</span
                >
              </template>
            </el-table-column>

            <el-table-column
              label="余"
              min-width="100"
              align="right"
              header-cell-class-name="balance-header"
            >
              <template #default="scope">
                <span
                  v-if="scope && scope.row"
                  :class="
                    calculateFinalBalance(scope.row) >= 0
                      ? 'income-text'
                      : 'expense-text'
                  "
                  >{{ calculateFinalBalance(scope.row) >= 0 ? "+" : ""
                  }}{{ formatCurrency(calculateFinalBalance(scope.row)) }}</span
                >
              </template>
            </el-table-column>
          </el-table-column>

          <el-table-column
            label="操作"
            width="140"
            align="center"
            fixed="right"
            header-cell-class-name="operation-header"
          >
            <template #default="scope">
              <template v-if="scope && scope.row">
                <el-button type="primary" link @click="handleEdit(scope.row)">
                  <el-icon><Edit /></el-icon>编辑
                </el-button>
                <el-button type="danger" link @click="handleDelete(scope.row)">
                  <el-icon><Delete /></el-icon>删除
                </el-button>
              </template>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Plus,
  Download,
  Search,
  Refresh,
  Edit,
  Delete,
  TrendCharts,
} from "@element-plus/icons-vue";
import AppLayout from "../../components/AppLayout.vue";
import {
  getFinanceRecordList,
  deleteFinanceRecord,
  cycleOptions,
  type FinanceRecord,
} from "../../utils/mock/finance";

// 路由
const router = useRouter();

// 格式化货币
const formatCurrency = (value: number): string => {
  // 如果是整数，不显示小数部分
  if (value % 1 === 0) {
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  }
  // 如果有小数，显示小数部分（最多2位）
  return value.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
};

// 计算总支出
const calculateTotalExpense = (record: FinanceRecord): number => {
  return (
    (record.juan || 0) +
    (record.wechat || 0) +
    (record.huabei || 0) +
    (record.spdbCreditCard || 0) +
    (record.commCreditCard || 0) +
    (record.cmCreditCard || 0) +
    (record.jdBaitiao || 0) +
    (record.jdJintiao || 0) +
    (record.rent || 0) +
    (record.mortgage || 0)
  );
};

// 计算工资后结余
const calculateBalanceAfterSalary = (record: FinanceRecord): number => {
  const totalIncome = (record.salary || 0) + (record.otherIncome || 0);
  return totalIncome;
};

// 计算还款后结余
const calculateBalanceAfterPayment = (record: FinanceRecord): number => {
  const totalIncome = (record.salary || 0) + (record.otherIncome || 0);
  const totalExpense = calculateTotalExpense(record);
  return totalIncome - totalExpense;
};

// 计算最终结余
const calculateFinalBalance = (record: FinanceRecord): number => {
  return calculateBalanceAfterPayment(record);
};

// 重置过滤条件
const resetFilters = () => {
  filterForm.cycle = "";
  filterForm.dateRange = [];
  filterForm.accountType = "";
  handleSearch();
};

// 处理搜索
const handleSearch = async () => {
  try {
    loading.value = true;
    const response = await getFinanceRecordList({
      page: currentPage.value,
      pageSize: pageSize.value,
      cycle: filterForm.cycle,
      dateRange: filterForm.dateRange,
      accountType: filterForm.accountType,
    });

    tableData.value = response.records;
    total.value = response.total;
    ElMessage.success("搜索条件已应用");
  } catch (error) {
    console.error("获取资金流转记录失败:", error);
    ElMessage.error("获取数据失败，请重试");
  } finally {
    loading.value = false;
  }
};

// 处理分页大小变化
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  handleSearch();
};

// 处理页码变化
const handleCurrentChange = (val: number) => {
  currentPage.value = val;
  handleSearch();
};

// 处理添加记录
const handleAddRecord = () => {
  router.push("/finance/record/new");
};

// 处理编辑记录
const handleEdit = (row: FinanceRecord) => {
  router.push(`/finance/record/edit/${row.id}`);
};

// 处理删除记录
const handleDelete = async (row: FinanceRecord) => {
  try {
    await ElMessageBox.confirm("确定要删除这条记录吗？", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    loading.value = true;
    const response = await deleteFinanceRecord(row.id);

    if (response.success) {
      ElMessage.success("记录已删除");
      handleSearch(); // 重新加载数据
    } else {
      ElMessage.error("删除失败，请重试");
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除资金流转记录失败:", error);
      ElMessage.error("操作失败，请重试");
    }
  } finally {
    loading.value = false;
  }
};

// 处理导出
const handleExport = () => {
  ElMessage.success("数据导出功能将在后续版本中实现");
};

// 处理多选变化
const handleSelectionChange = (selection: FinanceRecord[]) => {
  selectedRecords.value = selection;
};

// 处理对比分析
const handleComparison = () => {
  if (selectedRecords.value.length < 2) {
    ElMessage.warning("请至少选择2个记录进行对比");
    return;
  }

  // 获取选中记录的ID
  const ids = selectedRecords.value.map((record) => record.id).join(",");

  // 跳转到对比页面
  router.push(`/finance/comparison?ids=${ids}`);
};

// 过滤表单
const filterForm = reactive({
  cycle: "",
  dateRange: [],
  accountType: "",
});

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const loading = ref(false);

// 表格数据
const tableData = ref<FinanceRecord[]>([]);

// 多选相关
const selectedRecords = ref<FinanceRecord[]>([]);

// 获取表头单元格类名
const getHeaderCellClass = ({ column }: { column: any }) => {
  if (column.property === "salary" || column.property === "otherIncome") {
    return "income-header";
  } else if (
    column.property === "juan" ||
    column.property === "wechat" ||
    column.property === "huabei" ||
    column.property === "spdbCreditCard" ||
    column.property === "commCreditCard" ||
    column.property === "cmCreditCard" ||
    column.property === "jdBaitiao" ||
    column.property === "jdJintiao" ||
    column.property === "rent" ||
    column.property === "mortgage"
  ) {
    return "expense-header";
  } else if (
    column.property === "balanceAfterSalary" ||
    column.property === "balanceAfterPayment" ||
    column.property === "finalBalance"
  ) {
    return "balance-header";
  } else if (column.property === "operation") {
    return "operation-header";
  }
  return "";
};

// 页面加载时获取数据
onMounted(async () => {
  await handleSearch();
});
</script>

<style lang="scss" scoped>
@import "@/styles/variables.scss";
@import "@/styles/mixins.scss";

.finance-list {
  padding: $spacing-lg;
  height: calc(100vh - 60px); // 减去头部导航的高度
  display: flex;
  flex-direction: column;

  .list-card {
    margin-bottom: $spacing-lg;
    flex: 1;
    display: flex;
    flex-direction: column;

    :deep(.el-card__header) {
      padding: $spacing-base $spacing-lg;
    }

    :deep(.el-card__body) {
      padding: $spacing-base $spacing-lg;
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: auto;
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      font-size: $font-size-lg;
      font-weight: $font-weight-semibold;
      color: $text-color-primary;
    }

    .operations {
      display: flex;
      gap: $spacing-sm;
    }
  }

  .filter-bar {
    margin-bottom: $spacing-lg;
    padding: $spacing-base;
    background-color: $background-color-secondary;
    border-radius: $border-radius-base;
  }

  .income-text {
    color: $success-color;
    font-weight: $font-weight-medium;
  }

  .expense-text {
    color: $error-color;
    font-weight: $font-weight-medium;

    &.total-expense {
      font-weight: 600;
      font-size: 14px;
      border: 1px solid $error-color;
      padding: 0 10px;
    }
  }

  .period-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  :deep(.el-table) {
    border: 1px solid #dcdfe6;
    flex: 1;

    .el-table__row {
      &:hover {
        background-color: #f7f8fa;
      }
    }

    .el-table__header th {
      border-right: 1px solid #dcdfe6;
      background-color: #f2f3f5; // 默认表头背景色
    }

    .el-table__cell {
      border-bottom: 1px solid #dcdfe6;
      white-space: nowrap;
    }

    // 确保固定列正确显示
    .el-table__fixed-right {
      right: 0 !important;
    }

    .el-table__fixed-left {
      left: 0 !important;
    }

    // 表格主体滚动区域
    .el-table__body-wrapper {
      overflow-x: auto;
    }

    // 自定义表头背景色 - 简化后的样式
    th.income-header {
      background-color: #e6f7ff !important; // 浅蓝色背景
    }

    th.expense-header {
      background-color: #fff2e8 !important; // 浅橙色背景
    }

    th.balance-header {
      background-color: #f6ffed !important; // 浅绿色背景
    }

    th.operation-header {
      background-color: #f2f2f2 !important; // 浅灰色背景
    }
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: $spacing-lg;
  }
}
</style>
