<template>
  <AppLayout>
    <div class="finance-settings">
      <a-card class="settings-card">
        <template #title>财务设置</template>

        <a-tabs default-active-key="1">
          <a-tab-pane key="1" title="基本设置">
            <a-form :model="basicForm" layout="vertical">
              <a-form-item field="currency" label="默认货币">
                <a-select
                  v-model="basicForm.currency"
                  placeholder="请选择默认货币"
                >
                  <a-option value="CNY">人民币 (CNY)</a-option>
                  <a-option value="USD">美元 (USD)</a-option>
                  <a-option value="EUR">欧元 (EUR)</a-option>
                  <a-option value="JPY">日元 (JPY)</a-option>
                  <a-option value="GBP">英镑 (GBP)</a-option>
                </a-select>
              </a-form-item>

              <a-form-item field="dateFormat" label="日期格式">
                <a-select
                  v-model="basicForm.dateFormat"
                  placeholder="请选择日期格式"
                >
                  <a-option value="YYYY-MM-DD">YYYY-MM-DD</a-option>
                  <a-option value="DD/MM/YYYY">DD/MM/YYYY</a-option>
                  <a-option value="MM/DD/YYYY">MM/DD/YYYY</a-option>
                </a-select>
              </a-form-item>

              <a-form-item field="startDayOfMonth" label="月初日期">
                <a-select
                  v-model="basicForm.startDayOfMonth"
                  placeholder="请选择月初日期"
                >
                  <a-option v-for="day in 31" :key="day" :value="day"
                    >{{ day }} 日</a-option
                  >
                </a-select>
                <div class="form-item-help">
                  设置每月的起始日期，用于月度统计
                </div>
              </a-form-item>

              <a-form-item field="budgetAlerts" label="预算提醒">
                <a-switch v-model="basicForm.budgetAlerts" />
                <div class="form-item-help">当支出接近预算限额时发送提醒</div>
              </a-form-item>

              <a-form-item>
                <a-button type="primary">保存基本设置</a-button>
              </a-form-item>
            </a-form>
          </a-tab-pane>

          <a-tab-pane key="2" title="分类管理">
            <div class="category-section">
              <div class="section-header">
                <h3>收入分类</h3>
                <a-button type="primary" size="small">
                  <template #icon><icon-plus /></template>
                  添加
                </a-button>
              </div>

              <a-table
                :columns="categoryColumns"
                :data="incomeCategories"
                :pagination="false"
              >
                <template #operations="{ record }">
                  <a-button type="text" size="small">
                    <template #icon><icon-edit /></template>
                    编辑
                  </a-button>
                  <a-button type="text" size="small" status="danger">
                    <template #icon><icon-delete /></template>
                    删除
                  </a-button>
                </template>
              </a-table>
            </div>

            <div class="category-section">
              <div class="section-header">
                <h3>支出分类</h3>
                <a-button type="primary" size="small">
                  <template #icon><icon-plus /></template>
                  添加
                </a-button>
              </div>

              <a-table
                :columns="categoryColumns"
                :data="expenseCategories"
                :pagination="false"
              >
                <template #operations="{ record }">
                  <a-button type="text" size="small">
                    <template #icon><icon-edit /></template>
                    编辑
                  </a-button>
                  <a-button type="text" size="small" status="danger">
                    <template #icon><icon-delete /></template>
                    删除
                  </a-button>
                </template>
              </a-table>
            </div>
          </a-tab-pane>

          <a-tab-pane key="3" title="预算设置">
            <a-form :model="budgetForm" layout="vertical">
              <a-form-item field="enableBudget" label="启用预算">
                <a-switch v-model="budgetForm.enableBudget" />
              </a-form-item>

              <template v-if="budgetForm.enableBudget">
                <a-form-item field="budgetType" label="预算周期">
                  <a-radio-group v-model="budgetForm.budgetType">
                    <a-radio value="monthly">月度预算</a-radio>
                    <a-radio value="yearly">年度预算</a-radio>
                  </a-radio-group>
                </a-form-item>

                <a-form-item field="totalBudget" label="总预算金额">
                  <a-input-number
                    v-model="budgetForm.totalBudget"
                    :min="0"
                    :step="100"
                    mode="button"
                    prefix="¥"
                  />
                </a-form-item>

                <div class="category-budgets">
                  <h3>分类预算</h3>
                  <div
                    v-for="(item, index) in budgetForm.categoryBudgets"
                    :key="index"
                    class="category-budget-item"
                  >
                    <a-form-item
                      :field="`categoryBudgets[${index}].category`"
                      label="分类"
                    >
                      <a-select
                        v-model="item.category"
                        placeholder="请选择分类"
                      >
                        <a-option value="food">餐饮</a-option>
                        <a-option value="transport">交通</a-option>
                        <a-option value="shopping">购物</a-option>
                        <a-option value="entertainment">娱乐</a-option>
                        <a-option value="housing">住房</a-option>
                      </a-select>
                    </a-form-item>

                    <a-form-item
                      :field="`categoryBudgets[${index}].amount`"
                      label="金额"
                    >
                      <a-input-number
                        v-model="item.amount"
                        :min="0"
                        :step="100"
                        mode="button"
                        prefix="¥"
                      />
                    </a-form-item>

                    <a-button
                      class="remove-btn"
                      circle
                      status="danger"
                      @click="removeCategoryBudget(index)"
                    >
                      <template #icon><icon-minus /></template>
                    </a-button>
                  </div>

                  <div class="add-category-budget">
                    <a-button type="dashed" long @click="addCategoryBudget">
                      <template #icon><icon-plus /></template>
                      添加分类预算
                    </a-button>
                  </div>
                </div>
              </template>

              <a-form-item>
                <a-button type="primary">保存预算设置</a-button>
              </a-form-item>
            </a-form>
          </a-tab-pane>
        </a-tabs>
      </a-card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import {
  IconPlus,
  IconMinus,
  IconEdit,
  IconDelete,
} from "@arco-design/web-vue/es/icon";
import AppLayout from "../../components/AppLayout.vue";

// 基本设置表单
const basicForm = reactive({
  currency: "CNY",
  dateFormat: "YYYY-MM-DD",
  startDayOfMonth: 1,
  budgetAlerts: true,
});

// 预算设置表单
const budgetForm = reactive({
  enableBudget: true,
  budgetType: "monthly",
  totalBudget: 5000,
  categoryBudgets: [
    { category: "food", amount: 1500 },
    { category: "transport", amount: 500 },
    { category: "shopping", amount: 1000 },
  ],
});

const addCategoryBudget = () => {
  budgetForm.categoryBudgets.push({ category: "", amount: 0 });
};

const removeCategoryBudget = (index: number) => {
  budgetForm.categoryBudgets.splice(index, 1);
};

// 分类表格列定义
const categoryColumns = [
  {
    title: "名称",
    dataIndex: "name",
  },
  {
    title: "描述",
    dataIndex: "description",
  },
  {
    title: "操作",
    slotName: "operations",
    width: 150,
  },
];

// 收入分类数据
const incomeCategories = ref([
  { key: "1", name: "工资", description: "主要工作收入" },
  { key: "2", name: "奖金", description: "绩效奖金、年终奖等" },
  { key: "3", name: "兼职", description: "额外工作收入" },
  { key: "4", name: "投资", description: "股票、基金收益" },
  { key: "5", name: "其他", description: "其他收入来源" },
]);

// 支出分类数据
const expenseCategories = ref([
  { key: "1", name: "餐饮", description: "日常饮食支出" },
  { key: "2", name: "交通", description: "公共交通、打车、加油等" },
  { key: "3", name: "购物", description: "日用品、衣物等" },
  { key: "4", name: "娱乐", description: "电影、游戏等" },
  { key: "5", name: "住房", description: "房租、物业费等" },
  { key: "6", name: "医疗", description: "医药费、体检等" },
  { key: "7", name: "教育", description: "学习培训费用" },
]);
</script>

<style lang="scss" scoped>
@import "@/styles/variables.scss";
@import "@/styles/mixins.scss";

.finance-settings {
  padding: $spacing-lg;

  .settings-card {
    margin-bottom: $spacing-lg;
  }

  .form-item-help {
    font-size: $font-size-xs;
    color: $text-color-secondary;
    margin-top: $spacing-xs;
  }

  .category-section {
    margin-bottom: $spacing-xl;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: $spacing-base;

      h3 {
        margin: 0;
        color: $text-color-primary;
        font-size: $font-size-base;
      }
    }
  }

  .category-budgets {
    margin-top: $spacing-lg;
    margin-bottom: $spacing-lg;
    padding: $spacing-base;
    background-color: $background-color-secondary;
    border-radius: $border-radius-base;

    h3 {
      margin-top: 0;
      margin-bottom: $spacing-base;
      color: $text-color-primary;
      font-size: $font-size-base;
    }

    .category-budget-item {
      display: grid;
      grid-template-columns: 1fr 1fr auto;
      gap: $spacing-base;
      align-items: flex-end;
      margin-bottom: $spacing-base;

      .remove-btn {
        margin-bottom: 22px;
      }
    }

    .add-category-budget {
      margin-top: $spacing-base;
    }
  }

  :deep(.arco-table) {
    .arco-table-th {
      background-color: $background-color-secondary;
      font-weight: $font-weight-semibold;
    }

    .arco-btn-text {
      padding: 0 $spacing-xs;

      &:hover {
        background-color: transparent;
        color: $primary-color;
      }

      &.arco-btn-status-danger:hover {
        color: $error-color;
      }
    }
  }
}
</style>
