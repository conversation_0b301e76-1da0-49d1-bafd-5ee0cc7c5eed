<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>全新财务记录表单设计</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <style>
      body {
        font-family: "Inter", "Helvetica Neue", "Hiragino Sans GB",
          "WenQuanYi Micro Hei", "Microsoft Yahei", sans-serif;
      }
      /* 自定义样式，用于激活标签页的下划线 */
      .tab-active {
        border-bottom-color: #4f46e5;
        color: #4f46e5;
        font-weight: 600;
      }
      .input-group {
        display: flex;
        align-items: center;
        background-color: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;
        transition: all 0.2s ease-in-out;
      }
      .input-group:focus-within {
        border-color: #4f46e5;
        box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
      }
      .input-group input {
        border: none;
        background: transparent;
        outline: none;
        width: 100%;
        padding: 0.75rem;
        text-align: right;
        font-size: 1rem;
      }
      .input-group .currency-symbol {
        padding-left: 0.75rem;
        color: #6b7280;
      }
    </style>
  </head>
  <body
    class="bg-gray-100 flex items-center justify-center min-h-screen p-4 sm:p-6 lg:p-8"
  >
    <div
      class="w-full max-w-4xl mx-auto bg-white rounded-2xl shadow-xl overflow-hidden"
    >
      <div class="p-6 sm:p-8">
        <!-- 头部 -->
        <div class="flex items-center mb-6">
          <button class="text-gray-500 hover:text-gray-800">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path d="m15 18-6-6 6-6" />
            </svg>
          </button>
          <h1 class="text-2xl font-bold text-gray-800 ml-4">编辑财务记录</h1>
        </div>

        <!-- 记录周期 -->
        <div class="mb-8">
          <label class="block text-sm font-medium text-gray-600 mb-2"
            >记录周期</label
          >
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 items-center">
            <div class="relative">
              <div
                class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none"
              >
                <svg
                  class="w-5 h-5 text-gray-400"
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <rect width="18" height="18" x="3" y="4" rx="2" ry="2" />
                  <line x1="16" x2="16" y1="2" y2="6" />
                  <line x1="8" x2="8" y1="2" y2="6" />
                  <line x1="3" x2="21" y1="10" y2="10" />
                </svg>
              </div>
              <input
                type="text"
                value="2025-07-01"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-10 p-2.5"
              />
            </div>
            <div class="relative">
              <div
                class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none"
              >
                <svg
                  class="w-5 h-5 text-gray-400"
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <rect width="18" height="18" x="3" y="4" rx="2" ry="2" />
                  <line x1="16" x2="16" y1="2" y2="6" />
                  <line x1="8" x2="8" y1="2" y2="6" />
                  <line x1="3" x2="21" y1="10" y2="10" />
                </svg>
              </div>
              <input
                type="text"
                value="2025-07-31"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-10 p-2.5"
              />
            </div>
          </div>
        </div>

        <!-- 标签页导航 -->
        <div class="border-b border-gray-200">
          <nav class="-mb-px flex space-x-8" id="tabs">
            <button
              data-tab-target="income"
              class="tab-btn tab-active py-4 px-1 border-b-2 font-medium text-lg leading-5 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 focus:outline-none"
            >
              收入项目
            </button>
            <button
              data-tab-target="expenses"
              class="tab-btn py-4 px-1 border-b-2 font-medium text-lg leading-5 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 focus:outline-none"
            >
              支出项目
            </button>
          </nav>
        </div>

        <!-- 标签页内容 -->
        <div class="pt-8">
          <!-- 收入面板 -->
          <div id="income" class="tab-content">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
              <!-- 工资 -->
              <div class="space-y-2">
                <label class="font-medium text-gray-700 flex items-center">
                  <svg
                    class="w-5 h-5 mr-2 text-indigo-500"
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path d="M21 12V7H5a2 2 0 0 1 0-4h14v4" />
                    <path d="M3 5v14a2 2 0 0 0 2 2h16v-5" />
                    <path d="M18 12a2 2 0 0 0 0 4h4v-4Z" />
                  </svg>
                  工资
                </label>
                <div class="input-group">
                  <span class="currency-symbol">¥</span>
                  <input type="number" value="80.00" placeholder="0.00" />
                </div>
              </div>
              <!-- 其他收入 -->
              <div class="space-y-2">
                <label class="font-medium text-gray-700 flex items-center">
                  <svg
                    class="w-5 h-5 mr-2 text-indigo-500"
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path d="M12 8v4" />
                    <path d="M12 16h.01" />
                    <path d="M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                    <path d="M4.22 16.22a2.5 2.5 0 0 1 0-3.54" />
                    <path d="M19.78 7.78a2.5 2.5 0 0 1 0 3.54" />
                  </svg>
                  其他收入
                </label>
                <div class="input-group">
                  <span class="currency-symbol">¥</span>
                  <input type="number" value="20.00" placeholder="0.00" />
                </div>
              </div>
            </div>
          </div>

          <!-- 支出面板 -->
          <div id="expenses" class="tab-content hidden">
            <div class="space-y-8">
              <!-- 日常开销卡片 -->
              <div class="bg-white border border-gray-200 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">
                  日常开销
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
                  <div class="space-y-2">
                    <label class="font-medium text-gray-700 flex items-center">
                      <svg
                        class="w-5 h-5 mr-2 text-green-500"
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      >
                        <path
                          d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"
                        />
                        <path d="m9 12 2 2 4-4" />
                      </svg>
                      花呗
                    </label>
                    <div class="input-group">
                      <span class="currency-symbol">¥</span
                      ><input type="number" value="30.00" placeholder="0.00" />
                    </div>
                  </div>
                  <div class="space-y-2">
                    <label class="font-medium text-gray-700 flex items-center">
                      <svg
                        class="w-5 h-5 mr-2 text-green-500"
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      >
                        <path d="M2 12h20" />
                        <path d="M12 2v20" />
                      </svg>
                      储值
                    </label>
                    <div class="input-group">
                      <span class="currency-symbol">¥</span
                      ><input type="number" value="15.00" placeholder="0.00" />
                    </div>
                  </div>
                </div>
              </div>

              <!-- 信用卡还款卡片 -->
              <div class="bg-white border border-gray-200 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">
                  信用卡还款
                </h3>
                <div
                  class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-8 gap-y-6"
                >
                  <div class="space-y-2">
                    <label class="font-medium text-gray-700 flex items-center">
                      <svg
                        class="w-5 h-5 mr-2 text-blue-500"
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      >
                        <rect width="20" height="14" x="2" y="5" rx="2" />
                        <line x1="2" x2="22" y1="10" y2="10" />
                      </svg>
                      浦发信用卡
                    </label>
                    <div class="input-group">
                      <span class="currency-symbol">¥</span
                      ><input
                        type="number"
                        value="9007199254740991.00"
                        placeholder="0.00"
                      />
                    </div>
                  </div>
                  <div class="space-y-2">
                    <label class="font-medium text-gray-700 flex items-center">
                      <svg
                        class="w-5 h-5 mr-2 text-blue-500"
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      >
                        <rect width="20" height="14" x="2" y="5" rx="2" />
                        <line x1="2" x2="22" y1="10" y2="10" />
                      </svg>
                      交行信用卡
                    </label>
                    <div class="input-group">
                      <span class="currency-symbol">¥</span
                      ><input
                        type="number"
                        value="9007199254740991.00"
                        placeholder="0.00"
                      />
                    </div>
                  </div>
                  <div class="space-y-2">
                    <label class="font-medium text-gray-700 flex items-center">
                      <svg
                        class="w-5 h-5 mr-2 text-blue-500"
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      >
                        <rect width="20" height="14" x="2" y="5" rx="2" />
                        <line x1="2" x2="22" y1="10" y2="10" />
                      </svg>
                      招商信用卡
                    </label>
                    <div class="input-group">
                      <span class="currency-symbol">¥</span
                      ><input type="number" value="45.00" placeholder="0.00" />
                    </div>
                  </div>
                </div>
              </div>

              <!-- 住房与借贷卡片 -->
              <div class="bg-white border border-gray-200 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">
                  住房与借贷
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
                  <div class="space-y-2">
                    <label class="font-medium text-gray-700 flex items-center">
                      <svg
                        class="w-5 h-5 mr-2 text-orange-500"
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      >
                        <path
                          d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"
                        />
                        <polyline points="9 22 9 12 15 12 15 22" />
                      </svg>
                      房租
                    </label>
                    <div class="input-group">
                      <span class="currency-symbol">¥</span
                      ><input type="number" value="80.00" placeholder="0.00" />
                    </div>
                  </div>
                  <div class="space-y-2">
                    <label class="font-medium text-gray-700 flex items-center">
                      <svg
                        class="w-5 h-5 mr-2 text-orange-500"
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      >
                        <path
                          d="M12 22V8.4C12 7.1 12.8 6 14.5 6h0C15.9 6 17 7 17 8.4V22"
                        />
                        <path d="M17 11h.5a2.5 2.5 0 0 1 0 5H17" />
                        <path d="M7 22v-5a2 2 0 0 1 2-2h1" />
                        <path d="M7 15h1.5a2.5 2.5 0 0 1 0 5H7" />
                      </svg>
                      房贷
                    </label>
                    <div class="input-group">
                      <span class="currency-symbol">¥</span
                      ><input type="number" value="60.00" placeholder="0.00" />
                    </div>
                  </div>
                  <div class="space-y-2">
                    <label class="font-medium text-gray-700 flex items-center">
                      <svg
                        class="w-5 h-5 mr-2 text-red-500"
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      >
                        <line x1="12" x2="12" y1="2" y2="22" />
                        <path
                          d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"
                        />
                      </svg>
                      京东白条
                    </label>
                    <div class="input-group">
                      <span class="currency-symbol">¥</span
                      ><input type="number" value="25.00" placeholder="0.00" />
                    </div>
                  </div>
                  <div class="space-y-2">
                    <label class="font-medium text-gray-700 flex items-center">
                      <svg
                        class="w-5 h-5 mr-2 text-red-500"
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      >
                        <line x1="12" x2="12" y1="2" y2="22" />
                        <path
                          d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"
                        />
                      </svg>
                      京东金条
                    </label>
                    <div class="input-group">
                      <span class="currency-symbol">¥</span
                      ><input type="number" value="35.00" placeholder="0.00" />
                    </div>
                  </div>
                </div>
              </div>

              <!-- 其他卡片 -->
              <div class="bg-white border border-gray-200 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">其他</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
                  <div class="space-y-2">
                    <label class="font-medium text-gray-700 flex items-center">
                      <svg
                        class="w-5 h-5 mr-2 text-purple-500"
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      >
                        <path
                          d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"
                        />
                      </svg>
                      捐
                    </label>
                    <div class="input-group">
                      <span class="currency-symbol">¥</span
                      ><input type="number" value="20.00" placeholder="0.00" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 备注 -->
        <div class="mt-8">
          <label
            for="remarks"
            class="block text-sm font-medium text-gray-600 mb-2"
            >备注</label
          >
          <textarea
            id="remarks"
            rows="4"
            class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm p-3"
            placeholder="填写本月财务备注..."
          >
七月份财务记录</textarea
          >
        </div>
      </div>

      <!-- 底部操作栏 -->
      <div
        class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end items-center space-x-4"
      >
        <button
          type="button"
          class="px-6 py-2.5 border border-gray-300 rounded-lg text-sm font-semibold text-gray-700 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
        >
          取消
        </button>
        <button
          type="button"
          class="px-6 py-2.5 bg-indigo-600 border border-transparent rounded-lg text-sm font-semibold text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          保存记录
        </button>
      </div>
    </div>

    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const tabs = document.querySelectorAll(".tab-btn");
        const tabContents = document.querySelectorAll(".tab-content");

        tabs.forEach((tab) => {
          tab.addEventListener("click", () => {
            // 移除所有按钮的激活样式
            tabs.forEach((t) => t.classList.remove("tab-active"));
            // 为当前点击的按钮添加激活样式
            tab.classList.add("tab-active");

            const targetId = tab.dataset.tabTarget;

            // 隐藏所有内容面板
            tabContents.forEach((content) => {
              content.classList.add("hidden");
            });

            // 显示目标内容面板
            const targetContent = document.getElementById(targetId);
            if (targetContent) {
              targetContent.classList.remove("hidden");
            }
          });
        });
      });
    </script>
  </body>
</html>
