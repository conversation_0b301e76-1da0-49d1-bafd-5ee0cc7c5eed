/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-22 10:48:52
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-16 23:25:30
 * @FilePath     : /src/main.ts
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-22 10:48:52
 */
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ArcoVue from '@arco-design/web-vue'
import '@arco-design/web-vue/dist/arco.css'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import VChart from 'vue-echarts'
import './styles/main.scss'
import App from './App.vue'
import router from './router'
import { isTauriApp, logEnvironment, mockSystem } from './utils/tauri-api'

// 初始化mock系统
mockSystem.init();

// 记录当前运行环境
logEnvironment()

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)
app.use(ArcoVue)
app.use(ElementPlus)

// 注册 ECharts 组件
app.component('VChart', VChart)

// 添加全局属性，用于在组件中检测运行环境
app.config.globalProperties.$isTauriApp = isTauriApp

// 全局错误处理
app.config.errorHandler = (err, _vm, info) => {
  console.error('Vue error:', err)
  console.error('Error info:', info)

  // 捕获特定的 DOM 操作错误
  if (err && err.toString().includes('parentNode')) {
    console.warn('DOM parentNode error caught and handled')
    return
  }

  // 捕获路由相关错误
  if (err && err.toString().includes('navigation')) {
    console.warn('Navigation error caught and handled')
    return
  }
}

// 全局未捕获的 Promise 错误处理
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled Promise Rejection:', event.reason)
  if (event.reason && event.reason.toString().includes('parentNode')) {
    console.warn('DOM parentNode promise rejection caught and handled')
    event.preventDefault()
  }
})

app.mount('#app')
