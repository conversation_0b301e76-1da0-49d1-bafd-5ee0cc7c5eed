/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-22 10:54:59
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-17
 * @FilePath     : /src/stores/auth.ts
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-22 10:54:59
 */
import { defineStore } from 'pinia'
import { ref } from 'vue'
import { api, logEnvironment } from '../utils/tauri-api'

// 记录当前运行环境
logEnvironment()

export const useAuthStore = defineStore('auth', () => {
  const isAuthenticated = ref(false)
  const isLoading = ref(false)

  const initDatabase = async () => {
    try {
      await api.invoke('init_database')
    } catch (error) {
      console.error('Failed to initialize database:', error)
      throw error
    }
  }

  const checkMasterPasswordSet = async (): Promise<boolean> => {
    try {
      return await api.invoke<boolean>('is_master_password_set')
    } catch (error) {
      console.error('Failed to check master password:', error)
      return false
    }
  }

  const verifyMasterPassword = async (password: string): Promise<boolean> => {
    try {
      isLoading.value = true
      console.log('Verifying master password...')
      const result = await api.invoke<boolean>('verify_master_password', { password })
      if (result) {
        isAuthenticated.value = true
        localStorage.setItem('isAuthenticated', 'true')
        console.log('Master password verified and stored')
      } else {
        console.log('Master password verification failed')
      }
      return result
    } catch (error) {
      console.error('Failed to verify master password:', error)
      return false
    } finally {
      isLoading.value = false
    }
  }

  const lockApp = async () => {
    try {
      await api.invoke('lock_app')
      isAuthenticated.value = false
      localStorage.removeItem('isAuthenticated')
    } catch (error) {
      console.error('Failed to lock app:', error)
    }
  }

  const logout = () => {
    isAuthenticated.value = false
    localStorage.removeItem('isAuthenticated')
  }

  const checkAuthStatus = async (): Promise<boolean> => {
    try {
      console.log('Checking backend auth status...')
      const result = await api.invoke<boolean>('check_auth_status')
      console.log('Backend auth status:', result)
      return result
    } catch (error) {
      console.error('Failed to check auth status:', error)
      return false
    }
  }

  const syncAuthStatus = async (): Promise<boolean> => {
    try {
      const localIsAuthenticated = localStorage.getItem('isAuthenticated') === 'true'
      console.log('🔍 [Auth Sync] Starting auth status synchronization')
      console.log('🔍 [Auth Sync] Local storage authenticated:', localIsAuthenticated)

      if (!localIsAuthenticated) {
        console.log('🔍 [Auth Sync] Local not authenticated, setting frontend to false')
        isAuthenticated.value = false
        return false
      }

      console.log('🔍 [Auth Sync] Local authenticated, checking backend...')
      const backendIsAuthenticated = await checkAuthStatus()
      console.log('🔍 [Auth Sync] Backend authenticated:', backendIsAuthenticated)

      if (backendIsAuthenticated) {
        console.log('🔍 [Auth Sync] ✅ Both frontend and backend authenticated')
        isAuthenticated.value = true
        return true
      } else {
        console.log('🔍 [Auth Sync] Backend not authenticated, checking if master password exists...')
        const hasMasterPassword = await checkMasterPasswordSet()
        console.log('🔍 [Auth Sync] Master password exists:', hasMasterPassword)

        if (hasMasterPassword) {
          console.log('🔍 [Auth Sync] ⚠️ Development mode restart detected - maintaining frontend auth state')
          console.log('🔍 [Auth Sync] Note: In production, authentication would persist across restarts')
          isAuthenticated.value = true
          return true
        } else {
          console.log('🔍 [Auth Sync] ❌ No master password found, clearing frontend state')
          logout()
          return false
        }
      }
    } catch (error) {
      console.error('🔍 [Auth Sync] Failed to sync auth status:', error)
      logout()
      return false
    }
  }

  return {
    isAuthenticated,
    isLoading,
    initDatabase,
    checkMasterPasswordSet,
    verifyMasterPassword,
    lockApp,
    logout,
    checkAuthStatus,
    syncAuthStatus
  }
})