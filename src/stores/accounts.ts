import { defineStore } from 'pinia'
import { ref } from 'vue'
import { withAuth<PERSON>he<PERSON> } from '../utils/auth-interceptor'
import { accountApi, categoryApi } from '../api/accounts'
import { api } from '../utils/tauri-api'
import { mockSystem } from '../utils/mock-system'

export interface Account {
  id: string
  platform: string
  username: string
  display_name?: string
  encrypted_password: string
  email?: string
  phone?: string
  website_url?: string
  category_id?: string
  notes?: string
  custom_fields?: Record<string, string>
  created_at: string
  updated_at: string
}

export interface CreateAccountRequest {
  platform: string
  username: string
  display_name?: string
  password: string
  email?: string
  phone?: string
  website_url?: string
  category_id?: string
  notes?: string
  custom_fields?: Record<string, string>
}

export interface UpdateAccountRequest {
  id: string
  platform?: string
  username?: string
  display_name?: string
  password?: string
  email?: string
  phone?: string
  website_url?: string
  category_id?: string
  notes?: string
  custom_fields?: Record<string, string>
}

export interface Category {
  id: string
  name: string
  color?: string
  icon?: string
  created_at: string
}

export interface CreateCategoryRequest {
  name: string
  color?: string
  icon?: string
}

export interface UpdateCategoryRequest {
  id: string
  name?: string
  color?: string
  icon?: string
}

export const useAccountsStore = defineStore('accounts', () => {
  const accounts = ref<Account[]>([])
  const categories = ref<Category[]>([])
  const isLoading = ref(false)
  const useMock = ref(mockSystem.isMockEnabled())

  const loadAccounts = async () => {
    return withAuthCheck(async () => {
      try {
        console.log("Loading accounts...")
        let result: Account[];

        if (useMock.value) {
          result = await accountApi.getAllAccounts();
        } else {
          result = await api.invoke<Account[]>('get_all_accounts');
        }

        accounts.value = result;
        console.log(`Loaded ${result.length} accounts`);
        return result;
      } catch (error) {
        console.error('Failed to load accounts:', error)
        throw error
      }
    });
  }

  const searchAccounts = async (keyword: string) => {
    try {
      isLoading.value = true
      let result: Account[];

      if (useMock.value) {
        result = await accountApi.searchAccounts(keyword);
      } else {
        result = await api.invoke<Account[]>('search_accounts', { keyword });
      }

      return result;
    } catch (error) {
      console.error('Failed to search accounts:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const getAccountById = async (id: string): Promise<Account | null> => {
    return withAuthCheck(async () => {
      try {
        let result: Account;

        if (useMock.value) {
          result = await accountApi.getAccountById(id);
        } else {
          result = await api.invoke<Account>('get_account_by_id', { id });
        }

        return result;
      } catch (error) {
        console.error('Failed to get account by id:', error)
        return null
      }
    });
  }

  const createAccount = async (request: CreateAccountRequest): Promise<Account> => {
    console.log("=== Store: 开始创建账号 ===");
    console.log("Store: 接收到的请求数据:", {...request, password: "***"});

    return withAuthCheck(async () => {
      try {
        console.log("Store: 调用创建账号API...");
        let result: Account;

        if (useMock.value) {
          result = await accountApi.createAccount(request);
        } else {
          result = await api.invoke<Account>('create_account', { request });
        }

        console.log("Store: API执行成功，返回结果:", result);

        console.log("Store: 将新账号添加到本地列表...");
        accounts.value.unshift(result)
        console.log("Store: 当前账号总数:", accounts.value.length);

        console.log("=== Store: 账号创建完成 ===");
        return result
      } catch (error) {
        console.error('=== Store: 账号创建失败 ===')
        console.error('Store: 错误详情:', error)
        throw error
      }
    });
  }

  const updateAccount = async (request: UpdateAccountRequest): Promise<Account> => {
    return withAuthCheck(async () => {
      try {
        let result: Account;

        if (useMock.value) {
          result = await accountApi.updateAccount(request);
        } else {
          result = await api.invoke<Account>('update_account', { request });
        }

        // 更新本地列表
        const index = accounts.value.findIndex(acc => acc.id === result.id)
        if (index !== -1) {
          accounts.value[index] = result
        }

        return result
      } catch (error) {
        console.error('Failed to update account:', error)
        throw error
      }
    });
  }

  const deleteAccount = async (id: string): Promise<void> => {
    return withAuthCheck(async () => {
      try {
        if (useMock.value) {
          await accountApi.deleteAccount(id);
        } else {
          await api.invoke('delete_account', { id });
        }

        // 从本地列表中移除
        const index = accounts.value.findIndex(acc => acc.id === id)
        if (index !== -1) {
          accounts.value.splice(index, 1)
        }
      } catch (error) {
        console.error('Failed to delete account:', error)
        throw error
      }
    });
  }

  const batchDeleteAccounts = async (ids: string[]): Promise<number> => {
    return withAuthCheck(async () => {
      try {
        let deletedCount: number;

        if (useMock.value) {
          deletedCount = await accountApi.batchDeleteAccounts(ids);
        } else {
          deletedCount = await api.invoke<number>('batch_delete_accounts', { ids });
        }

        // 从本地列表中移除成功删除的账号
        accounts.value = accounts.value.filter(acc => !ids.includes(acc.id))

        return deletedCount
      } catch (error) {
        console.error('Failed to batch delete accounts:', error)
        throw error
      }
    });
  }

  const decryptPassword = async (encryptedPassword: string): Promise<string> => {
    return withAuthCheck(async () => {
      try {
        let result: string;

        if (useMock.value) {
          // 在mock模式下，简单地返回一个假的解密密码
          result = "mock_decrypted_password";
        } else {
          result = await api.invoke<string>('decrypt_password', { encryptedPassword });
        }

        return result;
      } catch (error) {
        console.error('Failed to decrypt password:', error)
        throw error
      }
    });
  }

  const loadCategories = async () => {
    return withAuthCheck(async () => {
      try {
        let result: Category[];

        if (useMock.value) {
          result = await categoryApi.getAllCategories();
        } else {
          result = await api.invoke<Category[]>('get_all_categories');
        }

        categories.value = result;
        return result;
      } catch (error) {
        console.error('Failed to load categories:', error)
        throw error
      }
    });
  }

  const createCategory = async (request: CreateCategoryRequest): Promise<Category> => {
    return withAuthCheck(async () => {
      try {
        let result: Category;

        if (useMock.value) {
          result = await categoryApi.createCategory(request);
        } else {
          result = await api.invoke<Category>('create_category', { request });
        }

        categories.value.push(result);
        return result;
      } catch (error) {
        console.error('Failed to create category:', error)
        throw error
      }
    });
  }

  const updateCategory = async (request: UpdateCategoryRequest): Promise<Category> => {
    return withAuthCheck(async () => {
      try {
        let result: Category;

        if (useMock.value) {
          result = await categoryApi.updateCategory(request);
        } else {
          result = await api.invoke<Category>('update_category', { request });
        }

        const index = categories.value.findIndex(cat => cat.id === result.id)
        if (index !== -1) {
          categories.value[index] = result
        }

        return result
      } catch (error) {
        console.error('Failed to update category:', error)
        throw error
      }
    });
  }

  const deleteCategory = async (id: string): Promise<void> => {
    return withAuthCheck(async () => {
      try {
        if (useMock.value) {
          await categoryApi.deleteCategory(id);
        } else {
          await api.invoke('delete_category', { id });
        }

        const index = categories.value.findIndex(cat => cat.id === id)
        if (index !== -1) {
          categories.value.splice(index, 1)
        }
      } catch (error) {
        console.error('Failed to delete category:', error)
        throw error
      }
    });
  }

  const getDatabasePath = async (): Promise<string> => {
    return withAuthCheck(async () => {
      try {
        if (useMock.value) {
          return "/mock/database/path.db";
        } else {
          const result = await api.invoke<string>('get_database_path');
          return result;
        }
      } catch (error) {
        console.error('Failed to get database path:', error)
        throw error
      }
    });
  }

  const openDataFolder = async () => {
    try {
      if (!useMock.value) {
        await api.invoke('open_data_folder');
      } else {
        console.log("Mock mode: Would open data folder");
      }
    } catch (error) {
      console.error('Failed to open data folder:', error)
      throw error
    }
  }

  const openDownloadsFolder = async () => {
    try {
      if (!useMock.value) {
        await api.invoke('open_downloads_folder');
      } else {
        console.log("Mock mode: Would open downloads folder");
      }
    } catch (error) {
      console.error('Failed to open downloads folder:', error)
      throw error
    }
  }

  const clearAllData = async () => {
    try {
      if (useMock.value) {
        // 在mock模式下，直接清空本地数据
        accounts.value = [];
        // 模拟重新加载默认分类
        await loadCategories();
      } else {
        await api.invoke('clear_all_data');
        // 清空本地状态
        accounts.value = [];
        // 重新加载默认分类
        await loadCategories();
      }
    } catch (error) {
      console.error('Failed to clear all data:', error)
      throw error
    }
  }

  const exportData = async (): Promise<string> => {
    try {
      if (useMock.value) {
        // 在mock模式下，返回一个假的JSON字符串
        return JSON.stringify({ accounts: accounts.value, categories: categories.value }, null, 2);
      } else {
        const exportJson = await api.invoke<string>('export_data');
        return exportJson;
      }
    } catch (error) {
      console.error('Failed to export data:', error)
      throw error
    }
  }

  const exportDataToFile = async (): Promise<string> => {
    try {
      if (useMock.value) {
        // 在mock模式下，模拟导出成功
        return "/mock/path/to/exported/file.json";
      } else {
        const result = await api.invoke<string>('export_data_to_file');
        return result;
      }
    } catch (error) {
      console.error('Failed to export data to file:', error)
      throw error
    }
  }

  const importData = async (jsonData: string): Promise<{ success: number; failed: number }> => {
  return withAuthCheck(async () => {
    try {
      console.log('🏪 [AccountsStore] Starting import_data with JSON length:', jsonData.length);
      console.log('🏪 [AccountsStore] JSON preview:', jsonData.substring(0, 300) + '...');

      let result: { success: number; failed: number };

      if (useMock.value) {
        // 在mock模式下，模拟导入成功
        result = { success: 5, failed: 0 };
      } else {
        result = await api.invoke<{ success: number; failed: number }>('import_data', { importJson: jsonData });
      }

      console.log('🏪 [AccountsStore] import_data completed successfully:', result);

      // 重新加载账号列表
      console.log('🏪 [AccountsStore] Reloading accounts after import...');
      await loadAccounts();
      console.log('🏪 [AccountsStore] Accounts reloaded successfully');

      return result;
    } catch (error) {
      console.error('🏪 [AccountsStore] Failed to import data:');
      console.error('  Error type:', typeof error);
      console.error('  Error message:', (error as any)?.message || String(error));
      console.error('  Error stack:', (error as Error)?.stack);
      console.error('  Full error object:', error);
      throw error;
    }
  });
}

  const downloadTemplate = async (): Promise<string> => {
    return withAuthCheck(async () => {
      try {
        if (useMock.value) {
          // 在mock模式下，模拟下载模板成功
          return "/mock/path/to/template.csv";
        } else {
          const result = await api.invoke<string>('download_template');
          return result;
        }
      } catch (error) {
        console.error('Failed to download template:', error)
        throw error
      }
    });
  }

  // 添加一个方法来切换mock模式
  const toggleMockMode = (value: boolean) => {
    useMock.value = value;
    localStorage.setItem('use_mock', useMock.value ? 'true' : 'false');
    // 重新加载数据
    loadAccounts();
    loadCategories();
  };

  return {
    accounts,
    categories,
    isLoading,
    useMock,
    loadAccounts,
    searchAccounts,
    getAccountById,
    createAccount,
    updateAccount,
    deleteAccount,
    batchDeleteAccounts,
    decryptPassword,
    loadCategories,
    createCategory,
    updateCategory,
    deleteCategory,
    getDatabasePath,
    openDataFolder,
    openDownloadsFolder,
    clearAllData,
    exportData,
    exportDataToFile,
    importData,
    downloadTemplate,
    toggleMockMode
  }
})