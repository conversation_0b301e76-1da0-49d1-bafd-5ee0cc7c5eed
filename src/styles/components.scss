// src/styles/components.scss - 通用组件样式
// 注意：variables.scss 和 mixins.scss 通过 Vite additionalData 自动导入

// =============================================
// 表单组件样式
// =============================================
.form-container {
  .form-item {
    margin-bottom: $spacing-lg;

    .form-label {
      display: block;
      margin-bottom: $spacing-xs;
      font-weight: $font-weight-medium;
      color: $text-color-primary;
      font-size: $font-size-sm;

      &.required::after {
        content: '*';
        color: $error-color;
        margin-left: $spacing-xs;
      }
    }

    .form-input {
      @include input-base;
    }

    .form-textarea {
      @include input-base;
      min-height: 80px;
      resize: vertical;
    }

    .form-error {
      margin-top: $spacing-xs;
      font-size: $font-size-xs;
      color: $error-color;
      @include fade-in;
    }

    .form-help {
      margin-top: $spacing-xs;
      font-size: $font-size-xs;
      color: $text-color-secondary;
    }
  }
}

// =============================================
// 按钮组件样式
// =============================================
.btn {
  @include button-base;
  padding: $spacing-sm $spacing-base;
  font-size: $font-size-sm;

  &.btn-primary {
    @include button-primary;
  }

  &.btn-secondary {
    @include button-secondary;
  }

  &.btn-danger {
    background: $error-color;
    border-color: $error-color;
    color: $text-color-white;

    &:hover:not(:disabled) {
      background: $error-color-dark;
      border-color: $error-color-dark;
    }
  }

  &.btn-sm {
    padding: $spacing-xs $spacing-sm;
    font-size: $font-size-xs;
  }

  &.btn-lg {
    padding: $spacing-base $spacing-lg;
    font-size: $font-size-base;
  }

  &.btn-block {
    width: 100%;
  }

  .btn-icon {
    margin-right: $spacing-xs;

    &:last-child {
      margin-right: 0;
      margin-left: $spacing-xs;
    }
  }
}

// =============================================
// 卡片组件样式
// =============================================
.card {
  @include card-shadow;
  overflow: hidden;

  &.card-hoverable {
    @include card-hover;
    cursor: pointer;
  }

  .card-header {
    padding: $spacing-base $spacing-lg;
    border-bottom: 1px solid $border-color-light;
    @include flex-center-between;

    .card-title {
      margin: 0;
      font-size: $font-size-lg;
      font-weight: $font-weight-semibold;
      color: $text-color-primary;
      @include text-ellipsis;
    }

    .card-actions {
      @include flex-center;
      gap: $spacing-xs;
    }
  }

  .card-body {
    padding: $spacing-lg;
  }

  .card-footer {
    padding: $spacing-base $spacing-lg;
    border-top: 1px solid $border-color-light;
    background: $background-color-secondary;
  }

  // 卡片变体
  &.card-compact {
    .card-header,
    .card-body,
    .card-footer {
      padding: $spacing-base;
    }
  }
}

// =============================================
// 模态框组件样式
// =============================================
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  @include flex-center;
  z-index: $z-index-modal;
  @include fade-in;

  .modal-content {
    background: $background-color-primary;
    border-radius: $border-radius-lg;
    box-shadow: $shadow-xl;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    @include scale-in;

    .modal-header {
      padding: $spacing-lg;
      border-bottom: 1px solid $border-color-light;
      @include flex-center-between;

      .modal-title {
        margin: 0;
        font-size: $font-size-lg;
        font-weight: $font-weight-semibold;
        color: $text-color-primary;
      }

      .modal-close {
        @include button-base;
        padding: $spacing-xs;
        border: none;
        background: transparent;
        color: $text-color-secondary;

        &:hover {
          background: $background-color-secondary;
          color: $text-color-primary;
        }
      }
    }

    .modal-body {
      padding: $spacing-lg;
      overflow-y: auto;
      @include custom-scrollbar;
    }

    .modal-footer {
      padding: $spacing-lg;
      border-top: 1px solid $border-color-light;
      @include flex-center-between;
      gap: $spacing-base;

      .modal-actions {
        @include flex-center;
        gap: $spacing-sm;
      }
    }
  }

  @include mobile {
    .modal-content {
      width: 95%;
      max-height: 90vh;

      .modal-header,
      .modal-body,
      .modal-footer {
        padding: $spacing-base;
      }
    }
  }
}

// =============================================
// 数据表格样式
// =============================================
.data-table {
  width: 100%;
  border-collapse: collapse;
  background: $background-color-primary;
  border-radius: $border-radius-lg;
  overflow: hidden;
  box-shadow: $shadow-sm;

  .table-header {
    background: $background-color-secondary;

    .table-cell {
      padding: $spacing-base;
      font-weight: $font-weight-semibold;
      color: $text-color-primary;
      border-bottom: 2px solid $border-color;
      text-align: left;
    }
  }

  .table-row {
    @include smooth-transition(background-color);

    &:hover {
      background: $background-color-dark;
    }

    &:nth-child(even) {
      background: $background-color-secondary;

      &:hover {
        background: $background-color-dark;
      }
    }

    .table-cell {
      padding: $spacing-base;
      border-bottom: 1px solid $border-color-light;
      color: $text-color-primary;

      &.cell-actions {
        width: 1%;
        white-space: nowrap;

        .action-buttons {
          @include flex-center;
          gap: $spacing-xs;
          opacity: 0;
          @include smooth-transition(opacity);
        }
      }
    }

    &:hover .action-buttons {
      opacity: 1;
    }
  }

  // 响应式表格
  @include mobile {
    .table-header {
      display: none;
    }

    .table-row {
      display: block;
      margin-bottom: $spacing-base;
      border: 1px solid $border-color-light;
      border-radius: $border-radius-base;
      padding: $spacing-base;

      .table-cell {
        display: block;
        border: none;
        padding: $spacing-xs 0;

        &::before {
          content: attr(data-label) ': ';
          font-weight: $font-weight-semibold;
          color: $text-color-secondary;
        }

        &.cell-actions {
          .action-buttons {
            opacity: 1;
            margin-top: $spacing-sm;
          }
        }
      }
    }
  }
}

// =============================================
// 加载状态样式
// =============================================
.loading-container {
  @include flex-center;
  padding: $spacing-xl;
  color: $text-color-secondary;

  .loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid $border-color-light;
    border-top: 2px solid $primary-color;
    border-radius: $border-radius-circle;
    animation: spin 1s linear infinite;
    margin-right: $spacing-sm;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// =============================================
// 消息提示样式
// =============================================
.toast {
  position: fixed;
  top: $spacing-lg;
  right: $spacing-lg;
  min-width: 300px;
  max-width: 400px;
  padding: $spacing-base;
  border-radius: $border-radius-base;
  box-shadow: $shadow-lg;
  z-index: $z-index-toast;
  @include fade-in-down;

  &.toast-success {
    background: $success-color;
    color: $text-color-white;
  }

  &.toast-error {
    background: $error-color;
    color: $text-color-white;
  }

  &.toast-warning {
    background: $warning-color;
    color: $text-color-white;
  }

  &.toast-info {
    background: $info-color;
    color: $text-color-white;
  }

  .toast-content {
    @include flex-center-between;

    .toast-message {
      flex: 1;
      margin-right: $spacing-sm;
    }

    .toast-close {
      background: none;
      border: none;
      color: inherit;
      cursor: pointer;
      padding: 0;
      opacity: 0.8;

      &:hover {
        opacity: 1;
      }
    }
  }

  @include mobile {
    left: $spacing-base;
    right: $spacing-base;
    min-width: auto;
  }
}