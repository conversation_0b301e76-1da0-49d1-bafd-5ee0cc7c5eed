// src/styles/main.scss - 主样式文件

// =============================================
// 基础样式文件 - 全局样式定义
// =============================================

// 导入变量和混入
@import "./variables.scss";
@import "./mixins.scss";

// 全局重置和基础样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: $line-height-base;
}

body {
  font-family: $font-family-base;
  font-size: $font-size-base;
  color: $text-color-primary;
  background: $background-color-primary;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 滚动条样式（全局定义）
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: $background-color-secondary;
}

::-webkit-scrollbar-thumb {
  background: $border-color;
  border-radius: $border-radius-sm;

  &:hover {
    background: $border-color-dark;
  }
}

// 基础链接样式
a {
  color: $primary-color;
  text-decoration: none;
  @include smooth-transition(color);

  &:hover {
    color: $primary-color-hover;
  }
}

// 基础按钮重置
button {
  border: none;
  background: none;
  cursor: pointer;
  font-family: inherit;
}

// 基础表单元素样式
input, textarea, select {
  font-family: inherit;
  font-size: inherit;
}

// 基础焦点样式
:focus {
  outline: none;
}

:focus-visible {
  outline: 2px solid $primary-color;
  outline-offset: 2px;
}

// =============================================
// 全局样式重置
// =============================================
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: $line-height-base;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  margin: 0;
  padding: 0;
  font-family: $font-family-base;
  font-size: $font-size-base;
  color: $text-color-primary;
  background: $background-color-primary;
  line-height: $line-height-base;
}

// =============================================
// 全局样式定义
// =============================================
#app {
  height: 100vh;
  overflow: hidden;
}

// 滚动条样式在上面已定义

// =============================================
// 工具类
// =============================================
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: $primary-color; }
.text-success { color: $success-color; }
.text-warning { color: $warning-color; }
.text-error { color: $error-color; }
.text-secondary { color: $text-color-secondary; }
.text-disabled { color: $text-color-disabled; }

.bg-primary { background: $primary-color; }
.bg-success { background: $success-color; }
.bg-warning { background: $warning-color; }
.bg-error { background: $error-color; }
.bg-secondary { background: $background-color-secondary; }

.font-bold { font-weight: $font-weight-bold; }
.font-semibold { font-weight: $font-weight-semibold; }
.font-medium { font-weight: $font-weight-medium; }
.font-normal { font-weight: $font-weight-normal; }

.text-xs { font-size: $font-size-xs; }
.text-sm { font-size: $font-size-sm; }
.text-base { font-size: $font-size-base; }
.text-lg { font-size: $font-size-lg; }
.text-xl { font-size: $font-size-xl; }
.text-xxl { font-size: $font-size-xxl; }

// 间距工具类
.m-0 { margin: 0; }
.m-xs { margin: $spacing-xs; }
.m-sm { margin: $spacing-sm; }
.m-base { margin: $spacing-base; }
.m-lg { margin: $spacing-lg; }
.m-xl { margin: $spacing-xl; }

.mt-0 { margin-top: 0; }
.mt-xs { margin-top: $spacing-xs; }
.mt-sm { margin-top: $spacing-sm; }
.mt-base { margin-top: $spacing-base; }
.mt-lg { margin-top: $spacing-lg; }
.mt-xl { margin-top: $spacing-xl; }

.mb-0 { margin-bottom: 0; }
.mb-xs { margin-bottom: $spacing-xs; }
.mb-sm { margin-bottom: $spacing-sm; }
.mb-base { margin-bottom: $spacing-base; }
.mb-lg { margin-bottom: $spacing-lg; }
.mb-xl { margin-bottom: $spacing-xl; }

.ml-0 { margin-left: 0; }
.ml-xs { margin-left: $spacing-xs; }
.ml-sm { margin-left: $spacing-sm; }
.ml-base { margin-left: $spacing-base; }
.ml-lg { margin-left: $spacing-lg; }
.ml-xl { margin-left: $spacing-xl; }

.mr-0 { margin-right: 0; }
.mr-xs { margin-right: $spacing-xs; }
.mr-sm { margin-right: $spacing-sm; }
.mr-base { margin-right: $spacing-base; }
.mr-lg { margin-right: $spacing-lg; }
.mr-xl { margin-right: $spacing-xl; }

.p-0 { padding: 0; }
.p-xs { padding: $spacing-xs; }
.p-sm { padding: $spacing-sm; }
.p-base { padding: $spacing-base; }
.p-lg { padding: $spacing-lg; }
.p-xl { padding: $spacing-xl; }

.pt-0 { padding-top: 0; }
.pt-xs { padding-top: $spacing-xs; }
.pt-sm { padding-top: $spacing-sm; }
.pt-base { padding-top: $spacing-base; }
.pt-lg { padding-top: $spacing-lg; }
.pt-xl { padding-top: $spacing-xl; }

.pb-0 { padding-bottom: 0; }
.pb-xs { padding-bottom: $spacing-xs; }
.pb-sm { padding-bottom: $spacing-sm; }
.pb-base { padding-bottom: $spacing-base; }
.pb-lg { padding-bottom: $spacing-lg; }
.pb-xl { padding-bottom: $spacing-xl; }

.pl-0 { padding-left: 0; }
.pl-xs { padding-left: $spacing-xs; }
.pl-sm { padding-left: $spacing-sm; }
.pl-base { padding-left: $spacing-base; }
.pl-lg { padding-left: $spacing-lg; }
.pl-xl { padding-left: $spacing-xl; }

.pr-0 { padding-right: 0; }
.pr-xs { padding-right: $spacing-xs; }
.pr-sm { padding-right: $spacing-sm; }
.pr-base { padding-right: $spacing-base; }
.pr-lg { padding-right: $spacing-lg; }
.pr-xl { padding-right: $spacing-xl; }

// 布局工具类
.flex { display: flex; }
.flex-column { display: flex; flex-direction: column; }
.flex-center { @include flex-center; }
.flex-center-between { @include flex-center-between; }
.flex-center-start { @include flex-center-start; }
.flex-column-center { @include flex-column-center; }

.flex-1 { flex: 1; }
.flex-auto { flex: auto; }
.flex-none { flex: none; }

.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }

.justify-center { justify-content: center; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

// 圆角工具类
.rounded-none { border-radius: 0; }
.rounded-xs { border-radius: $border-radius-xs; }
.rounded-sm { border-radius: $border-radius-sm; }
.rounded { border-radius: $border-radius-base; }
.rounded-lg { border-radius: $border-radius-lg; }
.rounded-xl { border-radius: $border-radius-xl; }
.rounded-full { border-radius: $border-radius-circle; }

// 阴影工具类
.shadow-none { box-shadow: none; }
.shadow-xs { box-shadow: $shadow-xs; }
.shadow-sm { box-shadow: $shadow-sm; }
.shadow { box-shadow: $shadow-base; }
.shadow-lg { box-shadow: $shadow-lg; }
.shadow-xl { box-shadow: $shadow-xl; }

// 动画工具类
.transition { @include smooth-transition; }
.transition-fast { transition: all $transition-duration-fast $transition-timing; }
.transition-slow { transition: all $transition-duration-slow $transition-timing; }

// 隐藏工具类
.hidden { display: none; }
.sr-only { @include visually-hidden; }

// 溢出工具类
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-scroll { overflow: scroll; }

.overflow-x-hidden { overflow-x: hidden; }
.overflow-x-auto { overflow-x: auto; }
.overflow-x-scroll { overflow-x: scroll; }

.overflow-y-hidden { overflow-y: hidden; }
.overflow-y-auto { overflow-y: auto; }
.overflow-y-scroll { overflow-y: scroll; }

// 文本省略工具类
.truncate { @include text-ellipsis; }
.line-clamp-2 { @include text-multiline-ellipsis(2); }
.line-clamp-3 { @include text-multiline-ellipsis(3); }

// 位置工具类
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

// 宽高工具类
.w-full { width: 100%; }
.w-auto { width: auto; }
.h-full { height: 100%; }
.h-auto { height: auto; }
.h-screen { height: 100vh; }

// 响应式隐藏工具类
@include mobile {
  .mobile-hidden { display: none !important; }
}

@include tablet {
  .tablet-hidden { display: none !important; }
}

@include desktop {
  .desktop-hidden { display: none !important; }
}
