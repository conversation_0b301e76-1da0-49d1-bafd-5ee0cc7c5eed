# SCSS 样式系统使用指南

## 概述

本项目采用 SCSS 作为 CSS 预处理器，建立了完整的设计令牌系统和样式架构，提供了丰富的变量、混入函数和工具类。

## 文件结构

```
src/styles/
├── main.scss           # 主样式文件，导入所有模块
├── variables.scss      # 设计令牌（颜色、间距、字体等）
├── mixins.scss         # 混入函数库
├── components.scss     # 通用组件样式
├── arco-theme.scss     # Arco Design 主题定制
└── README.md          # 使用文档
```

## 使用方法

### 1. 基本使用

在 Vue 组件中使用 SCSS：

```vue
<style lang="scss" scoped>
// 变量和混入已自动导入，可直接使用
.my-component {
  padding: $spacing-base;
  color: $text-color-primary;
  @include smooth-transition(all);
}
</style>
```

### 2. 设计令牌变量

#### 颜色系统

```scss
// 主色调
$primary-color: #1890ff;
$primary-color-hover: #40a9ff;
$primary-color-active: #096dd9;

// 状态颜色
$success-color: #52c41a;
$warning-color: #faad14;
$error-color: #f5222d;

// 文本颜色
$text-color-primary: #262626;
$text-color-secondary: #8c8c8c;
$text-color-disabled: #bfbfbf;
```

#### 间距系统

```scss
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-base: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
```

#### 字体系统

```scss
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-base: 16px;
$font-size-lg: 18px;

$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;
```

### 3. 混入函数

#### 布局混入

```scss
.container {
  @include flex-center; // 居中对齐
  @include flex-center-between; // 两端对齐
  @include flex-column; // 垂直布局
}
```

#### 卡片样式混入

```scss
.card {
  @include card-shadow; // 基础卡片阴影
  @include card-hover; // 悬停效果
}
```

#### 按钮样式混入

```scss
.button {
  @include button-primary; // 主要按钮样式
  @include button-secondary; // 次要按钮样式
}
```

#### 文本处理混入

```scss
.text {
  @include text-ellipsis; // 单行省略
  @include text-multiline-ellipsis(2); // 多行省略
}
```

#### 响应式混入

```scss
.responsive {
  // 移动端样式
  @include mobile {
    font-size: $font-size-sm;
  }

  // 平板端样式
  @include tablet {
    font-size: $font-size-base;
  }

  // 桌面端样式
  @include desktop {
    font-size: $font-size-lg;
  }
}
```

#### 动画混入

```scss
.animated {
  @include smooth-transition(all); // 平滑过渡
  @include fade-in; // 淡入动画
  @include fade-in-up; // 向上淡入
  @include scale-in; // 缩放进入
}
```

### 4. BEM 命名约定

推荐使用 BEM（Block Element Modifier）命名约定：

```scss
// Block（块）
.account-card {
  @include card-shadow;

  // Element（元素）
  &__header {
    @include flex-center-between;
  }

  &__title {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
  }

  &__content {
    padding: $spacing-base;
  }

  // Modifier（修饰符）
  &--featured {
    border-color: $primary-color;
  }

  &--disabled {
    opacity: 0.6;
    pointer-events: none;
  }
}
```

### 5. 工具类

项目提供了丰富的工具类，可直接在模板中使用：

```html
<!-- 文本样式 -->
<p class="text-center text-primary font-bold text-lg">标题文本</p>

<!-- 间距 -->
<div class="p-lg mt-xl mb-base">内容区域</div>

<!-- 布局 -->
<div class="flex flex-center-between items-center">
  <span>左侧内容</span>
  <span>右侧内容</span>
</div>

<!-- 圆角和阴影 -->
<div class="rounded-lg shadow-base">卡片内容</div>
```

### 6. 响应式设计

使用响应式混入创建适配不同屏幕的样式：

```scss
.component {
  // 默认桌面样式
  padding: $spacing-lg;
  font-size: $font-size-base;

  // 平板样式
  @include tablet {
    padding: $spacing-base;
    font-size: $font-size-sm;
  }

  // 移动端样式
  @include mobile {
    padding: $spacing-sm;
    font-size: $font-size-xs;

    // 移动端特定布局
    @include flex-column;
    gap: $spacing-sm;
  }
}
```

### 7. Arco Design 主题定制

项目已配置了 Arco Design 的主题定制：

```scss
// 自动应用自定义主题
.arco-btn {
  @include smooth-transition(all);
  font-weight: $font-weight-medium;
}

.arco-card {
  @include card-shadow;
  border-radius: $border-radius-lg;
}
```

### 8. 深度选择器和全局样式

```scss
// 深度选择器（影响子组件）
:deep(.arco-btn) {
  border-radius: $border-radius-lg;
}

// 全局样式
:global(.custom-class) {
  background: $primary-color-light;
}
```

## 最佳实践

### 1. 变量优先级

1. 优先使用设计令牌变量
2. 避免硬编码数值
3. 保持样式一致性

```scss
// ✅ 推荐
.component {
  padding: $spacing-base;
  color: $text-color-primary;
  border-radius: $border-radius-base;
}

// ❌ 不推荐
.component {
  padding: 16px;
  color: #262626;
  border-radius: 6px;
}
```

### 2. 混入函数使用

1. 抽象通用样式模式
2. 提高代码复用性
3. 保持样式一致性

```scss
// ✅ 推荐
.card {
  @include card-shadow;
  @include card-hover;
}

// ❌ 不推荐
.card {
  background: white;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
```

### 3. 嵌套规范

1. 最多 3 层嵌套
2. 合理使用 `&` 引用父选择器
3. 保持代码可读性

```scss
// ✅ 推荐
.account-card {
  &__header {
    @include flex-center-between;

    .title {
      font-weight: $font-weight-semibold;
    }
  }

  &--featured {
    border-color: $primary-color;
  }
}

// ❌ 不推荐（嵌套过深）
.account-card {
  .header {
    .title {
      .icon {
        .svg {
          fill: $primary-color;
        }
      }
    }
  }
}
```

### 4. 响应式设计

1. 移动端优先的设计原则
2. 使用响应式混入
3. 保持各断点的一致性

```scss
// ✅ 推荐
.component {
  // 移动端默认样式
  padding: $spacing-sm;
  font-size: $font-size-sm;

  // 平板和桌面端
  @include tablet {
    padding: $spacing-base;
    font-size: $font-size-base;
  }

  @include desktop {
    padding: $spacing-lg;
    font-size: $font-size-lg;
  }
}
```

## 常见问题

### Q: 如何自定义主题颜色？

A: 修改 `variables.scss` 中的颜色变量，系统会自动应用到所有组件。

### Q: 如何添加新的混入函数？

A: 在 `mixins.scss` 中添加新的 `@mixin`，会自动在所有组件中可用。

### Q: 如何覆盖 Arco Design 的样式？

A: 在 `arco-theme.scss` 中添加自定义样式，或在组件中使用深度选择器。

### Q: 为什么某些 SCSS 功能不生效？

A: 确保在 `<style>` 标签中添加 `lang="scss"` 属性。

## 参考资源

- [Sass 官方文档](https://sass-lang.com/documentation)
- [BEM 命名规范](http://getbem.com/)
- [Arco Design Vue](https://arco.design/vue)
- [CSS Grid 布局指南](https://css-tricks.com/snippets/css/complete-guide-grid/)
