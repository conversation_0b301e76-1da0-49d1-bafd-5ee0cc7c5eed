// src/styles/arco-theme.scss - Arco Design 主题定制
// 注意：variables.scss 和 mixins.scss 通过 Vite additionalData 自动导入

// =============================================
// 覆盖 Arco Design 的 CSS 变量
// =============================================
:root {
  // 主色调
  --color-primary-1: #e6f7ff;
  --color-primary-2: #bae7ff;
  --color-primary-3: #91d5ff;
  --color-primary-4: #69c0ff;
  --color-primary-5: #40a9ff;
  --color-primary-6: #{$primary-color};
  --color-primary-7: #0050b3;
  --color-primary-8: #003a8c;
  --color-primary-9: #002766;
  --color-primary-10: #001529;

  // 状态颜色
  --color-success-6: #{$success-color};
  --color-warning-6: #{$warning-color};
  --color-danger-6: #{$error-color};
  --color-info-6: #{$info-color};

  // 文本颜色
  --color-text-1: #{$text-color-primary};
  --color-text-2: #{$text-color-secondary};
  --color-text-3: #{$text-color-disabled};
  --color-text-4: #{$text-color-white};

  // 背景颜色
  --color-bg-1: #{$background-color-primary};
  --color-bg-2: #{$background-color-secondary};
  --color-bg-3: #{$background-color-dark};
  --color-bg-4: #{$background-color-darker};

  // 边框颜色
  --color-border-1: #{$border-color-light};
  --color-border-2: #{$border-color};
  --color-border-3: #{$border-color-dark};

  // 字体大小
  --font-size-body-1: #{$font-size-base};
  --font-size-body-2: #{$font-size-sm};
  --font-size-body-3: #{$font-size-xs};
  --font-size-caption: #{$font-size-xs};
  --font-size-title-1: #{$font-size-xxl};
  --font-size-title-2: #{$font-size-xl};
  --font-size-title-3: #{$font-size-lg};

  // 圆角
  --border-radius-small: #{$border-radius-sm};
  --border-radius-medium: #{$border-radius-base};
  --border-radius-large: #{$border-radius-lg};

  // 间距
  --spacing-mini: #{$spacing-xs};
  --spacing-small: #{$spacing-sm};
  --spacing-medium: #{$spacing-base};
  --spacing-large: #{$spacing-lg};
  --spacing-extra-large: #{$spacing-xl};

  // 阴影
  --shadow-1: #{$shadow-xs};
  --shadow-2: #{$shadow-sm};
  --shadow-3: #{$shadow-base};
  --shadow-4: #{$shadow-lg};
  --shadow-5: #{$shadow-xl};
}

// =============================================
// 自定义 Arco 组件样式
// =============================================

// 按钮组件
.arco-btn {
  @include smooth-transition(all);
  font-weight: $font-weight-medium;

  &.arco-btn-primary {
    background: $primary-color;
    border-color: $primary-color;

    &:hover {
      background: $primary-color-hover;
      border-color: $primary-color-hover;
    }

    &:active {
      background: $primary-color-active;
      border-color: $primary-color-active;
    }
  }

  &.arco-btn-secondary {
    border-color: $border-color;

    &:hover {
      border-color: $primary-color;
      color: $primary-color;
    }
  }

  &.arco-btn-outline {
    background: transparent;

    &.arco-btn-primary {
      color: $primary-color;
      border-color: $primary-color;

      &:hover {
        background: $primary-color-light;
      }
    }
  }

  // 按钮大小
  &.arco-btn-size-small {
    padding: $spacing-xs $spacing-sm;
    font-size: $font-size-xs;
  }

  &.arco-btn-size-medium {
    padding: $spacing-sm $spacing-base;
    font-size: $font-size-sm;
  }

  &.arco-btn-size-large {
    padding: $spacing-base $spacing-lg;
    font-size: $font-size-base;
  }
}

// 输入框组件
.arco-input {
  @include smooth-transition(border-color, box-shadow);

  &:focus-within {
    box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
  }

  .arco-input-inner {
    font-size: $font-size-base;
    line-height: $line-height-base;
  }
}

.arco-textarea {
  @include smooth-transition(border-color, box-shadow);

  &:focus-within {
    box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
  }
}

// 选择器组件
.arco-select {
  @include smooth-transition(border-color, box-shadow);

  &:focus-within {
    box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
  }
}

// 卡片组件
.arco-card {
  @include card-shadow;
  border-radius: $border-radius-lg;

  &.arco-card-hoverable {
    @include card-hover;
  }

  .arco-card-header {
    border-bottom: 1px solid $border-color-light;

    .arco-card-header-title {
      font-weight: $font-weight-semibold;
      color: $text-color-primary;
    }
  }

  .arco-card-body {
    color: $text-color-primary;
  }
}

// 表格组件
.arco-table {
  .arco-table-thead {
    .arco-table-th {
      background: $background-color-secondary;
      font-weight: $font-weight-semibold;
      color: $text-color-primary;
      border-bottom: 2px solid $border-color;
    }
  }

  .arco-table-tbody {
    .arco-table-tr {
      @include smooth-transition(background-color);

      &:hover {
        background: $background-color-dark;
      }

      .arco-table-td {
        border-bottom: 1px solid $border-color-light;
      }
    }
  }

  // 紧凑模式
  &.arco-table-size-small {
    .arco-table-th,
    .arco-table-td {
      padding: $spacing-xs $spacing-sm;
    }
  }
}

// 模态框组件
.arco-modal {
  .arco-modal-wrapper {
    .arco-modal {
      border-radius: $border-radius-lg;
      box-shadow: $shadow-xl;

      .arco-modal-header {
        border-bottom: 1px solid $border-color-light;

        .arco-modal-title {
          font-weight: $font-weight-semibold;
          color: $text-color-primary;
        }
      }

      .arco-modal-body {
        color: $text-color-primary;
      }

      .arco-modal-footer {
        border-top: 1px solid $border-color-light;
      }
    }
  }
}

// 抽屉组件
.arco-drawer {
  .arco-drawer-header {
    border-bottom: 1px solid $border-color-light;

    .arco-drawer-title {
      font-weight: $font-weight-semibold;
      color: $text-color-primary;
    }
  }

  .arco-drawer-body {
    color: $text-color-primary;
  }
}

// 菜单组件
.arco-menu {
  border-right: 1px solid $border-color-light;

  .arco-menu-item {
    @include smooth-transition(background-color, color);

    &:hover {
      background: $background-color-secondary;
      color: $primary-color;
    }

    &.arco-menu-selected {
      background: $primary-color-light;
      color: $primary-color;
      font-weight: $font-weight-medium;

      &::after {
        background: $primary-color;
      }
    }
  }

  .arco-menu-group-title {
    color: $text-color-secondary;
    font-weight: $font-weight-medium;
  }
}

// 消息提示组件
.arco-message {
  border-radius: $border-radius-base;
  box-shadow: $shadow-lg;

  &.arco-message-success {
    background: $success-color;
    color: $text-color-white;
  }

  &.arco-message-error {
    background: $error-color;
    color: $text-color-white;
  }

  &.arco-message-warning {
    background: $warning-color;
    color: $text-color-white;
  }

  &.arco-message-info {
    background: $info-color;
    color: $text-color-white;
  }
}

// 通知组件
.arco-notification {
  border-radius: $border-radius-lg;
  box-shadow: $shadow-xl;

  .arco-notification-title {
    font-weight: $font-weight-semibold;
    color: $text-color-primary;
  }

  .arco-notification-content {
    color: $text-color-secondary;
  }
}

// 表单组件
.arco-form {
  .arco-form-item-label {
    font-weight: $font-weight-medium;
    color: $text-color-primary;

    &.arco-form-item-label-required::before {
      color: $error-color;
    }
  }

  .arco-form-item-message {
    color: $error-color;
    font-size: $font-size-xs;
  }

  .arco-form-item-extra {
    color: $text-color-secondary;
    font-size: $font-size-xs;
  }
}

// 分页组件
.arco-pagination {
  .arco-pagination-item {
    @include smooth-transition(background-color, color);
    border-radius: $border-radius-base;

    &:hover {
      background: $background-color-secondary;
      color: $primary-color;
    }

    &.arco-pagination-item-active {
      background: $primary-color;
      color: $text-color-white;
    }
  }
}

// 标签页组件
.arco-tabs {
  .arco-tabs-nav {
    .arco-tabs-tab {
      @include smooth-transition(color, border-color);

      &:hover {
        color: $primary-color;
      }

      &.arco-tabs-tab-active {
        color: $primary-color;
        font-weight: $font-weight-medium;

        &::after {
          background: $primary-color;
        }
      }
    }
  }

  .arco-tabs-content {
    .arco-tabs-content-item {
      color: $text-color-primary;
    }
  }
}

// 下拉菜单组件
.arco-dropdown {
  .arco-dropdown-list {
    border-radius: $border-radius-base;
    box-shadow: $shadow-lg;

    .arco-dropdown-option {
      @include smooth-transition(background-color, color);

      &:hover {
        background: $background-color-secondary;
        color: $primary-color;
      }
    }
  }
}

// 工具提示组件
.arco-tooltip {
  .arco-tooltip-content {
    border-radius: $border-radius-base;
    font-size: $font-size-xs;
  }
}

// 气泡确认框组件
.arco-popconfirm {
  .arco-popconfirm-popup {
    border-radius: $border-radius-base;
    box-shadow: $shadow-lg;

    .arco-popconfirm-body {
      .arco-popconfirm-title {
        color: $text-color-primary;
        font-weight: $font-weight-medium;
      }

      .arco-popconfirm-content {
        color: $text-color-secondary;
      }
    }
  }
}