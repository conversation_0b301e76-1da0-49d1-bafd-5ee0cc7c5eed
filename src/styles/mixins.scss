// src/styles/mixins.scss - 常用混入函数库

// =============================================
// 布局混入
// =============================================
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-center-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-center-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

@mixin flex-column-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

// =============================================
// 卡片样式混入
// =============================================
@mixin card-shadow {
  background: $background-color-primary;
  box-shadow: $shadow-base;
  border: 1px solid $border-color-light;
  border-radius: $border-radius-lg;
}

@mixin card-hover {
  @include smooth-transition(transform, box-shadow);

  &:hover {
    box-shadow: $shadow-lg;
    transform: translateY(-2px);
  }
}

// =============================================
// 按钮样式混入
// =============================================
@mixin button-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 1px solid transparent;
  border-radius: $border-radius-base;
  font-weight: $font-weight-medium;
  text-decoration: none;
  cursor: pointer;
  outline: none;
  @include smooth-transition(all);

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

@mixin button-primary {
  @include button-base;
  background: $primary-color;
  border-color: $primary-color;
  color: $text-color-white;

  &:hover:not(:disabled) {
    background: $primary-color-hover;
    border-color: $primary-color-hover;
  }

  &:active:not(:disabled) {
    background: $primary-color-active;
    border-color: $primary-color-active;
  }
}

@mixin button-secondary {
  @include button-base;
  background: transparent;
  border-color: $border-color;
  color: $text-color-primary;

  &:hover:not(:disabled) {
    border-color: $primary-color;
    color: $primary-color;
  }
}

// =============================================
// 文本样式混入
// =============================================
@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin text-multiline-ellipsis($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

@mixin text-gradient($from, $to) {
  background: linear-gradient(45deg, $from, $to);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

// =============================================
// 响应式混入
// =============================================
@mixin mobile {
  @media (max-width: #{$breakpoint-mobile - 1px}) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: #{$breakpoint-mobile}) and (max-width: #{$breakpoint-tablet - 1px}) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: #{$breakpoint-tablet}) {
    @content;
  }
}

@mixin wide {
  @media (min-width: #{$breakpoint-desktop}) {
    @content;
  }
}

// =============================================
// 动画混入
// =============================================
@mixin smooth-transition($properties...) {
  @if length($properties) == 0 {
    $properties: all;
  }

  $transitions: ();
  @each $property in $properties {
    $transitions: append($transitions, $property $transition-duration-base $transition-timing, comma);
  }

  transition: $transitions;
}

@mixin fade-in {
  opacity: 0;
  animation: fadeIn $transition-duration-base $transition-timing forwards;
}

@mixin fade-in-up {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp $transition-duration-base $transition-timing forwards;
}

@mixin fade-in-down {
  opacity: 0;
  transform: translateY(-20px);
  animation: fadeInDown $transition-duration-base $transition-timing forwards;
}

@mixin scale-in {
  opacity: 0;
  transform: scale(0.9);
  animation: scaleIn $transition-duration-base $transition-timing forwards;
}

// 动画关键帧
@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// =============================================
// 滚动条样式混入
// =============================================
@mixin custom-scrollbar($width: 6px, $track-color: $background-color-secondary, $thumb-color: $border-color) {
  &::-webkit-scrollbar {
    width: $width;
    height: $width;
  }

  &::-webkit-scrollbar-track {
    background: $track-color;
  }

  &::-webkit-scrollbar-thumb {
    background: $thumb-color;
    border-radius: $border-radius-sm;

    &:hover {
      background: $border-color-dark;
    }
  }
}

// =============================================
// 表单样式混入
// =============================================
@mixin input-base {
  width: 100%;
  padding: $spacing-sm $spacing-base;
  border: 1px solid $border-color;
  border-radius: $border-radius-base;
  font-size: $font-size-base;
  line-height: $line-height-base;
  color: $text-color-primary;
  background: $background-color-primary;
  @include smooth-transition(border-color, box-shadow);

  &::placeholder {
    color: $text-color-disabled;
  }

  &:focus {
    border-color: $primary-color;
    outline: none;
    box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
  }

  &:disabled {
    background: $background-color-secondary;
    color: $text-color-disabled;
    cursor: not-allowed;
  }

  &.error {
    border-color: $error-color;

    &:focus {
      box-shadow: 0 0 0 2px rgba($error-color, 0.2);
    }
  }
}

// =============================================
// 工具混入
// =============================================
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

@mixin visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

@mixin aspect-ratio($width, $height) {
  position: relative;

  &::before {
    content: '';
    display: block;
    padding-top: percentage($height / $width);
  }

  > * {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}