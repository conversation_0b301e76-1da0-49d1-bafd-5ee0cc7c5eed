// src/styles/variables.scss - 设计令牌系统

// =============================================
// 颜色系统 - Color System
// =============================================

// 主色调 - Primary Colors
$primary-color: #1890ff;
$primary-color-light: #52c1ff;
$primary-color-lighter: #85d4ff;
$primary-color-lightest: #b8e7ff;
$primary-color-hover: #0b7ce8;
$primary-color-active: #096dd9;
$primary-color-dark: #0050b3;

// 别名变量 - Alias Variables (为了保持一致性)
$color-primary: $primary-color;

// 状态颜色 - Status Colors
$success-color: #52c41a;
$success-color-light: #7cb342;
$success-color-dark: #389e0d;

$warning-color: #faad14;
$warning-color-light: #ffc53d;
$warning-color-dark: #d48806;

$error-color: #ff4d4f;
$error-color-light: #ff7875;
$error-color-dark: #cf1322;

$info-color: #1890ff;
$info-color-light: #52c1ff;
$info-color-dark: #096dd9;

// 文本颜色 - Text Colors
$text-color-primary: #262626;
$text-color-secondary: #595959;
$text-color-tertiary: #8c8c8c;
$text-color-disabled: #bfbfbf;
$text-color-white: #ffffff;
$text-color-inverse: #ffffff;

// 背景颜色 - Background Colors
$background-color-primary: #ffffff;
$background-color-secondary: #fafafa;
$background-color-dark: #f5f5f5;
$background-color-darker: #f0f0f0;
$background-color-light: #fcfcfc;

// 边框颜色 - Border Colors
$border-color: #d9d9d9;
$border-color-light: #f0f0f0;
$border-color-dark: #bfbfbf;
$border-color-darker: #8c8c8c;

// =============================================
// 间距系统 - Spacing System
// =============================================
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-base: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
$spacing-xxl: 48px;

// =============================================
// 字体系统 - Typography System
// =============================================

// 字体大小 - Font Sizes
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-base: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-xxl: 24px;
$font-size-xxxl: 32px;

// 字重 - Font Weights
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// 行高 - Line Heights
$line-height-tight: 1.25;
$line-height-base: 1.5;
$line-height-loose: 1.75;

// 字体族 - Font Families
$font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
$font-family-code: 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', Consolas, 'Courier New', monospace;

// =============================================
// 圆角系统 - Border Radius System
// =============================================
$border-radius-xs: 2px;
$border-radius-sm: 4px;
$border-radius-base: 6px;
$border-radius-lg: 8px;
$border-radius-xl: 12px;
$border-radius-xxl: 16px;
$border-radius-circle: 50%;

// =============================================
// 阴影系统 - Shadow System
// =============================================
$shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.04);
$shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
$shadow-base: 0 4px 6px rgba(0, 0, 0, 0.05), 0 2px 4px rgba(0, 0, 0, 0.06);
$shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
$shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
$shadow-xxl: 0 25px 50px rgba(0, 0, 0, 0.15);

// 内阴影
$shadow-inner: inset 0 2px 4px rgba(0, 0, 0, 0.06);

// =============================================
// 响应式断点 - Responsive Breakpoints
// =============================================
$breakpoint-mobile: 768px;
$breakpoint-tablet: 1024px;
$breakpoint-desktop: 1280px;
$breakpoint-large: 1440px;

// =============================================
// Z-Index 层级 - Z-Index Scale
// =============================================
$z-index-dropdown: 1000;
$z-index-sticky: 1010;
$z-index-fixed: 1020;
$z-index-modal-backdrop: 1030;
$z-index-modal: 1040;
$z-index-popover: 1050;
$z-index-tooltip: 1060;
$z-index-toast: 1070;

// =============================================
// 动画系统 - Animation System
// =============================================

// 动画时长 - Animation Duration
$transition-duration-fast: 150ms;
$transition-duration-base: 200ms;
$transition-duration-slow: 300ms;

// 动画曲线 - Animation Timing
$transition-timing: cubic-bezier(0.4, 0, 0.2, 1);
$transition-timing-ease-in: cubic-bezier(0.4, 0, 1, 1);
$transition-timing-ease-out: cubic-bezier(0, 0, 0.2, 1);
$transition-timing-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

// =============================================
// 组件特定变量 - Component Specific Variables
// =============================================

// 表单控件
$input-height-sm: 24px;
$input-height-base: 32px;
$input-height-lg: 40px;
$input-padding-horizontal: 11px;
$input-padding-vertical: 4px;

// 按钮
$button-height-sm: 24px;
$button-height-base: 32px;
$button-height-lg: 40px;

// 卡片
$card-padding: $spacing-lg;
$card-radius: $border-radius-lg;

// 容器
$container-max-width: 1200px;
$sidebar-width: 240px;
$header-height: 60px;

// =============================================
// 主题变量 - Theme Variables
// =============================================

// 支持主题切换的变量映射
$theme-colors: (
  'primary': $primary-color,
  'success': $success-color,
  'warning': $warning-color,
  'error': $error-color,
  'info': $info-color
);

$theme-text-colors: (
  'primary': $text-color-primary,
  'secondary': $text-color-secondary,
  'tertiary': $text-color-tertiary,
  'disabled': $text-color-disabled,
  'white': $text-color-white
);

$theme-background-colors: (
  'primary': $background-color-primary,
  'secondary': $background-color-secondary,
  'dark': $background-color-dark,
  'darker': $background-color-darker
);
