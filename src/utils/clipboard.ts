/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-29 00:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-06-29 00:00:00
 * @FilePath     : /src/utils/clipboard.ts
 * @Description  : 剪贴板操作工具方法
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-29 00:00:00
 */

/**
 * 复制文本到剪贴板
 * @param text 要复制的文本
 * @param successMessage 成功提示消息，为空时不显示提示
 * @param errorMessage 失败提示消息，为空时不显示提示
 * @returns Promise<boolean> 是否复制成功
 */
export const copyToClipboard = async (
  text: string,
  successMessage: string = '已复制到剪贴板',
  errorMessage: string = '复制失败'
): Promise<boolean> => {
  try {
    await navigator.clipboard.writeText(text);

    // 动态导入消息组件避免循环依赖
    if (successMessage) {
      const { showSuccess } = await import('./message');
      showSuccess(successMessage);
    }

    return true;
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);

    // 动态导入消息组件避免循环依赖
    if (errorMessage) {
      const { showError } = await import('./message');
      showError(errorMessage);
    }

    return false;
  }
};

/**
 * 复制账号信息（用户名）
 * @param username 用户名
 */
export const copyUsername = async (username: string): Promise<boolean> => {
  return copyToClipboard(username, '用户名已复制');
};

/**
 * 复制密码
 * @param password 密码
 */
export const copyPassword = async (password: string): Promise<boolean> => {
  return copyToClipboard(password, '密码已复制');
};

/**
 * 复制邮箱
 * @param email 邮箱
 */
export const copyEmail = async (email: string): Promise<boolean> => {
  return copyToClipboard(email, '邮箱已复制');
};

/**
 * 复制手机号
 * @param phone 手机号
 */
export const copyPhone = async (phone: string): Promise<boolean> => {
  return copyToClipboard(phone, '手机号已复制');
};

/**
 * 复制网址
 * @param url 网址
 */
export const copyUrl = async (url: string): Promise<boolean> => {
  return copyToClipboard(url, '网址已复制');
};

/**
 * 复制账号ID
 * @param id 账号ID
 */
export const copyAccountId = async (id: string): Promise<boolean> => {
  return copyToClipboard(id, '账号ID已复制');
};

/**
 * 通用复制方法，无消息提示
 * @param text 要复制的文本
 */
export const copyText = async (text: string): Promise<boolean> => {
  return copyToClipboard(text, '', '');
};