/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-17
 * @Description  : Tauri API 适配器，根据环境提供合适的 API 实现
 * Copyright 2025 Bruce, All Rights Reserved.
 */

import { mockInvoke } from './mock-tauri';
import { mockSystem } from './mock-system';

// 检测是否在 Tauri 环境中运行
export const isTauriApp = window && '__TAURI__' in window;

// 导出通用 API 接口
export const api = {
  // 核心 API
  invoke: async <T>(cmd: string, args?: any): Promise<T> => {
    // 如果应该使用 mock，则使用 mock API
    if (mockSystem.isMockEnabled()) {
      return mockInvoke<T>(cmd, args);
    }

    // 否则使用真实 Tauri API
    try {
      const { invoke } = await import('@tauri-apps/api/core');
      return invoke<T>(cmd, args);
    } catch (error) {
      console.error('Failed to import Tauri API:', error);
      // 如果导入失败，回退到 mock API
      return mockInvoke<T>(cmd, args);
    }
  },

  // 可以添加更多 Tauri API 的适配
  // 例如 dialog, fs 等
};

// 导出一个辅助函数，用于在控制台显示当前运行环境
export const logEnvironment = () => {
  if (isTauriApp && !mockSystem.isMockEnabled()) {
    console.log('%c🚀 Running in Tauri environment', 'color: #ff9800; font-weight: bold;');
  } else if (isTauriApp && mockSystem.isMockEnabled()) {
    console.log('%c🔄 Running in Tauri environment with MOCK API ENABLED', 'color: #2196f3; font-weight: bold;');
  } else {
    console.log('%c🌐 Running in browser environment (using mock Tauri API)', 'color: #9c27b0; font-weight: bold;');
  }
};

// 导出 mock 系统，以便在应用中使用
export { mockSystem };