/**
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-20 00:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-20 21:35:00
 * @FilePath     : /src/utils/storage.ts
 * @Description  : 本地存储工具函数
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-20 00:00:00
 */

// 存储键名常量
export const STORAGE_KEYS = {
  FINANCE_FORM_DRAFT: 'finance_form_draft',
  FINANCE_FORM_TIMESTAMP: 'finance_form_timestamp'
} as const

/**
 * 保存数据到本地存储
 */
export const saveToStorage = <T>(key: string, data: T): void => {
  try {
    const jsonString = JSON.stringify(data)
    localStorage.setItem(key, jsonString)
    // 同时保存时间戳
    localStorage.setItem(`${key}_timestamp`, Date.now().toString())
  } catch (error) {
    console.error('保存到本地存储失败:', error)
  }
}

/**
 * 从本地存储读取数据
 */
export const getFromStorage = <T>(key: string): T | null => {
  try {
    const jsonString = localStorage.getItem(key)
    if (!jsonString) return null
    return JSON.parse(jsonString) as T
  } catch (error) {
    console.error('从本地存储读取失败:', error)
    return null
  }
}

/**
 * 删除本地存储数据
 */
export const removeFromStorage = (key: string): void => {
  try {
    localStorage.removeItem(key)
    localStorage.removeItem(`${key}_timestamp`)
  } catch (error) {
    console.error('删除本地存储失败:', error)
  }
}

/**
 * 获取数据保存时间戳
 */
export const getStorageTimestamp = (key: string): number | null => {
  try {
    const timestamp = localStorage.getItem(`${key}_timestamp`)
    return timestamp ? parseInt(timestamp) : null
  } catch (error) {
    console.error('获取时间戳失败:', error)
    return null
  }
}

/**
 * 检查数据是否过期
 */
export const isStorageExpired = (key: string, maxAge: number = 24 * 60 * 60 * 1000): boolean => {
  const timestamp = getStorageTimestamp(key)
  if (!timestamp) return true

  return Date.now() - timestamp > maxAge
}

/**
 * 格式化时间戳为可读时间
 */
export const formatTimestamp = (timestamp: number): string => {
  const date = new Date(timestamp)
  const now = new Date()
  const diffMs = now.getTime() - timestamp
  const diffMins = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffMins < 1) {
    return '刚刚'
  } else if (diffMins < 60) {
    return `${diffMins}分钟前`
  } else if (diffHours < 24) {
    return `${diffHours}小时前`
  } else if (diffDays < 7) {
    return `${diffDays}天前`
  } else {
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
}