/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-20
 * @Description  : 财务计算工具函数
 * Copyright 2025 Bruce, All Rights Reserved.
 */

import type { FinanceRecord } from './mock/finance';

/**
 * 计算总支出
 * @param record 财务记录
 * @returns 总支出金额
 */
export function calculateTotalExpense(record: FinanceRecord): number {
  return (
    (record.juan || 0) +
    (record.wechat || 0) +
    (record.huabei || 0) +
    (record.spdbCreditCard || 0) +
    (record.commCreditCard || 0) +
    (record.cmCreditCard || 0) +
    (record.jdBaitiao || 0) +
    (record.jdJintiao || 0) +
    (record.rent || 0) +
    (record.mortgage || 0)
  );
}

/**
 * 计算总收入
 * @param record 财务记录
 * @returns 总收入金额
 */
export function calculateTotalIncome(record: FinanceRecord): number {
  return (record.salary || 0) + (record.otherIncome || 0);
}

/**
 * 计算发薪后余额
 * @param record 财务记录
 * @returns 发薪后余额
 */
export function calculateBalanceAfterSalary(record: FinanceRecord): number {
  return calculateTotalIncome(record);
}

/**
 * 计算还款后余额
 * @param record 财务记录
 * @returns 还款后余额
 */
export function calculateBalanceAfterPayment(record: FinanceRecord): number {
  return calculateTotalIncome(record) - calculateTotalExpense(record);
}

/**
 * 计算最终余额
 * @param record 财务记录
 * @returns 最终余额
 */
export function calculateFinalBalance(record: FinanceRecord): number {
  return calculateBalanceAfterPayment(record);
}

/**
 * 计算当期结余（与最终余额相同）
 * @param record 财务记录
 * @returns 当期结余
 */
export function calculateCurrentBalance(record: FinanceRecord): number {
  return calculateFinalBalance(record);
}

/**
 * 验证财务记录数据的完整性
 * @param record 财务记录
 * @returns 验证结果
 */
export function validateFinanceRecord(record: FinanceRecord): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  // 检查必填字段
  if (!record.period) {
    errors.push('记账周期不能为空');
  }

  // 检查数值字段是否为负数
  const numericFields = [
    'salary', 'otherIncome', 'juan', 'wechat', 'huabei',
    'spdbCreditCard', 'commCreditCard', 'cmCreditCard',
    'jdBaitiao', 'jdJintiao', 'rent', 'mortgage'
  ];

  numericFields.forEach(field => {
    const value = (record as any)[field];
    if (value !== undefined && value < 0) {
      errors.push(`${field} 不能为负数`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 格式化财务记录，确保所有计算字段都是最新的
 * @param record 原始财务记录
 * @returns 格式化后的财务记录（注意：计算字段不存储在数据中，而是实时计算）
 */
export function formatFinanceRecord(record: FinanceRecord): FinanceRecord {
  // 只返回原始记录，计算字段通过函数实时计算
  return {
    ...record
  };
}

/**
 * 批量格式化财务记录列表
 * @param records 财务记录列表
 * @returns 格式化后的财务记录列表
 */
export function formatFinanceRecords(records: FinanceRecord[]): FinanceRecord[] {
  return records.map(record => formatFinanceRecord(record));
}
