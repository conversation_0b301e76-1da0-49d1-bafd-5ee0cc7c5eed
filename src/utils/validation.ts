/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-29 00:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-06-28 23:55:26
 * @FilePath     : /src/utils/validation.ts
 * @Description  : 表单验证工具方法
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-29 00:00:00
 */

import { isValidUrl } from './url';

/**
 * 验证邮箱格式
 * @param email 邮箱地址
 * @returns 是否为有效邮箱
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * 验证手机号格式（中国大陆）
 * @param phone 手机号
 * @returns 是否为有效手机号
 */
export const isValidPhone = (phone: string): boolean => {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
};

/**
 * 验证密码强度
 * @param password 密码
 * @returns 密码强度等级和描述
 */
export const validatePasswordStrength = (password: string): {
  level: 'weak' | 'medium' | 'strong';
  score: number;
  description: string;
} => {
  let score = 0;
  const checks = {
    length: password.length >= 8,
    lowercase: /[a-z]/.test(password),
    uppercase: /[A-Z]/.test(password),
    number: /\d/.test(password),
    special: /[!@#$%^&*(),.?":{}|<>]/.test(password),
  };

  // 计算分数
  if (checks.length) score += 2;
  if (checks.lowercase) score += 1;
  if (checks.uppercase) score += 1;
  if (checks.number) score += 1;
  if (checks.special) score += 2;

  // 额外加分
  if (password.length >= 12) score += 1;
  if (password.length >= 16) score += 1;

  let level: 'weak' | 'medium' | 'strong';
  let description: string;

  if (score < 4) {
    level = 'weak';
    description = '密码强度较弱，建议包含大小写字母、数字和特殊字符';
  } else if (score < 7) {
    level = 'medium';
    description = '密码强度中等，可进一步增加复杂度';
  } else {
    level = 'strong';
    description = '密码强度很好';
  }

  return { level, score, description };
};

/**
 * 验证必填字段
 * @param value 字段值
 * @param fieldName 字段名称
 * @returns 验证结果
 */
export const validateRequired = (value: any, fieldName: string): { valid: boolean; message: string } => {
  const valid = value !== null && value !== undefined && String(value).trim() !== '';
  return {
    valid,
    message: valid ? '' : `${fieldName}不能为空`
  };
};

/**
 * 验证字符串长度
 * @param value 字符串值
 * @param min 最小长度
 * @param max 最大长度
 * @param fieldName 字段名称
 * @returns 验证结果
 */
export const validateLength = (
  value: string,
  min: number,
  max: number,
  fieldName: string
): { valid: boolean; message: string } => {
  if (!value) {
    return { valid: true, message: '' }; // 空值在这里不处理，由required验证
  }

  const length = value.length;
  let valid = true;
  let message = '';

  if (length < min) {
    valid = false;
    message = `${fieldName}长度不能少于${min}个字符`;
  } else if (length > max) {
    valid = false;
    message = `${fieldName}长度不能超过${max}个字符`;
  }

  return { valid, message };
};

/**
 * 验证数字范围
 * @param value 数字值
 * @param min 最小值
 * @param max 最大值
 * @param fieldName 字段名称
 * @returns 验证结果
 */
export const validateNumberRange = (
  value: number,
  min: number,
  max: number,
  fieldName: string
): { valid: boolean; message: string } => {
  let valid = true;
  let message = '';

  if (value < min) {
    valid = false;
    message = `${fieldName}不能小于${min}`;
  } else if (value > max) {
    valid = false;
    message = `${fieldName}不能大于${max}`;
  }

  return { valid, message };
};

/**
 * 验证正则表达式
 * @param value 字段值
 * @param pattern 正则表达式
 * @param fieldName 字段名称
 * @param errorMessage 错误消息
 * @returns 验证结果
 */
export const validatePattern = (
  value: string,
  pattern: RegExp,
  fieldName: string,
  errorMessage?: string
): { valid: boolean; message: string } => {
  if (!value) {
    return { valid: true, message: '' };
  }

  const valid = pattern.test(value);
  return {
    valid,
    message: valid ? '' : (errorMessage || `${fieldName}格式不正确`)
  };
};

/**
 * 通用字段验证器
 * @param value 字段值
 * @param rules 验证规则
 * @returns 验证结果
 */
export const validateField = (
  value: any,
  rules: {
    required?: boolean;
    min?: number;
    max?: number;
    pattern?: RegExp;
    email?: boolean;
    phone?: boolean;
    url?: boolean;
    custom?: (value: any) => boolean;
    message?: string;
    fieldName: string;
  }
): { valid: boolean; message: string } => {
  const { fieldName } = rules;

  // 必填验证
  if (rules.required) {
    const requiredResult = validateRequired(value, fieldName);
    if (!requiredResult.valid) {
      return requiredResult;
    }
  }

  // 如果值为空且不是必填，则跳过其他验证
  if (!value || String(value).trim() === '') {
    return { valid: true, message: '' };
  }

  const stringValue = String(value);

  // 长度验证
  if (rules.min !== undefined || rules.max !== undefined) {
    const min = rules.min || 0;
    const max = rules.max || Infinity;
    const lengthResult = validateLength(stringValue, min, max, fieldName);
    if (!lengthResult.valid) {
      return lengthResult;
    }
  }

  // 邮箱验证
  if (rules.email && !isValidEmail(stringValue)) {
    return { valid: false, message: `${fieldName}格式不正确` };
  }

  // 手机号验证
  if (rules.phone && !isValidPhone(stringValue)) {
    return { valid: false, message: `${fieldName}格式不正确` };
  }

  // URL验证
  if (rules.url && !isValidUrl(stringValue)) {
    return { valid: false, message: `${fieldName}格式不正确` };
  }

  // 正则表达式验证
  if (rules.pattern) {
    const patternResult = validatePattern(stringValue, rules.pattern, fieldName, rules.message);
    if (!patternResult.valid) {
      return patternResult;
    }
  }

  // 自定义验证
  if (rules.custom && !rules.custom(value)) {
    return { valid: false, message: rules.message || `${fieldName}验证失败` };
  }

  return { valid: true, message: '' };
};

/**
 * 验证表单数据
 * @param data 表单数据
 * @param rules 验证规则配置
 * @returns 验证结果
 */
export const validateForm = (
  data: Record<string, any>,
  rules: Record<string, any>
): { valid: boolean; errors: Record<string, string>; firstError?: string } => {
  const errors: Record<string, string> = {};
  let firstError: string | undefined;

  for (const [field, fieldRules] of Object.entries(rules)) {
    const value = data[field];
    const result = validateField(value, { ...fieldRules, fieldName: fieldRules.fieldName || field });

    if (!result.valid) {
      errors[field] = result.message;
      if (!firstError) {
        firstError = result.message;
      }
    }
  }

  return {
    valid: Object.keys(errors).length === 0,
    errors,
    firstError
  };
};

// 常用验证规则预设
export const commonRules = {
  required: (fieldName: string) => ({
    required: true,
    fieldName
  }),

  email: (fieldName: string = '邮箱', required: boolean = false) => ({
    email: true,
    required,
    fieldName
  }),

  phone: (fieldName: string = '手机号', required: boolean = false) => ({
    phone: true,
    required,
    fieldName
  }),

  url: (fieldName: string = '网址', required: boolean = false) => ({
    url: true,
    required,
    fieldName
  }),

  password: (fieldName: string = '密码', minLength: number = 6) => ({
    required: true,
    min: minLength,
    fieldName
  }),

  username: (fieldName: string = '用户名') => ({
    required: true,
    min: 2,
    max: 50,
    fieldName
  }),

  platform: (fieldName: string = '平台名称') => ({
    required: true,
    min: 1,
    max: 100,
    fieldName
  })
};