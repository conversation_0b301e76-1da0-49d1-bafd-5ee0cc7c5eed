/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-29 00:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-06-28 23:54:48
 * @FilePath     : /src/utils/url.ts
 * @Description  : URL处理工具方法
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-29 00:00:00
 */

/**
 * 从URL中提取域名
 * @param url URL字符串
 * @returns 域名字符串
 */
export const getDomainFromUrl = (url: string): string => {
  try {
    const domain = new URL(url.startsWith("http") ? url : `https://${url}`)
      .hostname;
    return domain.replace("www.", "");
  } catch {
    return url;
  }
};

/**
 * 验证URL格式是否正确
 * @param url URL字符串
 * @returns 是否为有效URL
 */
export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url.startsWith("http") ? url : `https://${url}`);
    return true;
  } catch {
    return false;
  }
};

/**
 * 格式化URL，确保有协议前缀
 * @param url URL字符串
 * @param defaultProtocol 默认协议，默认为https
 * @returns 格式化后的URL
 */
export const formatUrl = (url: string, defaultProtocol: string = 'https'): string => {
  if (!url) return '';

  const trimmedUrl = url.trim();
  if (trimmedUrl.startsWith('http://') || trimmedUrl.startsWith('https://')) {
    return trimmedUrl;
  }

  return `${defaultProtocol}://${trimmedUrl}`;
};

/**
 * 获取URL的协议
 * @param url URL字符串
 * @returns 协议字符串（如 'https:', 'http:'）
 */
export const getUrlProtocol = (url: string): string => {
  try {
    const urlObj = new URL(url.startsWith("http") ? url : `https://${url}`);
    return urlObj.protocol;
  } catch {
    return '';
  }
};

/**
 * 获取URL的路径部分
 * @param url URL字符串
 * @returns 路径字符串
 */
export const getUrlPath = (url: string): string => {
  try {
    const urlObj = new URL(url.startsWith("http") ? url : `https://${url}`);
    return urlObj.pathname;
  } catch {
    return '';
  }
};

/**
 * 获取URL的查询参数
 * @param url URL字符串
 * @returns 查询参数对象
 */
export const getUrlParams = (url: string): Record<string, string> => {
  try {
    const urlObj = new URL(url.startsWith("http") ? url : `https://${url}`);
    const params: Record<string, string> = {};
    urlObj.searchParams.forEach((value, key) => {
      params[key] = value;
    });
    return params;
  } catch {
    return {};
  }
};

/**
 * 构建URL查询字符串
 * @param params 参数对象
 * @returns 查询字符串
 */
export const buildQueryString = (params: Record<string, any>): string => {
  const searchParams = new URLSearchParams();
  Object.entries(params).forEach(([key, value]) => {
    if (value !== null && value !== undefined && value !== '') {
      searchParams.append(key, String(value));
    }
  });
  return searchParams.toString();
};

/**
 * 在URL上添加查询参数
 * @param url 基础URL
 * @param params 要添加的参数
 * @returns 带参数的URL
 */
export const addUrlParams = (url: string, params: Record<string, any>): string => {
  const formattedUrl = formatUrl(url);
  const queryString = buildQueryString(params);

  if (!queryString) return formattedUrl;

  const separator = formattedUrl.includes('?') ? '&' : '?';
  return `${formattedUrl}${separator}${queryString}`;
};

/**
 * 简化显示的URL（移除协议和www）
 * @param url URL字符串
 * @returns 简化后的URL
 */
export const getDisplayUrl = (url: string): string => {
  try {
    const urlObj = new URL(url.startsWith("http") ? url : `https://${url}`);
    return urlObj.hostname.replace("www.", "") + urlObj.pathname;
  } catch {
    return url;
  }
};

/**
 * 检查URL是否为HTTPS
 * @param url URL字符串
 * @returns 是否为HTTPS
 */
export const isHttps = (url: string): boolean => {
  return getUrlProtocol(url) === 'https:';
};

/**
 * 获取网站的favicon URL
 * @param url 网站URL
 * @returns favicon URL
 */
export const getFaviconUrl = (url: string): string => {
  try {
    const urlObj = new URL(url.startsWith("http") ? url : `https://${url}`);
    return `${urlObj.protocol}//${urlObj.hostname}/favicon.ico`;
  } catch {
    return '';
  }
};