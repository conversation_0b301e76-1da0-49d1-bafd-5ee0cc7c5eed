/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-18
 * @Description  : 财务模块的 Mock 数据
 * Copyright 2025 Bruce, All Rights Reserved.
 */

import { createMockResponse } from '../mock-system';

// 资金流转记录数据接口
export interface FinanceRecord {
  id: string;
  period: string;
  salary: number;              // 工资收入
  otherIncome: number;         // 其他收入
  juan: number;                // 娟
  wechat: number;              // 微信
  huabei: number;              // 花呗
  spdbCreditCard: number;      // 浦发信用卡
  commCreditCard: number;      // 交行信用卡
  cmCreditCard: number;        // 招商信用卡
  jdBaitiao: number;           // 京东白条
  jdJintiao: number;           // 京东金条
  rent: number;                // 房租
  mortgage: number;            // 房贷
  alipay?: number;             // 支付宝余额
  wechatPay?: number;          // 微信支付余额
  actualBalance?: number;      // 实际结余
  remark?: string;
}

// 分页数据接口
export interface PaginatedResponse<T> {
  records: T[];
  total: number;
  page: number;
  pageSize: number;
}

// 周期选项
export const cycleOptions = [
  { value: "202506-202507", label: "2025-06-15 至 2025-07-14" },
  { value: "202505-202506", label: "2025-05-15 至 2025-06-14" },
  { value: "202504-202505", label: "2025-04-15 至 2025-05-14" },
  { value: "202503-202504", label: "2025-03-15 至 2025-04-14" },
];

// Mock 资金流转记录数据
const mockFinanceRecords: FinanceRecord[] = [
  {
    id: "1",
    period: "2025-06-15 至 2025-07-14",
    salary: 80,
    otherIncome: 20,
    juan: 50,
    wechat: 30,
    huabei: 80,
    spdbCreditCard: 120,
    commCreditCard: 90,
    cmCreditCard: 110,
    jdBaitiao: 40,
    jdJintiao: 60,
    rent: 200,
    mortgage: 150,
    alipay: 1500,
    wechatPay: 800,
    actualBalance: -930,
  },
  {
    id: "2",
    period: "2025-05-15 至 2025-06-14",
    salary: 75,
    otherIncome: 20,
    juan: 45,
    wechat: 25,
    huabei: 70,
    spdbCreditCard: 100,
    commCreditCard: 85,
    cmCreditCard: 95,
    jdBaitiao: 35,
    jdJintiao: 50,
    rent: 200,
    mortgage: 150,
    alipay: 1200,
    wechatPay: 600,
    actualBalance: -855,
  },
  {
    id: "3",
    period: "2025-04-15 至 2025-05-14",
    salary: 85,
    otherIncome: 20,
    juan: 55,
    wechat: 35,
    huabei: 75,
    spdbCreditCard: 110,
    commCreditCard: 80,
    cmCreditCard: 100,
    jdBaitiao: 45,
    jdJintiao: 55,
    rent: 200,
    mortgage: 150,
    alipay: 1800,
    wechatPay: 900,
    actualBalance: -795,
  },
];

// 获取资金流转记录列表（带分页）
export async function getFinanceRecordList(
  params: {
    page?: number;
    pageSize?: number;
    cycle?: string;
    dateRange?: string[];
    accountType?: string;
  } = {}
): Promise<PaginatedResponse<FinanceRecord>> {
  const { page = 1, pageSize = 10 } = params;

  // 这里可以根据传入的参数进行过滤，目前简单返回所有数据
  return createMockResponse({
    records: mockFinanceRecords,
    total: mockFinanceRecords.length,
    page,
    pageSize
  });
}

// 获取单条资金流转记录
export async function getFinanceRecordById(id: string): Promise<FinanceRecord | null> {
  const record = mockFinanceRecords.find(item => item.id === id);

  if (!record) {
    return createMockResponse(null);
  }

  return createMockResponse(record);
}

// 添加资金流转记录
export async function addFinanceRecord(record: Omit<FinanceRecord, 'id'>): Promise<{ success: boolean; id: string }> {
  // 生成新ID
  const newId = `${Date.now()}`;

  // 在实际应用中，这里会将数据发送到后端
  return createMockResponse({
    success: true,
    id: newId
  });
}

// 更新资金流转记录
export async function updateFinanceRecord(record: FinanceRecord): Promise<{ success: boolean }> {
  // 在实际应用中，这里会将数据发送到后端
  return createMockResponse({
    success: true
  });
}

// 删除资金流转记录
export async function deleteFinanceRecord(id: string): Promise<{ success: boolean }> {
  // 在实际应用中，这里会将ID发送到后端
  return createMockResponse({
    success: true
  });
}