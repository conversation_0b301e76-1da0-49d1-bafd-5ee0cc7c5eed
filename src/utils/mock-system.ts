/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-17
 * @Description  : 全局 Mock 系统，支持在真实 API 和模拟 API 之间切换
 * Copyright 2025 Bruce, All Rights Reserved.
 */

// Mock 系统配置
interface MockSystemConfig {
  // 是否启用 mock
  enabled: boolean;
  // 模拟延迟时间 (ms)
  delay: number;
  // 各模块的mock状态
  modules: {
    accounts: boolean;
    finance: boolean;
    // 其他模块...
  };
}

// Mock 系统状态
class MockSystem {
  private static instance: MockSystem;
  private _config: MockSystemConfig = {
    enabled: false,
    delay: 300,
    modules: {
      accounts: false,
      finance: false
    }
  };

  // 获取单例实例
  public static getInstance(): MockSystem {
    if (!MockSystem.instance) {
      MockSystem.instance = new MockSystem();
    }
    return MockSystem.instance;
  }

  // 初始化 mock 系统
  public init(): void {
    // 从本地存储加载配置
    const savedConfig = localStorage.getItem('mock_system_config');
    if (savedConfig) {
      try {
        this._config = { ...this._config, ...JSON.parse(savedConfig) };
      } catch (e) {
        console.error('Failed to parse mock system config', e);
      }
    }

    // 检查 URL 参数是否有覆盖设置
    const urlParams = new URLSearchParams(window.location.search);
    const mockParam = urlParams.get('mock');
    if (mockParam !== null) {
      this._config.enabled = mockParam === 'true';

      // 同步所有模块的状态
      this.syncModulesState();

      this.saveConfig();
    }

    this.logStatus();
  }

  // 同步所有模块的状态
  private syncModulesState(): void {
    const enabled = this._config.enabled;
    Object.keys(this._config.modules).forEach(key => {
      (this._config.modules as any)[key] = enabled;
    });
  }

  // 检查是否应该使用mock
  public isMockEnabled(): boolean {
    // 检查是否在Tauri环境中运行
    const isTauriApp = window && '__TAURI__' in window;

    // 如果不在Tauri环境中，且全局mock开关未被明确设置为false，则使用mock
    if (!isTauriApp && this._config.enabled !== false) {
      return true;
    }

    return this._config.enabled;
  }

  // 检查特定模块是否应该使用mock
  public isModuleMockEnabled(moduleName: keyof MockSystemConfig['modules']): boolean {
    if (!this._config.enabled) {
      return false;
    }

    return this._config.modules[moduleName];
  }

  // 设置全局mock状态
  public setEnabled(enabled: boolean): void {
    this._config.enabled = enabled;

    // 同步所有模块的状态
    this.syncModulesState();

    this.saveConfig();
    this.logStatus();
  }

  // 设置特定模块的mock状态
  public setModuleEnabled(moduleName: keyof MockSystemConfig['modules'], enabled: boolean): void {
    this._config.modules[moduleName] = enabled;
    this.saveConfig();
  }

  // 获取延迟时间
  public getDelay(): number {
    return this._config.delay;
  }

  // 设置延迟时间
  public setDelay(delay: number): void {
    this._config.delay = delay;
    this.saveConfig();
  }

  // 保存配置到本地存储
  private saveConfig(): void {
    localStorage.setItem('mock_system_config', JSON.stringify(this._config));
  }

  // 记录当前状态
  private logStatus(): void {
    console.log(
      `%c[Mock System] ${this._config.enabled ? 'ENABLED' : 'DISABLED'}`,
      `color: ${this._config.enabled ? '#4caf50' : '#f44336'}; font-weight: bold;`
    );
  }

  // 添加日志方法
  public log(...args: any[]): void {
    if (this._config.enabled) {
      console.log('%c[Mock System]', 'color: #2196f3; font-weight: bold;', ...args);
    }
  }

  // 模拟延迟
  public async delay(): Promise<void> {
    if (this._config.enabled && this._config.delay > 0) {
      return new Promise((resolve) => setTimeout(resolve, this._config.delay));
    }
    return Promise.resolve();
  }

  // 重置mock数据
  public resetData(): void {
    // 清除本地存储中的mock数据
    localStorage.removeItem('account_manager_mock_data');
    localStorage.removeItem('finance_manager_mock_data');

    // 重新加载页面以应用更改
    window.location.reload();
  }
}

// 导出单例实例
export const mockSystem = MockSystem.getInstance();

// 导出一个创建带延迟的 mock 响应的辅助函数
export async function createMockResponse<T>(data: T): Promise<T> {
  await mockSystem.delay();
  return data;
}