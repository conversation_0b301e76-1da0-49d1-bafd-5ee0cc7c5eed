/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-28 02:40:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-17
 * @FilePath     : /src/utils/auth-interceptor.ts
 * @Description  : 认证拦截器，统一处理认证状态失效的情况
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-28 02:40:00
 */

import { useAuthStore } from '../stores/auth'
import { Message } from '@arco-design/web-vue'
import { mockSystem } from './mock-system'

export class AuthInterceptor {
  private static instance: AuthInterceptor
  private router: any = null

  private constructor() {}

  static getInstance(): AuthInterceptor {
    if (!AuthInterceptor.instance) {
      AuthInterceptor.instance = new AuthInterceptor()
    }
    return AuthInterceptor.instance
  }

  // 设置路由实例
  setRouter(router: any) {
    this.router = router
  }

  // 检查认证状态并处理失效情况
  async checkAuthAndHandle(): Promise<boolean> {
    try {
      console.log('🔒 [Auth Interceptor] Checking authentication status...')

      // 如果在浏览器环境中使用 mock API，简化认证检查
      if (mockSystem.isMockEnabled()) {
        console.log('🔒 [Auth Interceptor] Using mock API, simplified auth check')
        const localIsAuthenticated = localStorage.getItem('isAuthenticated') === 'true'
        if (!localIsAuthenticated) {
          console.log('🔒 [Auth Interceptor] No local auth found in mock mode')
          this.handleAuthFailure('本地认证状态已失效')
          return false
        }
        return true
      }

      const authStore = useAuthStore()

      // 1. 检查前端认证状态
      const localIsAuthenticated = localStorage.getItem('isAuthenticated') === 'true'
      console.log('🔒 [Auth Interceptor] Local auth status:', localIsAuthenticated)

      if (!localIsAuthenticated) {
        console.log('🔒 [Auth Interceptor] No local auth found')
        this.handleAuthFailure('本地认证状态已失效')
        return false
      }

      // 2. 检查后端认证状态
      console.log('🔒 [Auth Interceptor] Checking backend auth status...')
      const backendIsAuthenticated = await authStore.checkAuthStatus()
      console.log('🔒 [Auth Interceptor] Backend auth status:', backendIsAuthenticated)

      if (!backendIsAuthenticated) {
        console.log('🔒 [Auth Interceptor] Backend auth failed')
        this.handleAuthFailure('认证状态已失效，请重新登录')
        return false
      }

      console.log('🔒 [Auth Interceptor] Authentication check passed')
      return true
    } catch (error) {
      console.error('🔒 [Auth Interceptor] Auth check failed:', error)

      // 在浏览器环境中使用 mock API 时，忽略认证错误
      if (mockSystem.isMockEnabled()) {
        console.log('🔒 [Auth Interceptor] Using mock API, ignoring auth error')
        return true
      }

      this.handleAuthFailure('认证检查失败，请重新登录')
      return false
    }
  }

  // 处理认证失败的情况
  private handleAuthFailure(message: string) {
    console.log('🔒 [Auth Interceptor] Handling auth failure:', message)

    const authStore = useAuthStore()

    // 清除认证状态
    authStore.isAuthenticated = false
    localStorage.removeItem('isAuthenticated')

    // 显示提示信息
    Message.warning(message)

    // 跳转到登录页面
    if (this.router) {
      console.log('🔒 [Auth Interceptor] Redirecting to login page...')
      this.router.push('/login').catch((error: any) => {
        console.error('🔒 [Auth Interceptor] Navigation error:', error)
      })
    } else {
      console.warn('🔒 [Auth Interceptor] Router not available, cannot redirect')
      // 如果路由不可用，使用window.location
      window.location.hash = '#/login'
    }
  }

  // 包装异步函数，自动处理认证检查
  async wrapWithAuthCheck<T>(
    asyncFunction: () => Promise<T>,
    skipAuthCheck: boolean = false
  ): Promise<T> {
    if (!skipAuthCheck) {
      const isAuthenticated = await this.checkAuthAndHandle()
      if (!isAuthenticated) {
        throw new Error('认证状态失效')
      }
    }

    try {
      return await asyncFunction()
    } catch (error) {
      // 检查是否是认证相关错误
      const errorMessage = String(error)
      if (this.isAuthError(errorMessage)) {
        console.log('🔒 [Auth Interceptor] Detected auth error in API response:', errorMessage)

        // 在浏览器环境中使用 mock API 时，忽略认证错误
        if (mockSystem.isMockEnabled()) {
          console.log('🔒 [Auth Interceptor] Using mock API, ignoring auth error')
          throw error
        }

        this.handleAuthFailure('操作失败，认证状态已失效')
        throw error
      }
      throw error
    }
  }

  // 判断是否是认证相关错误
  private isAuthError(errorMessage: string): boolean {
    const authErrorKeywords = [
      'unlock',
      'auth',
      'authentication',
      'unauthorized',
      '认证',
      '登录',
      'master password',
      'Please unlock'
    ]

    return authErrorKeywords.some(keyword =>
      errorMessage.toLowerCase().includes(keyword.toLowerCase())
    )
  }
}

// 导出单例实例
export const authInterceptor = AuthInterceptor.getInstance()

// 便捷方法
export const withAuthCheck = async <T>(
  asyncFunction: () => Promise<T>,
  skipAuthCheck: boolean = false
): Promise<T> => {
  return authInterceptor.wrapWithAuthCheck(asyncFunction, skipAuthCheck)
}