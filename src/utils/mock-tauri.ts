/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-17
 * @Description  : 模拟 Tauri API 用于浏览器开发环境
 * Copyright 2025 Bruce, All Rights Reserved.
 */

import { mockSystem, createMockResponse } from './mock-system';

// 定义账户类型
interface Account {
  id: string;
  [key: string]: any;
}

// 模拟数据存储
const MOCK_DATA_KEY = 'account_manager_mock_data';

// 从本地存储加载模拟数据
const loadMockData = (): {
  isMasterPasswordSet: boolean;
  isAuthenticated: boolean;
  masterPassword: string;
  accounts: Account[];
} => {
  try {
    const savedData = localStorage.getItem(MOCK_DATA_KEY);
    if (savedData) {
      return JSON.parse(savedData);
    }
  } catch (e) {
    console.error('Failed to load mock data from localStorage', e);
  }

  return {
    isMasterPasswordSet: false,
    isAuthenticated: false,
    masterPassword: '',
    accounts: []
  };
};

// 保存模拟数据到本地存储
const saveMockData = (data: any): void => {
  try {
    localStorage.setItem(MOCK_DATA_KEY, JSON.stringify(data));
  } catch (e) {
    console.error('Failed to save mock data to localStorage', e);
  }
};

// 初始化模拟数据
const mockData = loadMockData();

// 模拟 Tauri 的 invoke 函数
export async function mockInvoke<T>(cmd: string, args?: any): Promise<T> {
  // 记录操作
  mockSystem.log('invoke', cmd, args);

  // 根据命令返回模拟数据
  switch (cmd) {
    case 'init_database':
      mockSystem.log('初始化数据库');
      return await createMockResponse(true as unknown as T);

    case 'is_master_password_set':
      return await createMockResponse(mockData.isMasterPasswordSet as unknown as T);

    case 'verify_master_password':
      if (!mockData.isMasterPasswordSet) {
        // 首次设置密码
        mockData.masterPassword = args?.password || '';
        mockData.isMasterPasswordSet = true;
        mockData.isAuthenticated = true;
        saveMockData(mockData);
        return await createMockResponse(true as unknown as T);
      } else {
        // 验证密码
        const result = args?.password === mockData.masterPassword;
        if (result) {
          mockData.isAuthenticated = true;
          saveMockData(mockData);
        }
        return await createMockResponse(result as unknown as T);
      }

    case 'check_auth_status':
      // 在浏览器环境中，如果本地存储显示已认证，则总是返回已认证状态
      const localIsAuthenticated = localStorage.getItem('isAuthenticated') === 'true';
      if (localIsAuthenticated) {
        mockData.isAuthenticated = true;
        saveMockData(mockData);
      }
      return await createMockResponse(mockData.isAuthenticated as unknown as T);

    case 'lock_app':
      mockData.isAuthenticated = false;
      saveMockData(mockData);
      return await createMockResponse(true as unknown as T);

    // 账户相关 API
    case 'get_accounts':
      return await createMockResponse(mockData.accounts as unknown as T);

    case 'add_account':
      if (args?.account) {
        const newAccount: Account = {
          id: Date.now().toString(),
          ...args.account
        };
        mockData.accounts.push(newAccount);
        saveMockData(mockData);
        return await createMockResponse(newAccount as unknown as T);
      }
      return await createMockResponse(null as unknown as T);

    case 'update_account':
      if (args?.id && args?.account) {
        const index = mockData.accounts.findIndex(a => a.id === args.id);
        if (index >= 0) {
          mockData.accounts[index] = { ...mockData.accounts[index], ...args.account };
          saveMockData(mockData);
          return await createMockResponse(mockData.accounts[index] as unknown as T);
        }
      }
      return await createMockResponse(null as unknown as T);

    case 'delete_account':
      if (args?.id) {
        const index = mockData.accounts.findIndex(a => a.id === args.id);
        if (index >= 0) {
          mockData.accounts.splice(index, 1);
          saveMockData(mockData);
          return await createMockResponse(true as unknown as T);
        }
      }
      return await createMockResponse(false as unknown as T);

    // 添加更多需要模拟的命令
    default:
      mockSystem.log('未实现的命令', cmd);
      return await createMockResponse(null as unknown as T);
  }
}

// 重置模拟数据（用于测试）
export function resetMockData(): void {
  mockData.isMasterPasswordSet = false;
  mockData.isAuthenticated = false;
  mockData.masterPassword = '';
  mockData.accounts = [];
  saveMockData(mockData);
  mockSystem.log('重置模拟数据');
}