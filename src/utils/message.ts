/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-29 00:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-06-28 23:54:24
 * @FilePath     : /src/utils/message.ts
 * @Description  : 消息提示工具方法
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-29 00:00:00
 */

import { Message } from '@arco-design/web-vue';

/**
 * 显示成功消息
 * @param content 消息内容
 * @param duration 显示时长（毫秒），默认3000
 */
export const showSuccess = (content: string, duration: number = 3000) => {
  Message.success({
    content,
    duration
  });
};

/**
 * 显示错误消息
 * @param content 消息内容
 * @param duration 显示时长（毫秒），默认3000
 */
export const showError = (content: string, duration: number = 3000) => {
  Message.error({
    content,
    duration
  });
};

/**
 * 显示警告消息
 * @param content 消息内容
 * @param duration 显示时长（毫秒），默认3000
 */
export const showWarning = (content: string, duration: number = 3000) => {
  Message.warning({
    content,
    duration
  });
};

/**
 * 显示信息消息
 * @param content 消息内容
 * @param duration 显示时长（毫秒），默认3000
 */
export const showInfo = (content: string, duration: number = 3000) => {
  Message.info({
    content,
    duration
  });
};

/**
 * 显示加载中消息
 * @param content 消息内容
 * @param duration 显示时长（毫秒），默认0表示不自动关闭
 */
export const showLoading = (content: string, duration: number = 0) => {
  return Message.loading({
    content,
    duration
  });
};

// 操作相关的消息
export const showOperationSuccess = (operation: string) => {
  showSuccess(`${operation}成功`);
};

export const showOperationError = (operation: string, error?: any) => {
  const errorMessage = error?.message || String(error) || '';
  showError(`${operation}失败${errorMessage ? '：' + errorMessage : ''}`);
};

// 数据操作消息
export const showSaveSuccess = () => showSuccess('保存成功');
export const showSaveError = (error?: any) => showOperationError('保存', error);

export const showDeleteSuccess = () => showSuccess('删除成功');
export const showDeleteError = (error?: any) => showOperationError('删除', error);

export const showCreateSuccess = () => showSuccess('创建成功');
export const showCreateError = (error?: any) => showOperationError('创建', error);

export const showUpdateSuccess = () => showSuccess('更新成功');
export const showUpdateError = (error?: any) => showOperationError('更新', error);

export const showLoadError = (error?: any) => showOperationError('加载', error);

// 表单验证消息
export const showValidationError = (message: string) => {
  showWarning(message);
};

// 认证相关消息
export const showLoginSuccess = () => showSuccess('登录成功');
export const showLoginError = () => showError('登录失败，请检查密码');
export const showUnlockSuccess = () => showSuccess('解锁成功');
export const showPasswordError = () => showError('密码错误');

// 导入导出消息
export const showExportSuccess = (message?: string) => showSuccess(message || '导出成功');
export const showExportError = (error?: any) => showOperationError('导出', error);
export const showImportSuccess = (message?: string) => showSuccess(message || '导入成功');
export const showImportError = (error?: any) => showOperationError('导入', error);