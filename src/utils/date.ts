/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-29 00:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-06-29 00:00:00
 * @FilePath     : /src/utils/date.ts
 * @Description  : 日期格式化工具方法
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-29 00:00:00
 */

/**
 * 格式化日期为相对时间（今天、昨天、X天前、具体日期）
 * @param dateString 日期字符串
 * @returns 格式化后的日期字符串
 */
export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - date.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 1) return "今天";
  if (diffDays === 2) return "昨天";
  if (diffDays <= 7) return `${diffDays - 1}天前`;

  return date.toLocaleDateString("zh-CN");
};

/**
 * 格式化日期时间为详细格式（年-月-日 时:分）
 * @param dateString 日期字符串
 * @returns 格式化后的日期时间字符串
 */
export const formatDateTime = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
};

/**
 * 格式化日期为简短格式（月-日）
 * @param dateString 日期字符串
 * @returns 格式化后的日期字符串
 */
export const formatDateShort = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString("zh-CN", {
    month: "2-digit",
    day: "2-digit",
  });
};

/**
 * 格式化日期为完整格式（年-月-日）
 * @param dateString 日期字符串
 * @returns 格式化后的日期字符串
 */
export const formatDateFull = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
  });
};

/**
 * 格式化时间为简短格式（时:分）
 * @param dateString 日期字符串
 * @returns 格式化后的时间字符串
 */
export const formatTime = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleTimeString("zh-CN", {
    hour: "2-digit",
    minute: "2-digit",
  });
};

/**
 * 格式化时间为详细格式（时:分:秒）
 * @param dateString 日期字符串
 * @returns 格式化后的时间字符串
 */
export const formatTimeWithSeconds = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleTimeString("zh-CN", {
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
  });
};

/**
 * 判断日期是否为今天
 * @param dateString 日期字符串
 * @returns 是否为今天
 */
export const isToday = (dateString: string): boolean => {
  const date = new Date(dateString);
  const today = new Date();
  return date.toDateString() === today.toDateString();
};

/**
 * 判断日期是否为昨天
 * @param dateString 日期字符串
 * @returns 是否为昨天
 */
export const isYesterday = (dateString: string): boolean => {
  const date = new Date(dateString);
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  return date.toDateString() === yesterday.toDateString();
};

/**
 * 计算两个日期之间的天数差
 * @param dateString1 第一个日期
 * @param dateString2 第二个日期
 * @returns 天数差
 */
export const getDaysDiff = (dateString1: string, dateString2: string): number => {
  const date1 = new Date(dateString1);
  const date2 = new Date(dateString2);
  const diffTime = Math.abs(date2.getTime() - date1.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

/**
 * 获取当前时间戳
 * @returns 当前时间戳字符串
 */
export const getCurrentTimestamp = (): string => {
  return new Date().toISOString();
};

/**
 * 格式化为ISO字符串
 * @param date 日期对象或日期字符串
 * @returns ISO格式的日期字符串
 */
export const toISOString = (date: Date | string): string => {
  if (typeof date === 'string') {
    return new Date(date).toISOString();
  }
  return date.toISOString();
};