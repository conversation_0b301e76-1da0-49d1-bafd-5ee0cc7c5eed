/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-21
 * @Description  : 账号Mock API服务器
 * Copyright 2025 Bruce, All Rights Reserved.
 */

const jsonServer = require("json-server");
const path = require("path");
const server = jsonServer.create();
const router = jsonServer.router(path.join(__dirname, "db.json"));
const middlewares = jsonServer.defaults();

// 设置默认中间件（logger, static, cors等）
server.use(middlewares);

// 添加自定义路由
server.use(jsonServer.bodyParser);

// 添加自定义响应处理
server.use((req, res, next) => {
  // 添加延迟以模拟网络延迟
  setTimeout(next, 300);
});

// 使用路由器
server.use(router);

// 启动服务器
const PORT = 3002;
server.listen(PORT, () => {
  console.log(`账号Mock API服务器运行在 http://localhost:${PORT}`);
  console.log(`可用API端点:`);
  console.log(`  GET    /accounts`);
  console.log(`  GET    /accounts/:id`);
  console.log(`  POST   /accounts`);
  console.log(`  PUT    /accounts/:id`);
  console.log(`  DELETE /accounts/:id`);
  console.log(`  GET    /categories`);
  console.log(`  POST   /categories`);
  console.log(`  PUT    /categories/:id`);
  console.log(`  DELETE /categories/:id`);
});
