{"categories": [{"id": "c1", "name": "社交媒体", "color": "#1890ff", "icon": "icon-social", "created_at": "2025-07-15T08:00:00.000Z"}, {"id": "c2", "name": "电子邮件", "color": "#52c41a", "icon": "icon-mail", "created_at": "2025-07-15T08:10:00.000Z"}, {"id": "c3", "name": "金融服务", "color": "#faad14", "icon": "icon-bank", "created_at": "2025-07-15T08:20:00.000Z"}, {"id": "c4", "name": "工作相关", "color": "#722ed1", "icon": "icon-work", "created_at": "2025-07-15T08:30:00.000Z"}, {"id": "c5", "name": "购物网站", "color": "#eb2f96", "icon": "icon-shopping", "created_at": "2025-07-15T08:40:00.000Z"}], "accounts": [{"id": "a1", "platform": "微信", "username": "wxuser123", "display_name": "我的微信账号", "encrypted_password": "encrypted_password123", "email": "<EMAIL>", "phone": "***********", "website_url": "https://weixin.qq.com", "category_id": "c1", "notes": "主要社交账号", "custom_fields": {"recovery_email": "<EMAIL>"}, "created_at": "2025-07-15T09:00:00.000Z", "updated_at": "2025-07-15T09:00:00.000Z"}, {"id": "a2", "platform": "Gmail", "username": "<EMAIL>", "encrypted_password": "encrypted_gmailpass456", "email": "<EMAIL>", "category_id": "c2", "website_url": "https://gmail.com", "created_at": "2025-07-15T09:10:00.000Z", "updated_at": "2025-07-15T09:10:00.000Z"}, {"id": "a3", "platform": "支付宝", "username": "alipay_user", "encrypted_password": "encrypted_alipay789", "phone": "***********", "category_id": "c3", "website_url": "https://www.alipay.com", "notes": "主要支付账号", "created_at": "2025-07-15T09:20:00.000Z", "updated_at": "2025-07-15T09:20:00.000Z"}, {"id": "a4", "platform": "公司邮箱", "username": "<EMAIL>", "encrypted_password": "encrypted_work123", "email": "<EMAIL>", "category_id": "c4", "notes": "工作邮箱，每90天更换一次密码", "created_at": "2025-07-15T09:30:00.000Z", "updated_at": "2025-07-15T09:30:00.000Z"}, {"id": "a5", "platform": "淘宝", "username": "taobao_user", "encrypted_password": "encrypted_taobao456", "phone": "13700137000", "email": "<EMAIL>", "category_id": "c5", "website_url": "https://www.taobao.com", "notes": "主要购物账号", "created_at": "2025-07-15T09:40:00.000Z", "updated_at": "2025-07-15T09:40:00.000Z"}, {"id": "a6", "platform": "京东", "username": "j<PERSON><PERSON>", "encrypted_password": "encrypted_jd789", "phone": "13600136000", "category_id": "c5", "website_url": "https://www.jd.com", "created_at": "2025-07-15T09:50:00.000Z", "updated_at": "2025-07-15T09:50:00.000Z"}, {"id": "a7", "platform": "QQ", "username": "123456789", "encrypted_password": "encrypted_qq123", "email": "<EMAIL>", "category_id": "c1", "website_url": "https://im.qq.com", "created_at": "2025-07-15T10:00:00.000Z", "updated_at": "2025-07-15T10:00:00.000Z"}, {"id": "a8", "platform": "招商银行", "username": "cmb_user", "encrypted_password": "encrypted_cmb456", "phone": "13500135000", "category_id": "c3", "website_url": "https://www.cmbchina.com", "notes": "招商银行网银账号", "created_at": "2025-07-15T10:10:00.000Z", "updated_at": "2025-07-15T10:10:00.000Z"}]}