{"finance-records": [{"period": "2025-01-01 至 2025-01-31", "periodStart": "2025-01-01", "periodEnd": "2025-01-31", "salary": 8000, "otherIncome": 2000, "juan": 500, "wechat": 300, "huabei": 800, "spdbCreditCard": 1200, "commCreditCard": 900, "cmCreditCard": 1100, "jdBaitiao": 400, "jdJintiao": 600, "rent": 2500, "mortgage": 3000, "remark": "一月份财务记录", "cmBank": 15000, "icbcBank": 8000, "spdbBank": 5000, "cash": 800, "alipay": 1200, "wechatPay": 500, "updatedAt": "2025-07-20T12:38:27.637Z", "id": "1"}, {"period": "2024-12-01 至 2024-12-31", "periodStart": "2024-12-01", "periodEnd": "2024-12-31", "salary": 8000, "otherIncome": 1500, "juan": 600, "wechat": 250, "huabei": 900, "spdbCreditCard": 1500, "commCreditCard": 800, "cmCreditCard": 1000, "jdBaitiao": 350, "jdJintiao": 500, "rent": 2500, "mortgage": 3000, "remark": "十二月份财务记录", "cmBank": 12000, "icbcBank": 7500, "spdbBank": 4500, "cash": 600, "alipay": 1000, "wechatPay": 400, "updatedAt": "2025-07-20T12:33:24.581Z", "id": "2"}, {"id": "3", "period": "2024-11-01 至 2024-11-30", "periodStart": "2024-11-01", "periodEnd": "2024-11-30", "salary": 8000, "otherIncome": 3000, "juan": 400, "wechat": 200, "huabei": 700, "spdbCreditCard": 1000, "commCreditCard": 1200, "cmCreditCard": 900, "jdBaitiao": 300, "jdJintiao": 400, "rent": 2500, "mortgage": 3000, "cmBank": 18000, "icbcBank": 9000, "spdbBank": 6000, "cash": 1000, "alipay": 1500, "wechatPay": 600, "remark": "十一月份财务记录", "createdAt": "2024-11-15T10:30:00.000Z", "updatedAt": "2024-11-15T10:30:00.000Z"}], "periods": [{"id": "1", "period": "2025-01-01 至 2025-01-31", "periodStart": "2025-01-01", "periodEnd": "2025-01-31", "status": "active"}, {"id": "2", "period": "2024-12-01 至 2024-12-31", "periodStart": "2024-12-01", "periodEnd": "2024-12-31", "status": "completed"}, {"id": "3", "period": "2024-11-01 至 2024-11-30", "periodStart": "2024-11-01", "periodEnd": "2024-11-30", "status": "completed"}], "accounts": [{"id": "1", "platform": "GitHub", "username": "bruce_dev", "display_name": "Bruce开发者账号", "encrypted_password": "enc_password_1", "email": "<EMAIL>", "phone": "***********", "website_url": "https://github.com", "category_id": "1", "notes": "主要开发账号", "custom_fields": {"2FA": "已启用", "恢复邮箱": "<EMAIL>"}, "created_at": "2025-01-01T08:00:00.000Z", "updated_at": "2025-07-01T10:30:00.000Z"}, {"id": "2", "platform": "Gmail", "username": "bruce.work", "display_name": "工作邮箱", "encrypted_password": "enc_password_2", "email": "<EMAIL>", "phone": "", "website_url": "https://gmail.com", "category_id": "2", "notes": "工作邮箱，用于接收工作通知", "custom_fields": {"备用邮箱": "<EMAIL>"}, "created_at": "2025-01-02T09:15:00.000Z", "updated_at": "2025-06-15T14:20:00.000Z"}, {"id": "3", "platform": "微信", "username": "bruce_wx", "display_name": "个人微信", "encrypted_password": "enc_password_3", "email": "", "phone": "**********2", "website_url": "", "category_id": "3", "notes": "个人社交账号", "custom_fields": {}, "created_at": "2025-01-03T11:30:00.000Z", "updated_at": "2025-07-10T16:45:00.000Z"}, {"id": "4", "platform": "支付宝", "username": "***********", "display_name": "个人支付宝", "encrypted_password": "enc_password_4", "email": "<EMAIL>", "phone": "***********", "website_url": "https://www.alipay.com", "category_id": "4", "notes": "主要支付账号", "custom_fields": {"支付密码": "已设置"}, "created_at": "2025-01-04T13:45:00.000Z", "updated_at": "2025-07-05T09:10:00.000Z"}, {"id": "5", "platform": "淘宝", "username": "bruce_shop", "display_name": "购物账号", "encrypted_password": "enc_password_5", "email": "<EMAIL>", "phone": "***********", "website_url": "https://www.taobao.com", "category_id": "4", "notes": "主要购物账号", "custom_fields": {}, "created_at": "2025-01-05T15:20:00.000Z", "updated_at": "2025-06-20T11:30:00.000Z"}], "categories": [{"id": "1", "name": "开发工具", "color": "#2196F3", "icon": "code", "created_at": "2025-01-01T00:00:00.000Z"}, {"id": "2", "name": "邮箱账号", "color": "#4CAF50", "icon": "email", "created_at": "2025-01-01T00:00:00.000Z"}, {"id": "3", "name": "社交媒体", "color": "#FF9800", "icon": "social", "created_at": "2025-01-01T00:00:00.000Z"}, {"id": "4", "name": "支付账号", "color": "#E91E63", "icon": "payment", "created_at": "2025-01-01T00:00:00.000Z"}, {"id": "5", "name": "工作相关", "color": "#9C27B0", "icon": "work", "created_at": "2025-01-01T00:00:00.000Z"}]}