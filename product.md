<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-22 10:25:03
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-06-22 10:43:26
 * @FilePath     : /product.md
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-22 10:25:03
-->

# 本地账号管理系统 - 产品需求文档

## 项目背景

用户目前管理多个互联网平台的多个账号（如 Cursor、知乎、微信等），使用 Excel 记录账号密码、关联手机号、备注、网址等信息，但管理困难。用户不希望使用网络密码管理器（担心泄露风险），也不擅长使用 Excel。

## 产品定位

一个完全本地化的桌面账号管理工具，提供安全、简单、易用的账号密码管理功能。

## 核心需求

### 1. 安全性要求

- **完全离线**：数据仅存储在用户本地电脑，不联网
- **加密存储**：使用 AES 加密算法保护数据
- **主密码保护**：设置主密码访问系统
- **多层加密**：数据库整体加密 + 单条记录加密
- **自动锁定**：闲置一定时间自动锁定

### 2. 数据存储

- **存储位置**：用户本地 Documents 文件夹
- **存储格式**：SQLite 加密数据库文件
- **文件命名**：普通文件名，不暴露内容性质
- **备份功能**：支持一键备份到指定位置

### 3. 账号管理功能

#### 3.1 信息字段

- 平台名称
- 账号/用户名
- 密码
- 关联邮箱
- 关联手机号
- 网站 URL
- 分类标签
- 备注信息
- 创建时间
- 最后修改时间

#### 3.2 分类管理

- 按平台分类（社交、工作、购物、娱乐等）
- 支持自定义分类
- 同一平台支持多个账号

### 4. 录入功能

#### 4.1 手动录入

- 简洁的表单界面
- 平台下拉选择 + 自定义输入
- 必填项验证
- 格式验证（邮箱、手机号）
- 重复账号检测

#### 4.2 批量导入

- 支持从 Excel 文件导入
- 智能识别列名映射
- 拖拽导入功能
- 导入数据预览和确认

#### 4.3 辅助功能

- 密码强度生成器
- 常用平台模板
- 表单信息记忆
- 一键保存功能

### 5. 查询和管理

#### 5.1 搜索功能

- 按平台名称搜索
- 按账号名搜索
- 按分类筛选
- 快速定位功能

#### 5.2 显示和操作

- 列表形式展示账号
- 密码默认隐藏，点击显示
- 一键复制功能（账号/密码）
- 编辑和删除功能

### 6. 用户界面要求

- **简单直观**：比 Excel 更易用的图形界面
- **操作便捷**：最少点击完成常用操作
- **视觉清晰**：信息层次分明，易于查看
- **响应迅速**：本地操作，无网络延迟

### 7. 技术要求

- 跨平台支持（Windows/Mac/Linux）
- 本地桌面应用程序
- 无需网络连接
- 启动速度快
- 占用资源少

## 用户使用流程

### 首次使用

1. 安装并启动应用
2. 设置主密码
3. 从 Excel 导入现有数据
4. 检查和完善导入的信息

### 日常使用

1. 输入主密码解锁
2. 搜索或浏览需要的账号
3. 复制账号密码进行登录
4. 添加新账号信息
5. 定期备份数据

## 成功标准

- 用户能够在 30 秒内找到并复制所需账号密码
- 数据迁移成功率达到 95%以上
- 用户操作错误率低于 5%
- 系统启动时间少于 3 秒
- 数据安全性达到银行级别标准
